[tool.poetry]
name = "llm-app"
version = "0.1.0"
description = "LLM assistant that analyzes diffs against custom policies"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [
    { include = "src" },
    # This is an important line
    { include = "protos_generated", from = "../all-protos/" }
]

[tool.poetry.dependencies]
python = "^3.9"
grpcio = "^1.71.0"
grpcio-tools = "^1.71.0"
litellm = "^1.63.8"
python-dotenv = "^1.0.0"
pydantic = "^2.0.0"
protobuf = "^5.26.1"
infisicalsdk = "^1.0.6"
sentry-sdk = "^2.24.0"
aiofiles = "^24.1.0"
opik = "^1.7.34"
boto3 = "^1.35.0"

[tool.poetry.group.dev.dependencies]
black = "^23.3.0"
isort = "^5.12.0"
mypy = "^1.3.0"
pytest = "^7.3.1"
pytest-asyncio = "^0.21.0"
pre-commit = "^4.2.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py38']

[tool.isort]
profile = "black"
multi_line_output = 3


[tool.poetry.scripts]
llm-server = "src.api.grpc.server:main"
llm-client = "src.api.grpc.client:main" 
test-explanatory-comments = "src.core.llm_analyzers.ExplanatoryCommentAnalyzer:main"