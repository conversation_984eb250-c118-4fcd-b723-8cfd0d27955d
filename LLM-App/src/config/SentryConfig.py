import sentry_sdk
from sentry_sdk.integrations.grpc import GRPCIntegration
from importlib.metadata import version

def configure_monitoring(deployment_environment: str):
    sentry_sdk.init(
        dsn="https://<EMAIL>/4509017070698496",
        # Add data like request headers and IP for users,
        # see https://docs.sentry.io/platforms/python/data-management/data-collected/ for more info
        send_default_pii=True,
        attach_stacktrace=True,
        environment=deployment_environment,
        traces_sample_rate=1.0,
        release=version("llm-app"),
        integrations=[GRPCIntegration()]
    )
    sentry_sdk.set_tag("service_name", "LLM-app")