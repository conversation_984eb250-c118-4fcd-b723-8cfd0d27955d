"""Configuration settings for the Code Review Assistant."""

import os
from typing import Optional
from dataclasses import dataclass, field
from dotenv import load_dotenv
from infisical_sdk import InfisicalSDKClient
import logging

from src.config.LoggingConfig import configure_logging

load_dotenv(verbose=True, override=True)
configure_logging()
logger = logging.getLogger(__name__)



ENVIRONMENT = os.getenv("ENVIRONMENT")

# --- Initialize the Infisical client
client = InfisicalSDKClient(host="https://app.infisical.com")
client.auth.universal_auth.login(
    client_id=os.getenv("INFISICAL_CLIENT_ID"), 
    client_secret=os.getenv("INFISICAL_CLIENT_SECRET")
)
INFISICAL_PROJECT_ID = "308c3ff9-e22e-4d84-bfce-7c94eda69299"


def get_secret(secret_name: str, default_value: str = "") -> str:
    try:
        if client:
            return client.secrets.get_secret_by_name(
                secret_name=secret_name,
                project_id=INFISICAL_PROJECT_ID,
                environment_slug=ENVIRONMENT,
                secret_path="/",
            ).secretValue
    except Exception as e:
        logger.warning(f"Failed to fetch {secret_name} from secrets: {e}")
    return os.getenv(secret_name, default_value)


DEPLOYMENT_ENVIRONMENT = get_secret("DEPLOYMENT_ENVIRONMENT", "unknown")


# --- Different Setting Configs
@dataclass
class LLMSettings:
    """LLM-related settings."""
    
    model: str = field(default=get_secret("LLM_MODEL", "gpt-4-turbo-preview"))
    api_key: str = field(default=get_secret("LLM_API_KEY", ""))
    api_base: Optional[str] = field(default=get_secret("LLM_API_BASE"))
    temperature: float = field(default=float(get_secret("LLM_TEMPERATURE", "0.2")))
    max_tokens: int = field(default=int(os.getenv("LLM_MAX_TOKENS", 6400)))

@dataclass
class LLMOpsSettings:
    """LLM-related settings."""
    api_key: str = field(default=get_secret("OPIK_API_KEY", ""))
    workspace: str = field(default="shivansh-jagga")
    project_name: str = field(default=DEPLOYMENT_ENVIRONMENT)
    

@dataclass
class ServerSettings:
    """Server-related settings."""
    
    host: str = field(default=os.getenv("SERVER_HOST", "localhost"))
    port: int = field(default=int(os.getenv("SERVER_PORT", "8100")))

@dataclass
class AWSSettings:
    """AWS-related settings."""
    
    access_key_id: str = field(default=get_secret("AWS_ACCESS_KEY_ID", ""))
    secret_access_key: str = field(default=get_secret("AWS_SECRET_ACCESS_KEY", ""))
    region: str = field(default=get_secret("AWS_REGION", "us-east-1"))
    s3_bucket_name: str = field(default=get_secret("AWS_S3_BUCKET_NAME", ""))


# --- The main App Config
@dataclass
class AppSettings:
    """Application settings."""
    
    debug: bool = field(default=os.getenv("DEBUG", "false").lower() in ("true", "1", "yes"))
    log_level: str = field(default=os.getenv("LOG_LEVEL", "INFO"))
    llm: LLMSettings = field(default_factory=LLMSettings)
    llm_ops: LLMOpsSettings = field(default_factory=LLMOpsSettings)
    server: ServerSettings = field(default_factory=ServerSettings)
    aws: AWSSettings = field(default_factory=AWSSettings)


def init_monitoring():
    from src.config.SentryConfig import configure_monitoring
    configure_monitoring(DEPLOYMENT_ENVIRONMENT)


# Create a global settings instance
app_settings = AppSettings()
init_monitoring()