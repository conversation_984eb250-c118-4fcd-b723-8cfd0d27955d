"""Service for AWS role assumption with MFA."""
import boto3
import logging
from typing import Dict, Optional, Tuple

logger = logging.getLogger(__name__)


class AssumeRoleService:
    """Service for assuming AWS roles with MFA support."""
    
    def __init__(self, profile_name: Optional[str] = None):
        """Initialize with optional AWS profile name."""
        self.base_session = boto3.Session(profile_name=profile_name)
        self.sts_client = self.base_session.client('sts')
        self.iam_client = self.base_session.client('iam')
    
    def get_mfa_device(self, username: str) -> Optional[str]:
        """Get the MFA device ARN for a user.
        
        Args:
            username: IAM username
            
        Returns:
            MFA device ARN if found, None otherwise
        """
        try:
            response = self.iam_client.list_mfa_devices(UserName=username)
            devices = response.get('MFADevices', [])
            
            if devices:
                # Return ARN of the first MFA device
                return devices[0]['SerialNumber']
            
            logger.warning(f"No MFA devices found for user {username}")
            return None
        except Exception as e:
            logger.error(f"Failed to get <PERSON>FA device for user {username}: {e}")
            return None
    
    def assume_role(self, 
                    role_arn: str, 
                    session_name: str, 
                    username: Optional[str] = None,
                    mfa_token: Optional[str] = None,
                    mfa_device_arn: Optional[str] = None) -> Dict:
        """Assume an AWS role with optional MFA.
        
        Args:
            role_arn: ARN of the role to assume
            session_name: Identifier for the assumed role session
            username: IAM username for MFA device lookup (if mfa_device_arn not provided)
            mfa_token: MFA token code (required if username or mfa_device_arn provided)
            mfa_device_arn: MFA device ARN (will be looked up from username if not provided)
            
        Returns:
            Dict with temporary credentials
        """
        try:
            # Determine if we need MFA
            if mfa_token and (username or mfa_device_arn):
                # Get MFA device ARN if not provided
                if not mfa_device_arn and username:
                    mfa_device_arn = self.get_mfa_device(username)
                    if not mfa_device_arn:
                        raise ValueError(f"Could not find MFA device for user {username}")
                
                # Assume role with MFA
                response = self.sts_client.assume_role(
                    RoleArn=role_arn,
                    RoleSessionName=session_name,
                    SerialNumber=mfa_device_arn,
                    TokenCode=mfa_token
                )
            else:
                # Assume role without MFA
                response = self.sts_client.assume_role(
                    RoleArn=role_arn,
                    RoleSessionName=session_name
                )
            
            # Return credentials
            return response['Credentials']
            
        except Exception as e:
            logger.error(f"Failed to assume role {role_arn}: {e}")
            raise
    
    def create_assumed_session(self, 
                              role_arn: str, 
                              session_name: str,
                              username: Optional[str] = None,
                              mfa_token: Optional[str] = None,
                              mfa_device_arn: Optional[str] = None) -> boto3.Session:
        """Create a boto3 session with assumed role credentials.
        
        Args:
            role_arn: ARN of the role to assume
            session_name: Identifier for the assumed role session
            username: IAM username for MFA device lookup (if mfa_device_arn not provided)
            mfa_token: MFA token code (required if username or mfa_device_arn provided)
            mfa_device_arn: MFA device ARN (will be looked up from username if not provided)
            
        Returns:
            boto3.Session configured with temporary credentials
        """
        creds = self.assume_role(
            role_arn=role_arn,
            session_name=session_name,
            username=username,
            mfa_token=mfa_token,
            mfa_device_arn=mfa_device_arn
        )
        
        return boto3.Session(
            aws_access_key_id=creds['AccessKeyId'],
            aws_secret_access_key=creds['SecretAccessKey'],
            aws_session_token=creds['SessionToken']
        )