"""
S3 Service for managing diff files in AWS S3.
"""
import boto3
import logging
from typing import Optional

from src.config.settings import app_settings

logger = logging.getLogger(__name__)


class S3Service:
    """Service for interacting with AWS S3 to store and retrieve diff files."""
    
    def __init__(self):
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=app_settings.aws.access_key_id,
            aws_secret_access_key=app_settings.aws.secret_access_key,
            region_name=app_settings.aws.region
        )
        self.bucket_name = "tanagram-pr-diffs"
        self.diff_files_prefix = "runway_diffs/"
    
    def get_diff_file(self, filename: str) -> Optional[str]:
        """
        Get diff file content from S3.
        
        Args:
            filename: Name of the diff file to retrieve
            
        Returns:
            File content as string, or None if retrieval fails
        """
        try:
            key = f"{self.diff_files_prefix}{filename}"
            response = self.s3_client.get_object(Bucket=self.bucket_name, Key=key)
            content = response['Body'].read().decode('utf-8')
            logger.info(f"Successfully retrieved {filename} from S3")
            return content
        except Exception as e:
            logger.error(f"Failed to retrieve {filename} from S3: {e}")
            return None
    
    def upload_diff_file(self, filename: str, content: str) -> bool:
        """
        Upload diff file to S3.
        
        Args:
            filename: Name of the file to upload
            content: File content as string
            
        Returns:
            True if upload successful, False otherwise
        """
        try:
            key = f"{self.diff_files_prefix}{filename}"
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=key,
                Body=content.encode('utf-8')
            )
            logger.info(f"Successfully uploaded {filename} to S3")
            return True
        except Exception as e:
            logger.error(f"Failed to upload {filename} to S3: {e}")
            return False


# Create singleton instance
s3_service = S3Service()
