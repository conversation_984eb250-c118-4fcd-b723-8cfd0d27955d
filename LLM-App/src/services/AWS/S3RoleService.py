"""S3 Service with role assumption capabilities."""
import boto3
import logging
from typing import Dict, Optional, Any

from src.services.AWS.S3Service import S3Service
from src.services.AWS.AssumeRoleService import AssumeRoleService
from src.config.settings import app_settings

logger = logging.getLogger(__name__)


class S3RoleService(S3Service):
    """Service for interacting with AWS S3 using an assumed role."""
    
    def __init__(self, 
                 role_arn: Optional[str] = None,
                 username: Optional[str] = None,
                 mfa_token: Optional[str] = None,
                 mfa_device_arn: Optional[str] = None,
                 profile_name: Optional[str] = None,
                 session_name: str = "s3-role-session",
                 bucket_name: Optional[str] = None):
        """Initialize with role assumption parameters.
        
        Args:
            role_arn: ARN of the role to assume (required)
            username: IAM username for MFA device lookup (if mfa_device_arn not provided)
            mfa_token: MFA token code for role assumption
            mfa_device_arn: MFA device ARN (will be looked up from username if not provided)
            profile_name: AWS profile name for base credentials
            session_name: Identifier for the assumed role session
            bucket_name: S3 bucket name (uses app_settings if not provided)
        """
        # Don't call parent __init__ yet as we need our own S3 client
        
        # Store parameters
        self.role_arn = role_arn
        self.username = username
        self.mfa_token = mfa_token
        self.mfa_device_arn = mfa_device_arn
        self.profile_name = profile_name
        self.session_name = session_name
        
        # Initialize S3 client with assumed role if provided
        if role_arn:
            self._assume_role_and_create_client()
        else:
            # Fall back to regular S3 client if no role ARN provided
            super().__init__()
    
    def _assume_role_and_create_client(self) -> None:
        """Assume role and create S3 client with temporary credentials."""
        try:
            assume_role_service = AssumeRoleService(profile_name=self.profile_name)
            
            # Create session with assumed role
            assumed_session = assume_role_service.create_assumed_session(
                role_arn=self.role_arn,
                session_name=self.session_name,
                username=self.username,
                mfa_token=self.mfa_token,
                mfa_device_arn=self.mfa_device_arn
            )
            
            # Create S3 client from assumed role session
            self.s3_client = assumed_session.client('s3')
            logger.info(f"Successfully assumed role {self.role_arn} for S3 access")
            
        except Exception as e:
            logger.error(f"Failed to assume role for S3 access: {e}")
            # Fall back to regular credentials if role assumption fails
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=app_settings.aws.access_key_id,
                aws_secret_access_key=app_settings.aws.secret_access_key,
                region_name=app_settings.aws.region
            )
            logger.warning("Falling back to default credentials for S3 access")
    

# Example usage
def create_s3_service(use_role: bool = False, 
                      role_arn: Optional[str] = None,
                      username: Optional[str] = None,
                      mfa_token: Optional[str] = None) -> Any:
    """Create either a standard S3Service or an S3RoleService with assumed role.
    
    Args:
        use_role: Whether to use role assumption
        role_arn: ARN of the role to assume
        username: IAM username for MFA device lookup
        mfa_token: MFA token code for role assumption
        
    Returns:
        S3Service or S3RoleService instance
    """
    if use_role and role_arn and mfa_token:
        return S3RoleService(
            role_arn=role_arn,
            username=username,
            mfa_token=mfa_token
        )
    else:
        from src.services.AWS.S3Service import s3_service
        return s3_service