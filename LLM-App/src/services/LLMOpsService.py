import os
import opik
import logging

from src.config.settings import app_settings
logger = logging.getLogger(__name__)


class LLMOpsService:
    
    def __init__(self):
        os.environ["OPIK_PROJECT_NAME"] = app_settings.llm_ops.project_name
        opik.configure(
            api_key=app_settings.llm_ops.api_key,
            workspace=app_settings.llm_ops.workspace,
        )
        # had to redefine for evals
        self.client = opik.Opik(
            api_key=app_settings.llm_ops.api_key,
            workspace=app_settings.llm_ops.workspace,
            project_name=app_settings.llm_ops.project_name,
        )

    # --- Logging related Functions

    def track(self, name: str, capture_input: bool = True, *args, **kwargs):
        try:
            name, capture_input = name, capture_input
            return opik.track(name=name, capture_input=capture_input, *args, **kwargs)
        except Exception as e:
            logger.exception(f"Error in opik.track: {str(e)}")
            raise e
    
    def get_current_trace_id(self):
        """
        A trace, is the top-level prompt-chain grouping,
        with multiple spans (prompts) within it.
        """
        try:
            o = opik.opik_context.get_current_span_data()
            return o.trace_id
        except Exception as e:
            logger.exception(f"Error in opik.get_current_trace_id: {str(e)}")
            raise e
        
        
    def log_with_trace_id(self, message: str):
        trace_id = self.get_current_trace_id()
        logger.info(f"[LLM-Trace: {trace_id}] {message}")
                        
    # --- Evaluation related Functions
    def get_or_create_dataset(self, name: str, **kwargs):
        try:
            return self.client.get_or_create_dataset(name=name, **kwargs)
        except Exception as e:
            logger.exception(f"Error in opik.get_or_create_dataset: {str(e)}")
            raise e
    

ops_service = LLMOpsService()