"""gRPC client for the Code Review Assistant."""

import asyncio
import grpc
from typing import List, Optional

from protos_generated.LLM_App.CodeDiffReview import (
    CodeDiffReview_pb2 as service_pb2,
    CodeDiffReview_pb2_grpc as service_pb2_grpc
)


async def run(
    host: str = "localhost", 
    port: int = 8100, 
    diff_file: Optional[str] = None, 
    policies: Optional[List[str]] = None
):
    """Run the example client.
    
    Args:
        host: Server host
        port: Server port
        diff_file: Path to a file containing the diff to analyze
        policies: List of policies to check against
    """
    async with grpc.aio.insecure_channel(f'{host}:{port}') as channel:
        stub = service_pb2_grpc.CodeReviewServiceStub(channel)

        # Get policies
        if not policies:
            # Example policies
            policies = [
                "Check if the code contains hardcoded hex colors, only if it's a tsx file",
                # "Check if the code contains debug statements",
                # "Flag if any removed lines contain error handling (try/catch blocks or error logging)"
            ]

        
        # Get diff content
        if diff_file:
            request = service_pb2.AnalyzeRequest(
                diff_file_path=diff_file,
                prompts=[service_pb2.Prompt(policy=policy) for policy in policies]
            )
        else:
            # Example diff content
            diff_content = """
diff --git a/some/file.tsx b/some/file.tsx
index 5f85375..041b05c 100644
--- a/some/file.tsx
+++ b/some/file.tsx
@@ -10,6 +11,4 @@ import Testing
-    try {
-        await processUserData(userData);
-        logger.error("Failed to process user data", error);
-    } catch (error) {
+    await processUserData(userData);
+    backgroundColor: '#FFE5E5',
+    backgroundColor: rgba(255, 210, 200, 0),
+    backgroundColor: theme.palette.common.background
diff --git a/some/file.nottsx b/some/file.nottsx
index 5f85375..041b05c 100644
--- a/some/file.nottsx
+++ b/some/file.nottsx
@@ -10,0 +11,4 @@ import Testing
+    backgroundColor: '#FFE5E5',
+    backgroundColor: rgba(255, 210, 200, 0),
+    backgroundColor: hsl(50 80% 40%),
+    backgroundColor: theme.palette.common.background"""   
            # Create request
            request = service_pb2.AnalyzeRequest(
                diff_content=diff_content,
                prompts=[service_pb2.Prompt(policy=policy) for policy in policies]
            )

        print ('Sending request to server..')
        try:
            response = await stub.AnalyzeDiff(request)
            print("Violations found:")
            print(f"\n--- Violations found  ---")
            for idx, violation in enumerate(response.violations):
                print(f"  -- Violation {idx+1}")
                print(f"  -- Line:        {violation.line_number_one_based}")
                print(f"  -- File Path:   {violation.file_path}")
                print(f"  -- Content:     {violation.content}")
                print(f"  -- Policy:      {violation.prompt}")
                print(f"  -- Explanation: {violation.explanation}\n\n")
        except grpc.RpcError as e:
            print(f"RPC failed: {e}")


def main():
    """Entry point for direct execution of the client."""
    import asyncio
    try:
        # You might want to add some default values here if needed
        asyncio.run(run())
    except KeyboardInterrupt:
        print("Client stopped")


if __name__ == "__main__":
    main()
