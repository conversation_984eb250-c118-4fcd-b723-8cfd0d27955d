from typing import List, Optional
import asyncio
import logging
import pydantic
import src.services.LLMCallService

from src.services.LLMOpsService import ops_service

logger = logging.getLogger(__name__)

class ExplanatoryComment(pydantic.BaseModel):
  comment_id: int
  content: str

class ExplanatoryCommentAnalyzer:
  def __init__(self):
    self.llm_service = src.services.LLMCallService.LLMCallService()
    self.system_prompt = {
      "role": "system", 
      "content": [
        {
          "type": "text",
          "text": (
            "A code review assistant has identified some changes that potentially violate a specific coding policy."
            " The code author has provided comments justifying their intentional violation of the policy."
            " Your job is to determine which of their comments (if any) explain *why* they're intentionally violating the policy."
          )
        },
        {
            "type": "text",
            "text": (
              "Each explanatory comment starts with a number, followed by the contents of the comment."
              "If a comment provides a reasonable explanation for why the code author is intentionally violating the policy, then return <PERSON><PERSON><PERSON> the number of that comment."
              "If a comment does not provide a reasonable explanation for why the code author is intentionally violating the policy, DO NOT return that comment's number."
              "IMPORTANT: ONLY RETURN COMMENT NUMBERS. One number per line."
              "If no comment provides a reasonable explanation, return an empty response."
              "Think about what the policy implies, and consider contextually-relevant variants of the policy you're given."
            ),
            "cache_control": {"type": "ephemeral"},
        },
      ]
    }
  
  @ops_service.track(name="explanatory_comment_analyzer")  
  async def find_explanatory_comments_with_llm(self, policy: str, comments: List[ExplanatoryComment]) -> List[int]:
    policies_text = "\n".join([f"{c.comment_id}: {c.content}" for c in comments])
    messages = [
      { **self.system_prompt },
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": (
              f"Policy: {policy}\n"
              f"Explanatory comments:\n"
              f"{policies_text}"
            ),
            "cache_control": { "type": "ephemeral" }
          }
        ]
      }
    ]

    logger.info(f"LLM input: {messages[1]["content"][0]["text"]}")
    response = await self.llm_service.generate_completion(messages)
    logger.info("LLM response:\n%s\n", response)
    llm_response = response
    return self._parse_response(llm_response)
  
  def _parse_response(self, response: Optional[str]) -> List[int]:
    if response is None:
      return []
    # We've asked the LLM to respond with only numbers (corresponding to comment IDs), one per line.
    # This parsing is pretty dumb and doesn't handle other formats well.
    results = []
    for line in response.split("\n"):
      try:
        line_as_int = int(line)
        results.append(line_as_int)
      except ValueError:
        # Sometimes the LLM doesn't give us a number back :/
        pass
    return results


def main():
  policy = "When calling find[One]By on a TaskArtifact, User, Asset, or Dataset, the criteria should explicitly say deleted: false"
  # If enabled, this comment should be returned.
  # comments = [
  #   ExplanatoryComment(**{"comment_id": 1, "content": "Always update, even if deleted, to allow for other systems to consume."}),
  # ]

  # If enabled, this comment should not be returned.
  comments = [
    ExplanatoryComment(**{"comment_id": 2, "content": "Handle task artifacts."}),
  ]

  # If enabled, this comment should be returned.
  # comments = [
  #   ExplanatoryComment(**{"comment_id": 3, "content": "Handle task artifacts — no deleted: false because we need to at least attempt to quarantine EVERYTHING. Better it fails during retrieval than we miss something in the 2 week deletion window."})
  # ]
  analyzer = ExplanatoryCommentAnalyzer()
  result = asyncio.run(analyzer.find_explanatory_comments_with_llm(policy, comments))
  print(f"result={result}")

if __name__ == "__main__":
    main()