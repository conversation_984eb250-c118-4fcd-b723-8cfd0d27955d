"""
Dataset for Policy 2: Database Migration - Foreign Keys
"""
from textwrap import dedent


class ForeignKeysDataset:
    """Dataset for foreign keys policy evaluation"""
    
    @property
    def policy_text(self) -> str:
        return dedent("""
            POLICY: Database Migration - Foreign Keys

            VIOLATION PATTERN:
            - Operations that ALTER a table TO ADD a foreign key constraint
            - Operations that CREATE a new table and add foreign key/s constraint
            - Parameter consisting of "foreignKeys"
            
            Examples of violations:
            - ALTER TABLE "existing_table" ADD CONSTRAINT "fk_name" FOREIGN KEY ("column_name") REFERENCES "target_table" ("target_column");
            - queryRunner.createForeignKey("existing_table", new TableForeignKey({...}));
            - await queryRunner.query(`ALTER TABLE "existing_table" ADD FOREIGN KEY ("column_name") REFERENCES "target_table" ("target_column")`);

            NON-VIOLATION PATTERN:
            - Dropping foreign keys
        """)
    
    @property
    def diff_expectations(self) -> dict:
        return {
            'runway_pr4037_diff.txt': dedent("""
                {
                  "violations": [
                    {
                      "is_violation": true,
                      "line_number_one_based": 71,
                      "content": "    await queryRunner.createForeignKey(",
                      "prompt": "Policy 2: Database Migration - Creating Foreign Keys",
                      "explanation": "VIOLATION: YES. Creating a foreign key constraint on the 'datasets' table using queryRunner.createForeignKey() method.",
                      "file_path": "migrations/1741376261990-AddAssetReference.ts"
                    },
                    {
                      "is_violation": true,
                      "line_number_one_based": 91,
                      "content": "    await queryRunner.createForeignKey(",
                      "prompt": "Policy 2: Database Migration - Creating Foreign Keys",
                      "explanation": "VIOLATION: YES. Adding a foreign key constraint to the 'task_artifacts' table with queryRunner.createForeignKey().",
                      "file_path": "migrations/1741376261990-AddAssetReference.ts"
                    }
                  ]
                }
            """),

            'runway_pr3382_diff.txt': dedent("""
                {
                  "violations": [
                    {
                      "is_violation": true,
                      "line_number_one_based": 16,
                      "content": "                CONSTRAINT \"fk_user_suspension_records_users\" ",
                      "prompt": "Policy 2: Database Migration - Creating Foreign Keys",
                      "explanation": "VIOLATION: YES. Adds a new foreign key constraint when creating the 'user_suspension_records' table.",
                      "file_path": "migrations/1730305255903-AddUserSuspensionRecord.ts"
                    }
                  ]
                }
            """),

            'runway_pr2649_diff.txt': dedent("""
                {
                  "violations": [
                    {
                      "is_violation": true,
                      "line_number_one_based": 7,
                      "content": "await queryRunner.createForeignKey(",
                      "prompt": "Policy 2: Database Migration - Creating Foreign Keys",
                      "explanation": "VIOLATION: YES. Uses queryRunner.createForeignKey to add a new foreign key constraint to the 'generation_pairs' table.",
                      "file_path": "migrations/1718734535900-AddForeignKeyBatchIdToGenerationPair.ts"
                    }
                  ]
                }
            """),

            'runway_pr2625_diff.txt': dedent("""
                {
                  "violations": [
                    {
                      "is_violation": true,
                      "line_number_one_based": 53,
                      "content": "foreignKeys: [",
                      "prompt": "Policy 2: Database Migration - Creating Foreign Keys",
                      "explanation": "VIOLATION: YES. Creates a new table 'generation_reviews' with foreign key constraints using the 'foreignKeys' parameter.",
                      "file_path": "migrations/1718224493166-AddGenerationReviewsTable.ts"
                    }
                  ]
                }
            """),

            'runway_pr1978_diff.txt': dedent("""
                {
                  "violations": [
                    {
                      "is_violation": true,
                      "line_number_one_based": 62,
                      "content": "await queryRunner.createForeignKey(",
                      "prompt": "Policy 2: Database Migration - Creating Foreign Keys",
                      "explanation": "VIOLATION: YES. Creates a new foreign key constraint on 'generated_audio_transcripts' table referencing 'datasets' table.",
                      "file_path": "migrations/1698091259275-AddGenerativeAudioTranscriptSearch.ts"
                    },
                    {
                      "is_violation": true,
                      "line_number_one_based": 72,
                      "content": "await queryRunner.createForeignKey(",
                      "prompt": "Policy 2: Database Migration - Creating Foreign Keys",
                      "explanation": "VIOLATION: YES. Creates a new foreign key constraint on 'generated_audio_transcripts' table referencing 'users' table.",
                      "file_path": "migrations/1698091259275-AddGenerativeAudioTranscriptSearch.ts"
                    }
                  ]
                }
            """),
        }
