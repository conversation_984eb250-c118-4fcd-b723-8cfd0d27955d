"""
Dataset for Policy 3: TaskArtifact, CustomPreset, and Dataset Query Requirements
"""
from textwrap import dedent


class QueryRequirementsDataset:
    """Dataset for query requirements policy evaluation"""
    
    @property
    def policy_text(self) -> str:
        return dedent("""
            POLICY: TaskArtifact, CustomPreset, and Dataset Query Requirements

            A code snippet is a violation if and ONLY if ALL THREE conditions are met:
            1. The exact string "TaskArtifact" OR the exact string "Dataset" OR the exact string "CustomPreset" appears as the model name
            2. The query includes "userId:" as a parameter 
            3. The query does NOT include "privateInTeam:" anywhere

            DO NOT report violations based on partial matches or pattern recognition.

            FALSE POSITIVE EXAMPLES (NOT VIOLATIONS):
            - CustomPreset.existsBy({ userId: team.id }) - NOT a violation because model is not TaskArtifact or Dataset or CustomPreset
            - TaskArtifact.find({ name: "test" }) - NOT a violation because userId is not in the query
            - Dataset.find({ userId: id, privateInTeam: false }) - NOT a violation because privateInTeam IS included
            - TaskArtifact.find({ where: { userId: USER_ID, privateInTeam: true } }) - NOT a violation
            - TaskArtifact.find({ where: { userId: USER_ID, deleted: true } }) - THIS IS A VIOLATION (meets all conditions)

            IMPORTANT: For each potential violation, explicitly check all three conditions before reporting.
        """)
    
    @property
    def diff_expectations(self) -> dict:
        return {
            'runway_pr3793_diff.txt': dedent("""
                {
                  "violations": [
                    {
                      "is_violation": true,
                      "line_number_one_based": 39,
                      "content": "    const customPreset = await CustomPreset.getOneBy({",
                      "prompt": "Policy 3: TaskArtifact, CustomPreset, and Dataset Query Requirements",
                      "explanation": "VIOLATION: YES. CustomPreset model is queried with userId parameter, but 'privateInTeam' is not included in the query.",
                      "file_path": "lib/athena/prompt-modifier.ts"
                    }
                  ]
                }
            """),

            'runway_pr3848_diff.txt': dedent("""
                {
                  "violations": []
                }
            """),

            'runway_pr2093_diff.txt': dedent("""
                {
                  "violations": [
                    {
                      "is_violation": true,
                      "line_number_one_based": 57,
                      "content": "      userId: USER_ID,",
                      "prompt": "Policy 3: TaskArtifact, CustomPreset, and Dataset Query Requirements",
                      "explanation": "VIOLATION: YES. TaskArtifact query includes 'userId' but does not include 'privateInTeam', violating the policy requirements.",
                      "file_path": "SCRIPTS/undeleted_preview_cleanup.ts"
                    }
                  ]
                }
            """),

            'runway_pr403_diff.txt': dedent("""
                {
                  "violations": []
                }
            """),

            'runway_pr2147_diff.txt': dedent("""
                {
                "violations": []
                }
            """),

            'runway_pr3943_diff.txt': dedent("""
                {
                  "violations": []
                }
            """),

            'runway_pr2225_diff.txt': dedent("""
                {
                "violations": []
                }
            """),
        }
