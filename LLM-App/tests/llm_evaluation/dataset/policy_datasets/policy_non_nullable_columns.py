"""
Dataset for Policy 1: Database Migration - Non-nullable Columns
"""
from textwrap import dedent


class NonNullableColumnsDataset:
    """Dataset for non-nullable columns policy evaluation"""
    
    @property
    def policy_text(self) -> str:
        return dedent("""
            POLICY: Database Migration - Non-nullable Columns on existing tables

            VIOLATION PATTERN:
            - Operations that add non-nullable columns without defaults to PREVIOUSLY EXISTING tables
            - Must meet ALL these conditions:
            1. Modifies a table that existed BEFORE the current migration
            2. Adds one or more new columns
            3. Makes those columns non-nullable (NOT NULL)
            4. Does not provide default values

            NON-VIOLATION PATTERN:
            - Adding non-nullable columns to tables created IN THE SAME MIGRATION
            - CREATE TABLE statements
            - DROP COLUMN statements
            - ADD COLUMN with nullable columns
            - ADD COLUMN with NOT NULL AND DEFAULT

            REASON: This policy prevents migration failures that occur when adding non-nullable columns without defaults to tables that contain existing data. Tables created in the same migration file are guaranteed to be empty when columns are added to them, so they're exempt from this policy.

            DETECTION LOGIC:
            1. If a CREATE TABLE and ALTER TABLE for the same table appear in the same migration file, operations on that table are exempt
            2. If only ALTER TABLE appears (indicating the table existed before), then check for violations
            3. Look specifically for the sequence of: ALTER TABLE → existing_table → ADD COLUMN → column_name → NOT NULL without DEFAULT
            4. Look for the sequence: queryRunner.addColumn → isNullable → false
        """)
    
    @property
    def diff_expectations(self) -> dict:
        return {
            'runway_pr260_diff.txt': dedent("""
                {
                    "violations": [
                        {
                        "is_violation": true,
                        "line_number_one_based": 7,
                        "content": " ALTER TABLE \"endpoints\" ADD COLUMN \"token\" varchar NOT NULL",
                        "prompt": "Policy 1: Database Migration - Non-nullable Columns on existing tables",
                        "explanation": "VIOLATION: YES. Adds a non-nullable column 'token' to existing 'endpoints' table without providing a DEFAULT value, which will fail if table contains data.",
                        "file_path": "migrations/1585941023975-AddSecurityColumnsToEndpointsTable.ts"
                        },
                        {
                            "is_violation": true,
                            "line_number_one_based": 14,
                            "content": " ADD COLUMN \"count\" integer NOT NULL,",
                            "prompt": "POLICY: Database Migration - Non-nullable Columns on existing tables",
                            "explanation": "VIOLATION: YES. Adding non-nullable column 'count' to existing table 'endpoint_requests' without providing a default value, which will fail if table contains data.",
                            "file_path": "migrations/1585317800897-EndpointRequestEdits.ts"
                        }
                    ]
                }
            """),
            
            'runway_pr299_diff.txt': dedent("""
                {
                  "violations": [
                    {
                      "is_violation": true,
                      "line_number_one_based": 7,
                      "content": "      `ALTER TABLE \"s3_object_deletions\" ADD COLUMN \"deleteAfter\" TIMESTAMP NOT NULL`",
                      "prompt": "Policy 1: Database Migration - Non-nullable Columns on existing tables",
                      "explanation": "VIOLATION: YES. Adds a NOT NULL column 'deleteAfter' to existing table 's3_object_deletions' without a DEFAULT value. Table is not created in this migration file.",
                      "file_path": "migrations/1586541313406-AddDeleteAfterColumnToS3ObjectDeletionsTable.ts"
                    }
                  ]
                }
            """),
            
            'runway_pr3961_diff.txt': dedent("""
                {
                "violations": []
                }
            """),
            
            'runway_pr2326_diff.txt': dedent("""
                {
                  "violations": [
                    {
                      "is_violation": true,
                      "line_number_one_based": 7,
                      "content": "await queryRunner.query(`ALTER TABLE \"user_memberships\" ALTER COLUMN \"organizationRole\" SET NOT NULL;",
                      "prompt": "Policy 1: Database Migration - Non-nullable Columns on existing tables",
                      "explanation": "VIOLATION: YES. Adds a non-nullable constraint to an existing column 'organizationRole' in previously existing table 'user_memberships' without providing a default value.",
                      "file_path": "migrations/1709590917664-UpdateUserMembershipsRoleColumn.ts"
                    }
                  ]
                }
            """),
        }
