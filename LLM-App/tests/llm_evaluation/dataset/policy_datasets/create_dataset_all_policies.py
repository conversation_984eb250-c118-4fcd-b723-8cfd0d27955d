"""
Aggregator for all policy datasets - creates a single combined dataset from all policies

Run 'python tests/llm_evaluation/dataset/policy_datasets/create_dataset_all_policies.py' to create a dataset with all policies combined.

Optional arguments:
  --assume-role ROLE_ARN  Assume the specified AWS role for S3 access (e.g., arn:aws:iam::290275941934:role/TanagramInfrastructureRole)
  --username USERNAME     IAM username for MFA device lookup (e.g., shivansh)
  --mfa-token TOKEN      MFA token code for role assumption
  --dataset-name NAME    Custom name for the dataset
"""
import logging
import argparse
from tests.llm_evaluation.dataset.policy_datasets.policy_non_nullable_columns import NonNullableColumnsDataset
from tests.llm_evaluation.dataset.policy_datasets.policy_foreign_keys import ForeignKeysDataset  
from tests.llm_evaluation.dataset.policy_datasets.policy_query_requirements import QueryRequirementsDataset
from tests.llm_evaluation.run_evaluation import EvaluationDataset, create_dataset_entries
from src.services.AWS.S3Service import s3_service
from src.services.AWS.S3RoleService import S3RoleService

logger = logging.getLogger(__name__)


def create_combined_dataset(dataset_name: str = "Combined Policy Dataset", s3_client=None):
    """Create a SINGLE dataset with all policies and their diffs combined
    
    Args:
        dataset_name: Name for the combined dataset
        s3_client: Optional S3 service instance (uses default if None)
    """
    datasets = [
        NonNullableColumnsDataset(),
        ForeignKeysDataset(),
        QueryRequirementsDataset()
    ]
    
    all_entries = []
    
    # Use provided S3 client or default
    s3 = s3_client or s3_service
    
    for dataset in datasets:
        for diff_file, expected_output in dataset.diff_expectations.items():
            diff_content = s3.get_diff_file(diff_file)
            if not diff_content:
                logger.warning(f"Could not retrieve diff file '{diff_file}' from S3")
                continue

            all_entries.append(
                EvaluationDataset(
                    dataset.policy_text,
                    diff_content,
                    expected_output
                )
            )
    
    create_dataset_entries(all_entries, name=dataset_name)
    return len(all_entries)


# Parse command line arguments
def parse_args():
    parser = argparse.ArgumentParser(description="Create a combined dataset with all policies.")
    parser.add_argument("--dataset-name", default="Runway Combined Dataset",
                        help="Name for the combined dataset")
    parser.add_argument("--assume-role", dest="role_arn",
                        help="ARN of the role to assume for S3 access")
    parser.add_argument("--username", help="IAM username for MFA device lookup")
    parser.add_argument("--mfa-token", help="MFA token code for role assumption")
    return parser.parse_args()


# Usage example
if __name__ == "__main__":
    args = parse_args()
    
    # Create S3 service (with role assumption if requested)
    s3_client = None
    # if args.role_arn and args.mfa_token:
    s3_client = S3RoleService(
        role_arn=args.role_arn or "arn:aws:iam::290275941934:role/TanagramInfrastructureRole",
        username=args.username or "shivansh",
        mfa_token=args.mfa_token or "xyz"
    )
    logger.info(f"Using assumed role {args.role_arn} for S3 access")
    
    # Create the combined dataset
    count = create_combined_dataset(args.dataset_name, s3_client)
    logger.info(f"Created combined dataset with {count} entries")
