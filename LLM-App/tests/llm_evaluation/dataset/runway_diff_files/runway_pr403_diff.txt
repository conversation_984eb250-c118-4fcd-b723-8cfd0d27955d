PR #403: Teams
================================================================================

diff --git a/controllers/datasets.ts b/controllers/datasets.ts
--- a/controllers/datasets.ts
+++ b/controllers/datasets.ts
@@ -11,6 +11,8 @@ import {
   Dataset,
   DatasetConfig,
 } from '../entity/datasets/Dataset';
+import { ResourcePermissionMode } from '../entity/ResourcePermission';
+import { SearchConditions } from '../entity/SharableResourceEntity';
 import { Task } from '../entity/tasks/Task';
 import { Upload } from '../entity/Upload';
 import { DATASETS_BUCKET, UUID_REGEX } from '../lib/constants';
@@ -32,9 +34,6 @@ export default () => {
   router.get(
     '/v1/datasets',
     [
-      query('includePublicDatasets')
-        .optional()
-        .isBoolean(),
       query('limit')
         .optional()
         .isInt({ min: 1, max: 50 }),
@@ -61,43 +60,33 @@ export default () => {
       const offset = parseInt(req.query.offset || 0);
       const type = req.query.type;
       const sort = req.query.sort;
-      const includePublicDatasets = req.query.includePublicDatasets !== 'false';
+      const userId = req.user.id;
       try {
-        let results;
+        let search: SearchConditions;
         if (req.query.q) {
-          results = await Dataset.SearchForDatasets(
-            req.query.q,
-            req.user.id,
-            includePublicDatasets
-          );
-        } else {
-          const whereClauses: any[] = [
-            { userId: req.user.id, private: true, deleted: false },
-          ];
-          if (includePublicDatasets)
-            whereClauses.push({ private: false, deleted: false });
-          results = await Dataset.find({
-            where: whereClauses,
-            order: {
-              createdAt: 'DESC',
-            },
-          });
+          search = {
+            columnNames: ['name'],
+            query: req.query.q,
+          };
         }
+        let results = await Dataset.GetAllWhereUserHasAccess<Dataset>({
+          userId,
+          mode: ResourcePermissionMode.Read,
+          search,
+        });
         if (type) {
           results = results.filter(ds => {
             if (ds.type.name === 'images' && type === 'image') return true;
             if (ds.type.name === 'text' && type === 'text') return true;
             return false;
           });
         }
-        if (sort) {
-          results = await Dataset.SortBy(results, sort);
-        }
+        results = Dataset.SortBy(results, sort || 'date');
         if (limit) {
           results = limit ? results.slice(offset, offset + limit) : results;
         }
         const datasets = await Promise.all(
-          results.map(dataset => dataset.summary(req.user.id))
+          results.map(dataset => dataset.summary(userId))
         );
         return res.status(200).json({ datasets });
       } catch (err) {
@@ -113,7 +102,7 @@ export default () => {
     requiresLogin,
     async (req, res) => {
       const dataset = await Dataset.GetOne(req.params.id);
-      if (!dataset || !dataset.hasReadPermission(req.user.id)) {
+      if (!dataset || !(await dataset.userHasReadPermission(req.user.id))) {
         return res.status(404).json({ error: 'Dataset not found' });
       }
       return res
@@ -344,8 +333,8 @@ export default () => {
           error: 'A dataset with this name already exists for your user',
         });
       }
-      const dataset = await Dataset.GetOne({ id: req.params.id, userId });
-      if (!dataset) {
+      const dataset = await Dataset.GetOne({ id: req.params.id });
+      if (!dataset || !(await dataset.userHasWritePermission(userId))) {
         return res.status(404).json({ error: 'Dataset not found' });
       }
       if (name) {
@@ -369,11 +358,8 @@ export default () => {
     requiresLogin,
     async (req, res) => {
       try {
-        const dataset = await Dataset.GetOne({
-          id: req.params.id,
-          userId: req.user.id,
-        });
-        if (!dataset) {
+        const dataset = await Dataset.GetOne(req.params.id);
+        if (!dataset || !(await dataset.userHasAdminPermission(req.user.id))) {
           return res.status(404).json({ error: 'Dataset not found' });
         }
         await dataset.delete();
@@ -408,7 +394,7 @@ export default () => {
       const dataset = await Dataset.GetOne({
         id: req.params.id,
       });
-      if (!dataset || !dataset.hasReadPermission(req.user.id)) {
+      if (!dataset || !(await dataset.userHasReadPermission(req.user.id))) {
         return res.status(404).json({ error: 'Dataset not found' });
       }
       const cachedFileList = await dataset.getCachedFileList();
@@ -437,7 +423,7 @@ export default () => {
       const dataset = await Dataset.GetOne({
         id: req.params.id,
       });
-      if (!dataset || !dataset.hasReadPermission(req.user.id)) {
+      if (!dataset || !(await dataset.userHasReadPermission(req.user.id))) {
         return res.status(404).json({ error: 'Dataset not found' });
       }
       if (!(await dataset.isArchiveMicroserviceRunning())) {
@@ -461,7 +447,7 @@ export default () => {
       const dataset = await Dataset.GetOne({
         id: req.params.id,
       });
-      if (!dataset || !dataset.hasReadPermission(req.user.id)) {
+      if (!dataset || !(await dataset.userHasReadPermission(req.user.id))) {
         return res.status(404).json({ error: 'Dataset not found' });
       }
       const task = await Task.Create({
@@ -488,7 +474,7 @@ export default () => {
       const dataset = await Dataset.GetOne({
         id: req.params.id,
       });
-      if (!dataset || !dataset.hasReadPermission(req.user.id)) {
+      if (!dataset || !(await dataset.userHasReadPermission(req.user.id))) {
         return res.status(404).json({ error: 'Dataset not found' });
       }
       const task = await Task.Create({

diff --git a/controllers/endpoints.ts b/controllers/endpoints.ts
--- a/controllers/endpoints.ts
+++ b/controllers/endpoints.ts
@@ -4,7 +4,6 @@ import { body, param, query } from 'express-validator/check';
 import { Endpoint } from '../entity/endpoints/Endpoint';
 import { EndpointRequests } from '../entity/endpoints/EndpointRequests';
 import { Model } from '../entity/models/Model';
-import { ModelPermissionMode } from '../entity/models/ModelPermission';
 import { User } from '../entity/users/User';
 import { UUID_REGEX } from '../lib/constants';
 import {
@@ -80,7 +79,7 @@ export default () => {
       ]);
       if (!model || !user) return ErrorNotFound(res);
       const [userHasPermission, quota] = await Promise.all([
-        model.userHasPermission(user.id, ModelPermissionMode.Read),
+        model.userHasReadPermission(user.id),
         user.quota(),
       ]);
       if (!userHasPermission) return ErrorPermissionDenied(res);

diff --git a/controllers/model_builds.ts b/controllers/model_builds.ts
--- a/controllers/model_builds.ts
+++ b/controllers/model_builds.ts
@@ -2,7 +2,6 @@ import * as Sentry from '@sentry/node';
 import * as express from 'express';

 import { ModelBuild } from '../entity/models/ModelBuild';
-import { ModelPermissionMode } from '../entity/models/ModelPermission';
 import { BUILD_LOGS_BUCKET } from '../lib/constants';
 import { getRunningModelBuildLogs } from '../lib/k8s';
 import { readS3File } from '../lib/s3';
@@ -20,9 +19,7 @@ export default () => {
         return res.status(404).json({ error: 'Build not found' });
       }
       const model = await build.model();
-      if (
-        !(await model.userHasPermission(req.user.id, ModelPermissionMode.Write))
-      ) {
+      if (!(await model.userHasWritePermission(req.user.id))) {
         return res
           .status(401)
           .json({ error: 'Not authorized to view build status' });
@@ -41,9 +38,7 @@ export default () => {
         return res.status(404).json({ error: 'Build not found' });
       }
       const model = await build.model();
-      if (
-        !(await model.userHasPermission(req.user.id, ModelPermissionMode.Write))
-      ) {
+      if (!(await model.userHasWritePermission(req.user.id))) {
         return res
           .status(401)
           .json({ error: 'Not authorized to view build logs' });
@@ -78,9 +73,7 @@ export default () => {
         return res.status(404).json({ error: 'Build not found' });
       }
       const model = await build.model();
-      if (
-        !(await model.userHasPermission(req.user.id, ModelPermissionMode.Write))
-      ) {
+      if (!(await model.userHasWritePermission(req.user.id))) {
         return res.status(401).json({ error: 'Not authorized to stop build' });
       }
       if (build.endedAt) {

diff --git a/controllers/model_sessions.ts b/controllers/model_sessions.ts
--- a/controllers/model_sessions.ts
+++ b/controllers/model_sessions.ts
@@ -5,12 +5,12 @@ import * as allSettled from 'promise.allsettled';
 import { In } from 'typeorm';

 import { Model } from '../entity/models/Model';
-import { ModelPermissionMode } from '../entity/models/ModelPermission';
 import {
   ModelSession,
   ModelSessionConfig,
 } from '../entity/models/ModelSession';
 import { ModelVersion } from '../entity/models/ModelVersion';
+import { ResourcePermissionMode } from '../entity/ResourcePermission';
 import { User } from '../entity/users/User';
 import { RUNWAY_ORG_ID, UserMembership } from '../entity/users/UserMembership';
 import { getModelServerLogs } from '../lib/k8s';
@@ -103,8 +103,8 @@ export default () => {

       const permissionRequired =
         req.body.version === 'default'
-          ? ModelPermissionMode.Read
-          : ModelPermissionMode.Write;
+          ? ResourcePermissionMode.Read
+          : ResourcePermissionMode.Write;

       if (
         !(await model.userHasPermission(

diff --git a/controllers/model_versions.ts b/controllers/model_versions.ts
--- a/controllers/model_versions.ts
+++ b/controllers/model_versions.ts
@@ -4,7 +4,6 @@ import { In } from 'typeorm';
 import * as uuid from 'uuid';

 import { Model } from '../entity/models/Model';
-import { ModelPermissionMode } from '../entity/models/ModelPermission';
 import { ModelVersion } from '../entity/models/ModelVersion';
 import { User } from '../entity/users/User';
 import { MODEL_SOURCES_BUCKET } from '../lib/constants';
@@ -68,9 +67,7 @@ export default () => {
           .json({ error: 'This model version does not exist' });
       }
       const model = await modelVersion.model();
-      if (
-        !(await model.userHasPermission(req.user.id, ModelPermissionMode.Read))
-      ) {
+      if (!(await model.userHasReadPermission(req.user.id))) {
         return res.status(401).json({
           error: 'You do not have permission to view this version of the model',
         });

diff --git a/controllers/models.ts b/controllers/models.ts
--- a/controllers/models.ts
+++ b/controllers/models.ts
@@ -11,12 +11,9 @@ import { Model } from '../entity/models/Model';
 import { ModelBuild } from '../entity/models/ModelBuild';
 import { ModelCheckpoint } from '../entity/models/ModelCheckpoint';
 import { ModelImage } from '../entity/models/ModelImage';
-import {
-  ModelPermission,
-  ModelPermissionMode,
-} from '../entity/models/ModelPermission';
 import { ModelRating } from '../entity/models/ModelRating';
 import { ModelVersion } from '../entity/models/ModelVersion';
+import { ResourcePermissionMode } from '../entity/ResourcePermission';
 import { User } from '../entity/users/User';
 import { RUNWAY_ORG_ID, UserMembership } from '../entity/users/UserMembership';
 import {
@@ -95,37 +92,36 @@ export default () => {
           });

       const { includeUserModels, type, category } = req.query;
-      const publicModels = includeFeaturedModels
-        ? await Model.find({ private: false, featured: true, deleted: false })
-        : [];
-
-      let results = publicModels;
-      if (req.user && includeUserModels !== 'false') {
-        const userModels = await Model.find({ userId: req.user.id });
-        const modelIdsWithPermission = (
-          await ModelPermission.find({ userId: req.user.id })
-        ).map(permission => permission.modelId);
-        const permissionedModels =
-          modelIdsWithPermission.length > 0
-            ? await Model.find({
-                where: { id: In(modelIdsWithPermission) },
-              })
-            : [];
-        const orgIds = await UserMembership.GetUserOrgIds(req.user.id);
-        const orgModels = orgIds.length
-          ? await Model.find({ where: { userId: In(orgIds) } })
-          : [];

-        results = [
-          ...publicModels,
-          ...userModels,
-          ...orgModels,
-          ...permissionedModels,
-        ];
-      }
+      const userId = req.user ? req.user.id : -1;
+      const getPublicModels = async (): Promise<Model[]> => {
+        if (includeFeaturedModels) {
+          return Model.find({
+            private: false,
+            featured: true,
+            deleted: false,
+          });
+        }
+        return [];
+      };
+      const getUserModels = async (): Promise<Model[]> => {
+        if (req.user && includeUserModels !== 'false') {
+          return Model.GetAllWhereUserHasAccess<Model>({
+            userId,
+            mode: ResourcePermissionMode.Read,
+            omitPublicReadOnlyWithNoDirectPermission: true,
+          });
+        }
+        return [];
+      };
+      const [publicModels, userModels] = await Promise.all([
+        getPublicModels(),
+        getUserModels(),
+      ]);

+      let results = [...publicModels, ...userModels];
       results = _.uniqBy(results, m => m.id);
-      results = results.filter(m => !m.deleted);
+
       if (searchQuery) {
         results = await Model.SearchModels(results, searchQuery);
       }
@@ -145,9 +141,7 @@ export default () => {
       if (limit) {
         results = results.slice(offset, offset + limit);
       }
-      const models = await Promise.all(
-        results.map(m => m.summary(req.user ? req.user.id : -1))
-      );
+      const models = await Promise.all(results.map(m => m.summary(userId)));
       return res.status(200).json({ models, count });
     }
   );
@@ -365,10 +359,7 @@ export default () => {
       if (!model) {
         return res.status(404).json({ error: 'Model not found' });
       }
-      const permitted = await model.userHasPermission(
-        req.user.id,
-        ModelPermissionMode.Write
-      );
+      const permitted = await model.userHasWritePermission(req.user.id);
       if (!permitted) {
         return res
           .status(401)
@@ -397,10 +388,7 @@ export default () => {
       if (!model) {
         return res.status(404).json({ error: 'Model not found' });
       }
-      const permitted = await model.userHasPermission(
-        req.user.id,
-        ModelPermissionMode.Write
-      );
+      const permitted = await model.userHasWritePermission(req.user.id);
       if (!permitted) {
         return res
           .status(401)
@@ -433,10 +421,7 @@ export default () => {
       if (!model) {
         return res.status(404).json({ error: 'Model not found' });
       }
-      const permitted = await model.userHasPermission(
-        req.user.id,
-        ModelPermissionMode.Write
-      );
+      const permitted = await model.userHasWritePermission(req.user.id);
       if (!permitted) {
         return res
           .status(401)
@@ -492,10 +477,7 @@ export default () => {
       if (!model) {
         return res.status(404).json({ error: 'Model not found' });
       }
-      const permitted = await model.userHasPermission(
-        req.user.id,
-        ModelPermissionMode.Admin
-      );
+      const permitted = await model.userHasAdminPermission(req.user.id);
       if (!permitted) {
         return res
           .status(401)
@@ -565,10 +547,7 @@ export default () => {
       if (!model) {
         return res.status(404).json({ error: 'Model not found' });
       }
-      const permitted = await model.userHasPermission(
-        req.user.id,
-        ModelPermissionMode.Admin
-      );
+      const permitted = await model.userHasAdminPermission(req.user.id);
       if (!permitted) {
         return res.status(401).json({
           error: 'You are not authorized to make this model private.',
@@ -645,10 +624,7 @@ export default () => {
     if (!model) {
       return res.status(404).json({ error: 'Model not found' });
     }
-    const permitted = await model.userHasPermission(
-      req.user.id,
-      ModelPermissionMode.Read
-    );
+    const permitted = await model.userHasReadPermission(req.user.id);
     if (!permitted) {
       return res.status(401).json({
         error:
@@ -668,10 +644,7 @@ export default () => {
     if (!model) {
       return res.status(404).json({ error: 'Model not found' });
     }
-    const permitted = await model.userHasPermission(
-      req.user.id,
-      ModelPermissionMode.Admin
-    );
+    const permitted = await model.userHasAdminPermission(req.user.id);
     if (!permitted) {
       return res
         .status(401)
@@ -693,10 +666,7 @@ export default () => {
       if (!model) {
         return res.status(404).json({ error: 'Model not found' });
       }
-      const readPermission = await model.userHasPermission(
-        req.user.id,
-        ModelPermissionMode.Read
-      );
+      const readPermission = await model.userHasReadPermission(req.user.id);
       if (!readPermission) {
         return res.status(401).json({
           error: "You do not have permission to view this model's artifacts.",
@@ -726,10 +696,7 @@ export default () => {
       if (!model) {
         return res.status(404).json({ error: 'Model not found' });
       }
-      const permitted = await model.userHasPermission(
-        req.user.id,
-        ModelPermissionMode.Write
-      );
+      const permitted = await model.userHasWritePermission(req.user.id);
       if (!permitted) {
         return res.status(401).json({
           error: "You do not have permission to view this model's artifacts.",
@@ -774,10 +741,7 @@ export default () => {
       if (!model) {
         return res.status(404).json({ error: 'Model not found' });
       }
-      const permitted = await model.userHasPermission(
-        req.user.id,
-        ModelPermissionMode.Write
-      );
+      const permitted = await model.userHasWritePermission(req.user.id);
       if (!permitted) {
         return res.status(401).json({
           error: "You do not have permission to view this model's artifacts.",
@@ -861,9 +825,8 @@ export default () => {
         return res.status(404).json({ error: 'Artifact not found' });
       }
       const model = await artifact.model();
-      const permittedToWriteModel = await model.userHasPermission(
-        req.user.id,
-        ModelPermissionMode.Write
+      const permittedToWriteModel = await model.userHasWritePermission(
+        req.user.id
       );
       const isArtifactOwner = artifact.userId === req.user.id;
       if (!permittedToWriteModel && !isArtifactOwner) {
@@ -917,10 +880,7 @@ export default () => {
         return res.status(404).json({ error: 'Artifact not found' });
       }
       const model = await artifact.model();
-      const permitted = await model.userHasPermission(
-        req.user.id,
-        ModelPermissionMode.Write
-      );
+      const permitted = await model.userHasWritePermission(req.user.id);
       if (!permitted && artifact.userId !== req.user.id) {
         return res.status(401).json({
           error: "You do not have permission to view this model's artifacts.",
@@ -965,10 +925,7 @@ export default () => {
         return res.status(404).json({ error: 'Artifact not found' });
       }
       const model = await artifact.model();
-      const permitted = await model.userHasPermission(
-        req.user.id,
-        ModelPermissionMode.Write
-      );
+      const permitted = await model.userHasWritePermission(req.user.id);
       if (!permitted) {
         return res.status(401).json({
           error: "You do not have permission to view this model's artifacts.",
@@ -991,10 +948,7 @@ export default () => {
       if (!model) {
         return res.status(404).json({ error: 'Model not found' });
       }
-      const permitted = await model.userHasPermission(
-        req.user.id,
-        ModelPermissionMode.Write
-      );
+      const permitted = await model.userHasWritePermission(req.user.id);
       if (!permitted) {
         return res.status(401).json({
           error:
@@ -1033,10 +987,7 @@ export default () => {
       if (!model) {
         return res.status(404).json({ error: 'Model not found' });
       }
-      const permitted = await model.userHasPermission(
-        req.user.id,
-        ModelPermissionMode.Write
-      );
+      const permitted = await model.userHasWritePermission(req.user.id);
       if (!permitted) {
         return res.status(401).json({
           error: 'You are not authorized to add an image to this model.',
@@ -1068,10 +1019,7 @@ export default () => {
       if (!model) {
         return res.status(404).json({ error: 'Model not found' });
       }
-      const permitted = await model.userHasPermission(
-        req.user.id,
-        ModelPermissionMode.Write
-      );
+      const permitted = await model.userHasWritePermission(req.user.id);
       if (!permitted) {
         return res.status(401).json({
           error: 'You are not authorized to remove an image from this model.',
@@ -1104,10 +1052,7 @@ export default () => {
       if (!model) {
         return res.status(404).json({ error: 'Model not found' });
       }
-      const permitted = await model.userHasPermission(
-        req.user.id,
-        ModelPermissionMode.Read
-      );
+      const permitted = await model.userHasReadPermission(req.user.id);
       if (!permitted) {
         return res
           .status(401)
@@ -1138,10 +1083,7 @@ export default () => {
     if (!model) {
       return res.status(404).json({ error: 'Model not found' });
     }
-    const permitted = await model.userHasPermission(
-      req.user.id,
-      ModelPermissionMode.Read
-    );
+    const permitted = await model.userHasReadPermission(req.user.id);
     if (!permitted) {
       return res.status(401).json({
         error: 'You are not authorized to view clones of this model.',
@@ -1150,9 +1092,7 @@ export default () => {
     const clones = await Model.find({ rootId: model.id, deleted: false });
     const filteredClones = [];
     for (const clone of clones) {
-      if (
-        await clone.userHasPermission(req.user.id, ModelPermissionMode.Read)
-      ) {
+      if (await clone.userHasReadPermission(req.user.id)) {
         filteredClones.push(clone);
       }
     }
@@ -1176,9 +1116,7 @@ export default () => {
           .status(404)
           .json({ error: 'A model with that id does not exist' });
       }
-      if (
-        !(await model.userHasPermission(req.user.id, ModelPermissionMode.Read))
-      ) {
+      if (!(await model.userHasReadPermission(req.user.id))) {
         return res.status(401).json({
           error: 'You do not have permission to view versions of this model',
         });
@@ -1211,9 +1149,7 @@ export default () => {
           .status(404)
           .json({ error: 'A model with that id does not exist' });
       }
-      if (
-        !(await model.userHasPermission(req.user.id, ModelPermissionMode.Write))
-      ) {
+      if (!(await model.userHasWritePermission(req.user.id))) {
         return res.status(401).json({
           error:
             'You do not have permission to create a new version of this model',
@@ -1250,9 +1186,7 @@ export default () => {
     if (!model) {
       return res.status(404).json({ error: 'Model not found' });
     }
-    if (
-      !(await model.userHasPermission(req.user.id, ModelPermissionMode.Write))
-    ) {
+    if (!(await model.userHasWritePermission(req.user.id))) {
       return res
         .status(401)
         .json({ error: 'Not authorized to view build status' });
@@ -1289,12 +1223,7 @@ export default () => {
         });
       }

-      if (
-        !(await model.userHasPermission(
-          req.user ? req.user.id : -1,
-          ModelPermissionMode.Read
-        ))
-      ) {
+      if (!(await model.userHasReadPermission(req.user ? req.user.id : -1))) {
         return res
           .status(401)
           .json({ error: 'You do not have permission to view this model' });
@@ -1340,8 +1269,8 @@ export default () => {

       const permissionRequired =
         req.params.version === 'default'
-          ? ModelPermissionMode.Read
-          : ModelPermissionMode.Write;
+          ? ResourcePermissionMode.Read
+          : ResourcePermissionMode.Write;

       if (
         !(await model.userHasPermission(

diff --git a/controllers/organizations.ts b/controllers/organizations.ts
--- a/controllers/organizations.ts
+++ b/controllers/organizations.ts
@@ -76,6 +76,7 @@ export default () => {
         organizationId,
         organizationRole:
           role === 'admin' ? MembershipRole.Admin : MembershipRole.Member,
+        createdBy: req.user.id,
       });
       if (properties) {
         if (typeof properties !== 'object') {

diff --git a/controllers/resource_permissions.ts b/controllers/resource_permissions.ts
--- a/controllers/resource_permissions.ts
+++ b/controllers/resource_permissions.ts
@@ -0,0 +1,98 @@
+import * as express from 'express';
+import { body, param } from 'express-validator/check';
+
+import {
+  ResourcePermission,
+  ResourcePermissionModeName,
+  ResourceType,
+} from '../entity/ResourcePermission';
+import { SharableResourceEntity } from '../entity/SharableResourceEntity';
+import { Success, SuccessWithBody } from '../lib/responses';
+import { requiresLogin, validateArguments } from '../lib/utils';
+
+export default () => {
+  const router = express.Router();
+
+  router.get('/v1/resource_permissions', requiresLogin, async (req, res) => {
+    const permissions = await ResourcePermission.GetByUserIdOrCreatedBy(
+      req.user.id
+    );
+    return SuccessWithBody(res, {
+      resourcePermissions: permissions.map(permission => permission.summary()),
+    });
+  });
+
+  router.post(
+    '/v1/resource_permissions',
+    [
+      body('userId').isNumeric(),
+      body('resourceId').exists(),
+      body('resourceType').isIn(Object.values(ResourceType)),
+      body('mode').isIn(Object.values(ResourcePermissionModeName)),
+    ],
+    requiresLogin,
+    validateArguments,
+    async (req, res) => {
+      const { resourceType, resourceId, mode } = req.body;
+      const requestingUserId = req.user.id;
+      const targetUserId = req.body.userId;
+      const resource = await SharableResourceEntity.GetByResourceTypeAndId(
+        resourceType,
+        resourceId
+      );
+      await resource.failIfHasNoAdminPermission(requestingUserId);
+      const permission = await ResourcePermission.Create({
+        userId: targetUserId,
+        createdBy: requestingUserId,
+        resourceType,
+        resourceId,
+        mode: ResourcePermission.ResourcePermissionMode(mode),
+      });
+      return SuccessWithBody(res, {
+        resourcePermission: permission.summary(),
+      });
+    }
+  );
+
+  router.put(
+    '/v1/resource_permissions/:permissionId',
+    [
+      param('permissionId').exists(),
+      body('mode').isIn(Object.values(ResourcePermissionModeName)),
+    ],
+    requiresLogin,
+    validateArguments,
+    async (req, res) => {
+      const { mode } = req.body;
+      const { permissionId } = req.params;
+      const requestingUserId = req.user.id;
+      const resource = await SharableResourceEntity.GetByPermissionId(
+        permissionId
+      );
+      await resource.failIfHasNoAdminPermission(requestingUserId);
+      const permission = await ResourcePermission.GetOne(permissionId);
+      await permission.setModeFromModeName(mode);
+      return Success(res);
+    }
+  );
+
+  router.delete(
+    '/v1/resource_permissions/:permissionId',
+    [param('permissionId').exists()],
+    requiresLogin,
+    validateArguments,
+    async (req, res) => {
+      const { permissionId } = req.params;
+      const requestingUserId = req.user.id;
+      const resource = await SharableResourceEntity.GetByPermissionId(
+        permissionId
+      );
+      await resource.failIfHasNoAdminPermission(requestingUserId);
+      const permission = await ResourcePermission.GetOne(permissionId);
+      await permission.delete();
+      return Success(res);
+    }
+  );
+
+  return router;
+};

diff --git a/controllers/teams.ts b/controllers/teams.ts
--- a/controllers/teams.ts
+++ b/controllers/teams.ts
@@ -0,0 +1,213 @@
+import * as express from 'express';
+import { body, param } from 'express-validator/check';
+
+import { User } from '../entity/users/User';
+import {
+  MembershipRole,
+  MembershipRoleName,
+  UserMembership,
+} from '../entity/users/UserMembership';
+import { BadRequestError, NotFoundError } from '../lib/errors';
+import { Success, SuccessWithBody } from '../lib/responses';
+import usernameBlacklist from '../lib/username_blacklist';
+import { requiresLogin, usernameExists, validateArguments } from '../lib/utils';
+
+const validateTeamName = async (name: string) => {
+  // This is sloppy pasted from /v1/register
+  if (name.includes(' ') || name.includes('@')) {
+    throw new BadRequestError('name must not include " " or "@" characters');
+  }
+  if (await usernameExists(name)) {
+    throw new BadRequestError('A user exists with the same username.');
+  }
+};
+
+export default () => {
+  const router = express.Router();
+
+  // TODO: This should probably also show the user's role in each team as well
+  router.get('/v1/teams', requiresLogin, async (req, res) => {
+    const user = await User.GetOne({ id: req.user.id });
+    const { organizations } = await user.sanitize();
+    return res.status(200).json({ teams: organizations });
+  });
+
+  router.post(
+    '/v1/teams',
+    [
+      body('name').isString(),
+      body('name')
+        .not()
+        .isIn(usernameBlacklist),
+    ],
+    requiresLogin,
+    validateArguments,
+    async (req, res) => {
+      const { name } = req.body;
+      await validateTeamName(name);
+      const user = await User.GetOne(req.user.id);
+      // The two methods below should be combined into a single Team.Create() method,
+      // with a foundingMember property in the config opject.
+      const org = await User.CreateOrgUser({ name, email: user.email });
+      await UserMembership.Create({
+        userId: user.id,
+        createdBy: user.id,
+        organizationId: org.id,
+        organizationRole: MembershipRole.Admin,
+      });
+      return SuccessWithBody(res, { team: org.publicProfile() });
+    }
+  );
+
+  router.delete(
+    '/v1/teams/:teamId',
+    [param('teamId').isNumeric()],
+    requiresLogin,
+    validateArguments,
+    async (req, res) => {
+      const { teamId } = req.params;
+      await UserMembership.FailIfNotActiveMember(
+        req.user.id,
+        teamId,
+        MembershipRole.Admin
+      );
+      const org = await User.GetOne({ id: teamId, isOrganization: true });
+      if (!org) throw new NotFoundError();
+      await org.delete();
+      return Success(res);
+    }
+  );
+
+  router.put(
+    '/v1/teams/:teamId',
+    [param('teamId').isNumeric(), body('name').isString()],
+    requiresLogin,
+    validateArguments,
+    async (req, res) => {
+      const { teamId } = req.params;
+      const { name } = req.body;
+      await UserMembership.FailIfNotActiveMember(
+        req.user.id,
+        teamId,
+        MembershipRole.Admin
+      );
+      const org = await User.GetOne({ id: teamId, isOrganization: true });
+      if (!org) throw new NotFoundError();
+      await validateTeamName(name);
+      org.username = name;
+      await org.save();
+      return Success(res);
+    }
+  );
+
+  router.get(
+    '/v1/teams/:teamId/members',
+    [param('teamId').isNumeric()],
+    requiresLogin,
+    validateArguments,
+    async (req, res) => {
+      const { teamId } = req.params;
+      const org = await User.GetOne({ id: teamId, isOrganization: true });
+      if (!org) throw new NotFoundError();
+      await UserMembership.FailIfNotActiveMember(req.user.id, teamId);
+      const pairs = await UserMembership.GetOrgMemberRolePairs(teamId);
+      const members = pairs.map(pair => ({
+        user: pair.user.publicProfile(),
+        role: UserMembership.MembershipRoleName(pair.role),
+      }));
+      return SuccessWithBody(res, { members });
+    }
+  );
+
+  router.post(
+    '/v1/teams/:teamId/members',
+    [
+      param('teamId').isInt(),
+      body('userId').isInt(),
+      body('role').isIn(['member', 'admin']),
+    ],
+    requiresLogin,
+    validateArguments,
+    async (req, res) => {
+      const { role } = req.body;
+      const { teamId } = req.params;
+      const targetUserId = req.body.userId as number;
+      const [requestUser, org] = await Promise.all([
+        User.GetOne(req.user.id),
+        User.GetOne({
+          id: teamId,
+          isOrganization: true,
+        }),
+      ]);
+      if (!org || !requestUser) throw new NotFoundError();
+      await UserMembership.FailIfNotActiveMember(
+        requestUser.id,
+        teamId,
+        MembershipRole.Admin
+      );
+      if (await UserMembership.IsActiveMember(targetUserId, teamId)) {
+        throw new BadRequestError(
+          'User is already a member of this organization'
+        );
+      }
+      await UserMembership.Create({
+        userId: targetUserId,
+        organizationId: teamId,
+        organizationRole:
+          role === 'admin' ? MembershipRole.Admin : MembershipRole.Member,
+        createdBy: requestUser.id,
+      });
+      return Success(res);
+    }
+  );
+
+  router.delete(
+    '/v1/teams/:teamId/members/:userId',
+    [param('teamId').isNumeric(), param('userId').isNumeric()],
+    requiresLogin,
+    validateArguments,
+    async (req, res) => {
+      const { teamId, userId } = req.params;
+      // Allow users to remove themselves from the team even if they aren't an admin
+      if (req.user.id !== parseInt(userId)) {
+        await UserMembership.FailIfNotActiveMember(
+          req.user.id,
+          teamId,
+          MembershipRole.Admin
+        );
+      }
+      await UserMembership.Inactivate(userId, teamId);
+      return Success(res);
+    }
+  );
+
+  router.put(
+    '/v1/teams/:teamId/members/:userId',
+    [
+      param('teamId').isNumeric(),
+      param('userId').isNumeric(),
+      body('role').isIn([MembershipRoleName.Member, MembershipRoleName.Admin]),
+    ],
+    requiresLogin,
+    validateArguments,
+    async (req, res) => {
+      const { teamId, userId } = req.params;
+      const { role } = req.body;
+      await UserMembership.FailIfNotActiveMember(
+        req.user.id,
+        teamId,
+        MembershipRole.Admin
+      );
+      const membership = await UserMembership.findOne({
+        userId,
+        organizationId: teamId,
+        active: true,
+      });
+      if (!membership) throw new NotFoundError();
+      await membership.setRole(UserMembership.MembershipRole(role));
+      return Success(res);
+    }
+  );
+
+  return router;
+};

diff --git a/controllers/training.ts b/controllers/training.ts
--- a/controllers/training.ts
+++ b/controllers/training.ts
@@ -6,7 +6,6 @@ import * as uuid from 'uuid';

 import { Model } from '../entity/models/Model';
 import { ModelCheckpoint } from '../entity/models/ModelCheckpoint';
-import { ModelPermissionMode } from '../entity/models/ModelPermission';
 import { ModelSessionConfig } from '../entity/models/ModelSession';
 import { ModelVersion } from '../entity/models/ModelVersion';
 import { TrainingExperiment } from '../entity/training/TrainingExperiment';
@@ -143,12 +142,7 @@ export default () => {
           if (!model) {
             return res.status(404).json({ error: 'Model not found' });
           }
-          if (
-            !(await model.userHasPermission(
-              req.user.id,
-              ModelPermissionMode.Read
-            ))
-          ) {
+          if (!(await model.userHasReadPermission(req.user.id))) {
             return res
               .status(401)
               .json({ error: 'You do not have permission to use this model' });
@@ -239,10 +233,7 @@ export default () => {
           });
         }
         const model = await modelVersion.model();
-        const hasPermission = await model.userHasPermission(
-          req.user.id,
-          ModelPermissionMode.Read
-        );
+        const hasPermission = await model.userHasReadPermission(req.user.id);
         if (!hasPermission) {
           return res.status(401).json({
             error: 'Permission denied',

diff --git a/controllers/users.ts b/controllers/users.ts
--- a/controllers/users.ts
+++ b/controllers/users.ts
@@ -50,6 +50,7 @@ import {
   getSetting,
   isProduction,
   requiresLogin,
+  usernameExists,
   validateArguments,
 } from '../lib/utils';

@@ -137,14 +138,7 @@ export default () => {
           .status(400)
           .json({ error: 'A user exists with the same email.' });
       }
-
-      const existingWithUsername = await getRepository(User)
-        .createQueryBuilder()
-        .where('LOWER(username) = :username', {
-          username: username.toLowerCase(),
-        })
-        .getOne();
-      if (existingWithUsername) {
+      if (await usernameExists(username)) {
         return res
           .status(400)
           .json({ error: 'A user exists with the same username.' });
@@ -241,6 +235,7 @@ export default () => {
           userId: user.id,
           organizationId: RUNWAY_WEB_BETA_ACCESS_ORG_ID,
           organizationRole: MembershipRole.Member,
+          createdBy: RUNWAY_ORG_ID,
         });
       }
       await createStripeCustomer(user);

diff --git a/entity/ResourcePermission.ts b/entity/ResourcePermission.ts
--- a/entity/ResourcePermission.ts
+++ b/entity/ResourcePermission.ts
@@ -0,0 +1,259 @@
+import {
+  BaseEntity,
+  Column,
+  CreateDateColumn,
+  Entity,
+  In,
+  PrimaryGeneratedColumn,
+  UpdateDateColumn,
+} from 'typeorm';
+
+import { NotFoundError } from '../lib/errors';
+import { UserMembership } from './users/UserMembership';
+
+export enum ResourcePermissionMode {
+  Read = 1,
+  Write,
+  Admin,
+}
+
+export enum ResourcePermissionModeName {
+  Read = 'read',
+  Write = 'write',
+  Admin = 'admin',
+}
+
+export enum ResourceType {
+  MODEL = 'model',
+  TASK = 'task',
+  DATASET = 'dataset',
+  TASK_ARTIFACT = 'taskArtifact',
+  ENDPOINT = 'endpoint',
+  TRAINING_EXPERIMENT = 'trainingExperiment',
+}
+
+export type ResourceId = number | string;
+
+export type ResourcePermissionCreateOpts = {
+  userId: number;
+  createdBy: number;
+  resourceId: ResourceId;
+  resourceType: ResourceType;
+  mode: ResourcePermissionMode;
+};
+
+export type UserHasPermissionOpts = {
+  userId: number;
+  resourceId: ResourceId;
+  resourceType: ResourceType;
+  mode: ResourcePermissionMode;
+};
+
+export type OneOfUsersHasPermissionOpts = {
+  userIds: number[];
+  resourceId: ResourceId;
+  resourceType: ResourceType;
+  mode: ResourcePermissionMode;
+};
+
+export type UserHasPermissionByPermissionIdOpts = {
+  id: string;
+  userId: number;
+  mode: ResourcePermissionMode;
+};
+
+export type OneOfUsersHasPermissionByPermissionIdOpts = {
+  id: string;
+  userIds: number[];
+  mode: ResourcePermissionMode;
+};
+
+@Entity({ name: 'resource_permissions' })
+export class ResourcePermission extends BaseEntity {
+  @PrimaryGeneratedColumn()
+  id: number;
+
+  @CreateDateColumn()
+  createdAt: Date;
+
+  @UpdateDateColumn()
+  updatedAt: Date;
+
+  @Column()
+  userId: number;
+
+  @Column()
+  createdBy: number;
+
+  @Column()
+  resourceId: string;
+
+  @Column()
+  resourceType: string;
+
+  @Column()
+  mode: ResourcePermissionMode;
+
+  @Column({ default: false })
+  deleted: boolean;
+
+  summary() {
+    const {
+      id,
+      createdAt,
+      createdBy,
+      userId,
+      resourceId,
+      resourceType,
+      mode,
+    } = this;
+    return {
+      id,
+      createdAt,
+      createdBy,
+      userId,
+      resourceId: Number(resourceId) || resourceId,
+      resourceType,
+      mode: ResourcePermission.ResourcePermissionModeName(mode),
+    };
+  }
+
+  modeIsAtLeast(mode: ResourcePermissionMode) {
+    return this.mode >= mode;
+  }
+
+  async delete() {
+    this.deleted = true;
+    await this.save();
+  }
+
+  async setModeFromModeName(modeName: ResourcePermissionModeName) {
+    this.mode = ResourcePermission.ResourcePermissionMode(modeName);
+    await this.save();
+  }
+
+  static async Create(opts: ResourcePermissionCreateOpts) {
+    const permission = new ResourcePermission();
+    permission.userId = opts.userId;
+    permission.resourceId =
+      typeof opts.resourceId === 'string'
+        ? opts.resourceId
+        : opts.resourceId.toString();
+    permission.resourceType = opts.resourceType;
+    permission.mode = opts.mode;
+    permission.createdBy = opts.createdBy;
+    await permission.save();
+    return permission;
+  }
+
+  static async UserHasPermission(
+    opts: UserHasPermissionOpts
+  ): Promise<boolean> {
+    return ResourcePermission.OneOfUsersHasPermission({
+      userIds: [opts.userId],
+      ...opts,
+    });
+  }
+
+  static async OneOfUsersHasPermission(
+    opts: OneOfUsersHasPermissionOpts
+  ): Promise<boolean> {
+    const permissions = await ResourcePermission.find({
+      where: {
+        userId: In(opts.userIds),
+        resourceType: opts.resourceType,
+        resourceId: resourceIdToString(opts.resourceId),
+        deleted: false,
+      },
+    });
+    if (permissions.length < 1) return false;
+    return permissions.some(perm => perm.modeIsAtLeast(opts.mode));
+  }
+
+  static async UserHasPermissionByPermissionId(
+    opts: UserHasPermissionByPermissionIdOpts
+  ): Promise<boolean> {
+    return ResourcePermission.OneOfUsersHasPermissionByPermissionId({
+      userIds: [opts.userId],
+      ...opts,
+    });
+  }
+
+  static async OneOfUsersHasPermissionByPermissionId(
+    opts: OneOfUsersHasPermissionByPermissionIdOpts
+  ): Promise<boolean> {
+    const permissions = await ResourcePermission.find({
+      where: {
+        id: opts.id,
+        userId: In(opts.userIds),
+        deleted: false,
+      },
+    });
+    if (permissions.length < 1) return false;
+    return permissions.some(perm => perm.modeIsAtLeast(opts.mode));
+  }
+
+  static async GetByUserIdOrCreatedBy(
+    userId: number,
+    resourceType?: ResourceType
+  ): Promise<ResourcePermission[]> {
+    const teamIds = await UserMembership.GetUserOrgIds(userId);
+    const userIds = [userId, ...teamIds];
+    const where = [
+      {
+        userId: In(userIds),
+        deleted: false,
+      },
+      {
+        createdBy: In(userIds),
+        deleted: false,
+      },
+    ];
+    if (resourceType) {
+      where[0]['resourceType'] = resourceType;
+      where[1]['resourceType'] = resourceType;
+    }
+    const permissions = await ResourcePermission.find({ where });
+    return permissions;
+  }
+
+  static ResourcePermissionModeName(
+    mode: ResourcePermissionMode
+  ): ResourcePermissionModeName {
+    switch (mode) {
+      case ResourcePermissionMode.Admin:
+        return ResourcePermissionModeName.Admin;
+      case ResourcePermissionMode.Write:
+        return ResourcePermissionModeName.Write;
+      case ResourcePermissionMode.Read:
+        return ResourcePermissionModeName.Read;
+      default:
+        throw new Error(`Invalid resource permission mode: ${mode}`);
+    }
+  }
+
+  static ResourcePermissionMode(
+    modeName: ResourcePermissionModeName
+  ): ResourcePermissionMode {
+    switch (modeName) {
+      case ResourcePermissionModeName.Admin:
+        return ResourcePermissionMode.Admin;
+      case ResourcePermissionModeName.Write:
+        return ResourcePermissionMode.Write;
+      case ResourcePermissionModeName.Read:
+        return ResourcePermissionMode.Read;
+      default:
+        throw new Error(`Invalid resource permission mode name: ${modeName}`);
+    }
+  }
+
+  static async GetOne(id: string): Promise<ResourcePermission> {
+    const permission = await this.findOne({ where: { id, deleted: false } });
+    if (!permission) throw new NotFoundError();
+    return permission;
+  }
+}
+
+const resourceIdToString = (resourceId: ResourceId): string => {
+  return typeof resourceId === 'string' ? resourceId : resourceId.toString();
+};

diff --git a/entity/SharableResourceEntity.ts b/entity/SharableResourceEntity.ts
--- a/entity/SharableResourceEntity.ts
+++ b/entity/SharableResourceEntity.ts
@@ -0,0 +1,321 @@
+import {
+  BaseEntity,
+  Brackets,
+  getRepository,
+  SelectQueryBuilder,
+} from 'typeorm';
+
+import { NotFoundError, UnauthorizedError } from '../lib/errors';
+import { Dataset } from './datasets/Dataset';
+import { Model } from './models/Model';
+import {
+  ResourceId,
+  ResourcePermission,
+  ResourcePermissionMode,
+  ResourceType,
+} from './ResourcePermission';
+import { User } from './users/User';
+import { RUNWAY_ORG_ID, UserMembership } from './users/UserMembership';
+
+export type SearchConditions = {
+  columnNames: string[];
+  query: string;
+};
+
+export type GetAllWhereUserHasAccessOpts = {
+  userId: number;
+  mode: ResourcePermissionMode;
+  search?: SearchConditions;
+  omitPublicReadOnlyWithNoDirectPermission?: boolean;
+};
+
+export abstract class SharableResourceEntity extends BaseEntity {
+  public abstract id: ResourceId;
+  public abstract userId: number;
+  public abstract deleted: boolean;
+  public abstract private: boolean;
+  public abstract createdAt: Date;
+
+  protected abstract getSharableResourceType(): ResourceType;
+
+  isOwner(userId: number): boolean {
+    if (typeof userId !== 'number') {
+      throw Error(`userId must be a number, received: ${userId}`);
+    } else if (typeof this.userId !== 'number') {
+      throw Error(`entity's userId must be a number, received: ${userId}`);
+    }
+    return this.userId === userId;
+  }
+
+  async owner(): Promise<User> {
+    return User.GetOne(this.userId);
+  }
+
+  async userHasReadPermission(userId: number) {
+    return this.userHasPermission(userId, ResourcePermissionMode.Read);
+  }
+
+  async userHasWritePermission(userId: number) {
+    return this.userHasPermission(userId, ResourcePermissionMode.Write);
+  }
+
+  async userHasAdminPermission(userId: number) {
+    return this.userHasPermission(userId, ResourcePermissionMode.Admin);
+  }
+
+  async userHasPermission(
+    userId: number,
+    mode: ResourcePermissionMode
+  ): Promise<boolean> {
+    if (this.private === false && mode === ResourcePermissionMode.Read) {
+      return true;
+    }
+    if (this.isOwner(userId)) return true;
+    const owner = await this.owner();
+    if (!owner) return false;
+    const checks = await Promise.all([
+      this.legacyUserIsMemberOfRunwayOrgAndResourceBelongsToOrg(
+        owner,
+        userId,
+        mode
+      ),
+      this.userHasDirectResourcePermission(userId, mode),
+      this.userIsMemberOfOrgWithDirectResourcePermission(userId, mode),
+    ]);
+    return checks.some(check => check === true);
+  }
+
+  async permissionsForUser(userId: number) {
+    const [read, write, admin] = await Promise.all([
+      this.userHasPermission(userId, ResourcePermissionMode.Read),
+      this.userHasPermission(userId, ResourcePermissionMode.Write),
+      this.userHasPermission(userId, ResourcePermissionMode.Admin),
+    ]);
+    return { read, write, admin };
+  }
+
+  async saveWithPermissionCheck(userId: number): Promise<this> {
+    await this.failIfHasNoWritePermission(userId);
+    return this.save();
+  }
+
+  async shareWithUser(userId: number, mode: ResourcePermissionMode) {
+    await this.failIfHasNoAdminPermission(userId);
+    await ResourcePermission.Create({
+      userId,
+      mode,
+      createdBy: this.userId,
+      resourceId: this.id,
+      resourceType: this.getSharableResourceType(),
+    });
+  }
+
+  async transferOwnership(userId: number) {
+    await this.failIfHasNoAdminPermission(userId);
+    this.userId = userId;
+    await this.save();
+  }
+
+  async failIfHasNoReadPermission(userId: number) {
+    await this.failWithUnauthorizedIfFalse(this.userHasReadPermission(userId));
+  }
+
+  async failIfHasNoWritePermission(userId: number) {
+    await this.failWithUnauthorizedIfFalse(this.userHasWritePermission(userId));
+  }
+
+  async failIfHasNoAdminPermission(userId: number) {
+    await this.failWithUnauthorizedIfFalse(this.userHasAdminPermission(userId));
+  }
+
+  private async failWithUnauthorizedIfFalse(predicate: Promise<boolean>) {
+    const succeeded = await predicate;
+    if (!succeeded) throw new UnauthorizedError();
+  }
+
+  private async userHasDirectResourcePermission(
+    userId: number,
+    mode: ResourcePermissionMode
+  ): Promise<boolean> {
+    return ResourcePermission.UserHasPermission({
+      userId,
+      mode,
+      resourceType: this.getSharableResourceType(),
+      resourceId: this.id,
+    });
+  }
+
+  private async userIsMemberOfOrgWithDirectResourcePermission(
+    userId: number,
+    mode: ResourcePermissionMode
+  ): Promise<boolean> {
+    const orgIds = await UserMembership.GetUserOrgIds(userId);
+    if (orgIds.length < 1) return false;
+    return ResourcePermission.OneOfUsersHasPermission({
+      userIds: orgIds,
+      mode,
+      resourceType: this.getSharableResourceType(),
+      resourceId: this.id,
+    });
+  }
+
+  private async legacyUserIsMemberOfRunwayOrgAndResourceBelongsToOrg(
+    owner: User,
+    userId: number,
+    mode: ResourcePermissionMode
+  ) {
+    if (owner.id === RUNWAY_ORG_ID && owner.isOrganization) {
+      const membership = await UserMembership.findOne({
+        organizationId: owner.id,
+        userId,
+        active: true,
+      });
+      // owner is org and user is member
+      if (membership && membership.toResourcePermission() >= mode) return true;
+    }
+    return false;
+  }
+
+  static async GetByResourceTypeAndId(
+    resourceType: ResourceType,
+    resourceId: ResourceId
+  ) {
+    const entity = getEntityTypeByResourceType(resourceType);
+    const resource = await entity.findOne(resourceId);
+    errorIfResourceDoesntExistOrDeleted(resource);
+    return resource;
+  }
+
+  // TODO: This could use an innerJoinAndSelect() rather than two synchronous DB calls
+  static async GetByPermissionId(permissionId: string) {
+    const permission = await ResourcePermission.GetOne(permissionId);
+    const resource = await SharableResourceEntity.GetByResourceTypeAndId(
+      permission.resourceType as ResourceType,
+      permission.resourceId
+    );
+    errorIfResourceDoesntExistOrDeleted(resource);
+    return resource;
+  }
+
+  static async GetSelectQueryBuilderWithAccessCheck<
+    T extends SharableResourceEntity
+  >({
+    userId,
+    mode,
+    omitPublicReadOnlyWithNoDirectPermission,
+  }: {
+    userId: number;
+    mode: ResourcePermissionMode;
+    omitPublicReadOnlyWithNoDirectPermission?: boolean;
+  }): Promise<SelectQueryBuilder<T>> {
+    // @ts-ignore
+    const resourceType = new this().getSharableResourceType();
+    const entityType = getEntityTypeByResourceType(resourceType);
+    const qb = await getRepository<T>(entityType).createQueryBuilder(
+      'resource'
+    );
+    const orgIdsSubquery = qb
+      .subQuery()
+      .select('membership.organizationId')
+      .from('user_memberships', 'membership')
+      .where('membership.userId = :userId', { userId })
+      .andWhere('membership.active = TRUE')
+      .getQuery();
+
+    const query = qb
+      .select('resource')
+      .distinctOn(['resource.id'])
+      .leftJoinAndSelect(
+        'resource_permissions',
+        'permission',
+        'permission.resourceId = resource.id::VARCHAR AND permission.deleted = FALSE'
+      )
+      .innerJoin(
+        'users',
+        'user',
+        'user.id = resource.userId AND user.deleted = FALSE'
+      )
+      .where('resource.deleted = false')
+      .andWhere(
+        new Brackets(qb => {
+          qb.where('resource.userId = :userId', { userId })
+            .orWhere(`resource.userId IN ${orgIdsSubquery}`)
+            .orWhere(
+              new Brackets(qb => {
+                qb.where('permission.mode >= :mode', {
+                  mode,
+                })
+                  .andWhere('permission.resourceType = :resourceType', {
+                    resourceType,
+                  })
+                  .andWhere(
+                    new Brackets(qb => {
+                      qb.where('permission.userId = :userId', {
+                        userId,
+                      }).orWhere(`permission.userId IN ${orgIdsSubquery}`);
+                    })
+                  );
+              })
+            );
+          if (
+            mode === ResourcePermissionMode.Read &&
+            omitPublicReadOnlyWithNoDirectPermission !== true
+          ) {
+            qb.orWhere('resource.private = FALSE');
+          }
+        })
+      );
+    // We can't do this right now, but come back and investigate:
+    //// https://stackoverflow.com/questions/9795660/postgresql-distinct-on-with-different-order-by
+    // .orderBy('resource.createdAt', 'DESC');
+    return query;
+  }
+
+  // Maybe we can add a GetOneWhereUserHasAccess method too!
+  static async GetAllWhereUserHasAccess<T extends SharableResourceEntity>(
+    opts: GetAllWhereUserHasAccessOpts
+  ): Promise<T[]> {
+    const { search } = opts;
+    const query = await this.GetSelectQueryBuilderWithAccessCheck<T>(opts);
+    if (search) addSearchConditionToSelectQueryBuilder(query, search);
+    return query.getMany();
+  }
+}
+
+const getEntityTypeByResourceType = (
+  resourceType: ResourceType
+): typeof SharableResourceEntity => {
+  switch (resourceType) {
+    case ResourceType.MODEL:
+      return Model;
+    case ResourceType.DATASET:
+      return Dataset;
+    default:
+      throw new Error(`Invalid resourceType: ${resourceType}`);
+  }
+};
+
+const errorIfResourceDoesntExistOrDeleted = (
+  resource: SharableResourceEntity
+): void => {
+  if (!resource || resource.deleted) throw new NotFoundError();
+};
+
+const addSearchConditionToSelectQueryBuilder = <
+  T extends SharableResourceEntity
+>(
+  query: SelectQueryBuilder<T>,
+  search: SearchConditions
+): void => {
+  query.andWhere(
+    new Brackets(qb => {
+      for (let i = 0; i < search.columnNames.length; i++) {
+        const column = search.columnNames[i];
+        const fn = i === 0 ? qb.where : qb.orWhere;
+        fn(`resource.${column} ILIKE :query`, {
+          query: search.query,
+        });
+      }
+    })
+  );
+};

diff --git a/entity/datasets/Dataset.ts b/entity/datasets/Dataset.ts
--- a/entity/datasets/Dataset.ts
+++ b/entity/datasets/Dataset.ts
@@ -1,7 +1,6 @@
 import * as _ from 'lodash';
 import * as rp from 'request-promise';
 import {
-  BaseEntity,
   Brackets,
   Column,
   CreateDateColumn,
@@ -28,7 +27,9 @@ import {
   getArchiveMicroserviceURL,
   streamToString,
 } from '../../lib/utils';
+import { ResourceType } from '../ResourcePermission';
 import { S3ObjectDeletion } from '../S3ObjectDeletion';
+import { SharableResourceEntity } from '../SharableResourceEntity';
 import { PublicProfile, User } from '../users/User';
 import { DatasetAnnotationGroup } from './DatasetAnnotationGroup';

@@ -92,7 +93,7 @@ export const bigint = {
 type SortMethod = 'name' | 'date';

 @Entity('datasets')
-export class Dataset extends BaseEntity {
+export class Dataset extends SharableResourceEntity {
   @PrimaryGeneratedColumn('uuid')
   id: string;

@@ -176,11 +177,11 @@ export class Dataset extends BaseEntity {
   }

   async summary(requesterUserId: number): Promise<DatasetSummary> {
-    const user = await User.GetOne({ id: this.userId });
-    const annotationGroups = await DatasetAnnotationGroup.GetManyByDatasetId(
-      this.id,
-      requesterUserId
-    );
+    const [user, annotationGroups, permissions] = await Promise.all([
+      User.GetOne({ id: this.userId }),
+      DatasetAnnotationGroup.GetManyByDatasetId(this.id, requesterUserId),
+      this.permissionsForUser(requesterUserId),
+    ]);
     const {
       id,
       createdAt,
@@ -207,24 +208,12 @@ export class Dataset extends BaseEntity {
       previewUrls,
       fileCount,
       private: this.private,
-      permissions: {
-        read: this.hasReadPermission(requesterUserId),
-        write: this.hasWritePermission(requesterUserId),
-        admin: this.hasWritePermission(requesterUserId),
-      },
+      permissions,
       metadata,
       annotated: annotationGroups.length > 0,
     };
   }

-  hasReadPermission(userId: number) {
-    return this.userId === userId || !this.private;
-  }
-
-  hasWritePermission(userId: number) {
-    return this.userId === userId;
-  }
-
   static GetOne(opts: FindConditions<Dataset> | string) {
     const conditions = typeof opts === 'string' ? { id: opts } : opts;
     return Dataset.findOne({ ...conditions, deleted: false });
@@ -253,7 +242,7 @@ export class Dataset extends BaseEntity {
     return dataset;
   }

-  static async SortBy(datasets: Dataset[], sortMethod: SortMethod) {
+  static SortBy(datasets: Dataset[], sortMethod: SortMethod) {
     switch (sortMethod) {
       case 'name':
         return _.sortBy(datasets, dataset => dataset.name);
@@ -389,4 +378,8 @@ export class Dataset extends BaseEntity {
       );
     }
   }
+
+  protected getSharableResourceType() {
+    return ResourceType.DATASET;
+  }
 }

diff --git a/entity/datasets/DatasetAnnotationGroup.ts b/entity/datasets/DatasetAnnotationGroup.ts
--- a/entity/datasets/DatasetAnnotationGroup.ts
+++ b/entity/datasets/DatasetAnnotationGroup.ts
@@ -463,7 +463,7 @@ export class DatasetAnnotationGroup extends BaseEntity {
     if (!dataset) {
       throw new InvalidArgumentError('Dataset does not exist');
     }
-    if (!dataset.hasReadPermission(userId)) {
+    if (!(await dataset.userHasReadPermission(userId))) {
       throw new UnauthorizedError(
         'You need to have read permission to this dataset to create a new annotation group.'
       );

diff --git a/entity/models/Model.ts b/entity/models/Model.ts
--- a/entity/models/Model.ts
+++ b/entity/models/Model.ts
@@ -1,6 +1,5 @@
 import * as _ from 'lodash';
 import {
-  BaseEntity,
   Column,
   CreateDateColumn,
   Entity,
@@ -18,11 +17,11 @@ import {
   removeSpaceDash,
   validateModelName,
 } from '../../lib/utils';
+import { ResourceType } from '../ResourcePermission';
+import { SharableResourceEntity } from '../SharableResourceEntity';
 import { User } from '../users/User';
-import { UserMembership } from '../users/UserMembership';
 import { ModelCheckpoint } from './ModelCheckpoint';
 import { ModelImage } from './ModelImage';
-import { ModelPermission, ModelPermissionMode } from './ModelPermission';
 import { ModelVersion } from './ModelVersion';

 type ModelResource = {
@@ -62,7 +61,7 @@ const TYPE_TO_MODEL = {
 };

 @Entity({ name: 'models' })
-export class Model extends BaseEntity {
+export class Model extends SharableResourceEntity {
   @PrimaryGeneratedColumn()
   id: number;

@@ -165,7 +164,7 @@ export class Model extends BaseEntity {
   async files(userId: number, includeUnkept = false) {
     const opts: any = { modelId: this.id, deleted: false, keep: true };
     if (includeUnkept) delete opts.keep;
-    if (await this.userHasPermission(userId, ModelPermissionMode.Read)) {
+    if (await this.userHasReadPermission(userId)) {
       return ModelCheckpoint.find({
         where: [
           { ...opts, private: false },
@@ -177,39 +176,6 @@ export class Model extends BaseEntity {
     return [];
   }

-  async userHasPermission(userId: number, mode: ModelPermissionMode) {
-    // user is owner
-    if (this.userId === userId) return true;
-    const modelOwner = await User.GetOne(this.userId);
-    if (!modelOwner) return false;
-    if (modelOwner.isOrganization) {
-      const membership = await UserMembership.findOne({
-        organizationId: modelOwner.id,
-        userId,
-        active: true,
-      });
-      // owner is org and user is member
-      if (membership && membership.toModelPermission() >= mode) return true;
-    }
-    // user has direct permission
-    const permission = await ModelPermission.findOne({
-      userId,
-      modelId: this.id,
-    });
-    if (permission && permission.mode >= mode) return true;
-    // model is public
-    if (!this.private) return mode === ModelPermissionMode.Read;
-    return false;
-  }
-
-  async permissionsForUser(userId: number) {
-    return {
-      read: await this.userHasPermission(userId, ModelPermissionMode.Read),
-      write: await this.userHasPermission(userId, ModelPermissionMode.Write),
-      admin: await this.userHasPermission(userId, ModelPermissionMode.Admin),
-    };
-  }
-
   isLinkedToGithub() {
     return !!(
       this.githubRepositoryId &&
@@ -444,7 +410,28 @@ export class Model extends BaseEntity {
       managed,
       objectDetectionCategories,
     } = this;
-    const permissions = await this.permissionsForUser(userId);
+    const [
+      permissions,
+      modelOptions,
+      defaultVersionSummary,
+      parentSummary,
+      rootSummary,
+    ] = await Promise.all([
+      this.permissionsForUser(userId),
+      (() =>
+        defaultVersion
+          ? defaultVersion.augmentOptionsWithFiles(
+              defaultVersion.options,
+              userId
+            )
+          : null)(),
+      (() =>
+        defaultVersion
+          ? defaultVersion.summary(userId, appVersion, false)
+          : null)(),
+      (() => (parent ? parent.summary() : null))(),
+      (() => (root ? root.summary() : null))(),
+    ]);
     const toReturn: any = {
       id,
       createdAt,
@@ -465,17 +452,10 @@ export class Model extends BaseEntity {
       numberOfRuns,
       featuredImage,
       permissions,
-      defaultVersion: defaultVersion
-        ? await defaultVersion.summary(userId, appVersion, false)
-        : null,
+      defaultVersion: defaultVersionSummary,
       private: this.private,
       commands: defaultVersion ? defaultVersion.commands : null,
-      options: defaultVersion
-        ? await defaultVersion.augmentOptionsWithFiles(
-            defaultVersion.options,
-            userId
-          )
-        : null,
+      options: modelOptions,
       cpuDockerImage: defaultVersion ? defaultVersion.cpuDockerImage : null,
       gpuDockerImage: defaultVersion ? defaultVersion.gpuDockerImage : null,
       cpuSupported: defaultVersion ? !!defaultVersion.cpuDockerImage : false,
@@ -490,11 +470,13 @@ export class Model extends BaseEntity {
       versionUpdated: defaultVersion
         ? defaultVersion.createdAt
         : this.createdAt,
-      parent: parent ? await parent.summary() : null,
-      root: root ? await root.summary() : null,
+      parent: parentSummary,
+      root: rootSummary,
       managed,
       objectDetectionCategories,
     };
+    // @ts-ignore for some reason the tsc compiler thinks permissions is of an unknown type
+    // VSCode can infer its type just fine though...
     if (permissions.write) {
       toReturn.versions = await this.versionsSummary(userId, appVersion);
       if (this.isLinkedToGithub()) {
@@ -689,4 +671,8 @@ export class Model extends BaseEntity {
     this.name = newName;
     await this.save();
   }
+
+  protected getSharableResourceType() {
+    return ResourceType.MODEL;
+  }
 }

diff --git a/entity/models/ModelSession.ts b/entity/models/ModelSession.ts
--- a/entity/models/ModelSession.ts
+++ b/entity/models/ModelSession.ts
@@ -47,7 +47,6 @@ import { TrainingExperiment } from '../training/TrainingExperiment';
 import { User } from '../users/User';
 import { RUNWAY_ORG_ID, UserMembership } from '../users/UserMembership';
 import { Model } from './Model';
-import { ModelPermissionMode } from './ModelPermission';
 import { ModelVersion } from './ModelVersion';

 export interface ModelSessionConfig {
@@ -596,7 +595,7 @@ export class ModelSession extends BaseEntity {
       };
     }

-    if (!(await model.userHasPermission(user.id, ModelPermissionMode.Read))) {
+    if (!(await model.userHasReadPermission(user.id))) {
       return {
         error: {
           status: 401,

diff --git a/entity/users/User.ts b/entity/users/User.ts
--- a/entity/users/User.ts
+++ b/entity/users/User.ts
@@ -659,4 +659,17 @@ export class User extends BaseEntity {
       );
     }
   }
+
+  static async CreateOrgUser({ name, email }: { name: string; email: string }) {
+    const user = new User();
+    user.username = name;
+    user.email = email.toLowerCase();
+    user.isOrganization = true;
+    user.isVerified = true;
+    user.generateVerificationCode();
+    user.setPassword(uuid());
+    user.gpuCredits = 0;
+    await user.save();
+    return user;
+  }
 }

diff --git a/entity/users/UserMembership.ts b/entity/users/UserMembership.ts
--- a/entity/users/UserMembership.ts
+++ b/entity/users/UserMembership.ts
@@ -7,7 +7,9 @@ import {
   UpdateDateColumn,
 } from 'typeorm';

-import { ModelPermissionMode } from '../models/ModelPermission';
+import { NotFoundError, UnauthorizedError } from '../../lib/errors';
+import { ResourcePermissionMode } from '../ResourcePermission';
+import { User } from './User';

 export const RUNWAY_ORG_ID = 173;
 // Grants users access to unlimited training, without a plan. Should be used only for the
@@ -21,10 +23,16 @@ export enum MembershipRole {
   Admin,
 }

+export enum MembershipRoleName {
+  Member = 'member',
+  Admin = 'admin',
+}
+
 export interface UserMembershipConfig {
   userId: number;
   organizationId: number;
   organizationRole: MembershipRole;
+  createdBy: number;
 }

 @Entity({ name: 'user_memberships' })
@@ -41,6 +49,9 @@ export class UserMembership extends BaseEntity {
   @Column()
   userId: number;

+  @Column()
+  createdBy: number;
+
   @Column()
   organizationId: number;

@@ -50,19 +61,25 @@ export class UserMembership extends BaseEntity {
   @Column()
   active: boolean;

-  toModelPermission() {
-    if (this.organizationRole === MembershipRole.Member) {
-      return ModelPermissionMode.Write;
+  toResourcePermission() {
+    if (this.organizationRole === MembershipRole.Admin) {
+      return ResourcePermissionMode.Admin;
     } else {
-      return ModelPermissionMode.Admin;
+      return ResourcePermissionMode.Write;
     }
   }

+  async setRole(role: MembershipRole) {
+    this.organizationRole = role;
+    await this.save();
+  }
+
   static async Create(config: UserMembershipConfig) {
     const membership = new UserMembership();
     membership.userId = config.userId;
     membership.organizationId = config.organizationId;
     membership.organizationRole = config.organizationRole;
+    membership.createdBy = config.createdBy;
     membership.active = true;
     await membership.save();
     return membership;
@@ -95,6 +112,21 @@ export class UserMembership extends BaseEntity {
     );
   }

+  static async GetOrgMemberRolePairs(
+    organizationId: number
+  ): Promise<{ user: User; role: MembershipRole }[]> {
+    const memberships = await UserMembership.find({
+      organizationId,
+      active: true,
+    });
+    return Promise.all(
+      memberships.map(async membership => ({
+        user: await User.GetOne(membership.userId),
+        role: membership.organizationRole,
+      }))
+    );
+  }
+
   // Given a user ID, get the user IDs for members of all orgs that user is a part of
   static async GetPeerMemberIds(userId: number): Promise<number[]> {
     const orgs = await UserMembership.GetUserOrgIds(userId);
@@ -103,4 +135,53 @@ export class UserMembership extends BaseEntity {
     );
     return [].concat(...orgMembers);
   }
+
+  static async FailIfNotActiveMember(
+    userId: number,
+    organizationId: number,
+    role?: MembershipRole
+  ) {
+    const isActiveMember = await UserMembership.IsActiveMember(
+      userId,
+      organizationId,
+      role
+    );
+    if (!isActiveMember) throw new UnauthorizedError();
+  }
+
+  static MembershipRoleName(role: MembershipRole): MembershipRoleName {
+    switch (role) {
+      case MembershipRole.Admin:
+        return MembershipRoleName.Admin;
+      case MembershipRole.Member:
+        return MembershipRoleName.Member;
+      default:
+        throw new Error(`Invalid membership role: ${role}`);
+    }
+  }
+
+  static MembershipRole(roleName: MembershipRoleName): MembershipRole {
+    switch (roleName) {
+      case MembershipRoleName.Admin:
+        return MembershipRole.Admin;
+      case MembershipRoleName.Member:
+        return MembershipRole.Member;
+      default:
+        throw new Error(`Invalid membership role name: ${roleName}`);
+    }
+  }
+
+  static async Inactivate(
+    userId: number,
+    organizationId: number
+  ): Promise<void> {
+    const member = await UserMembership.findOne({
+      userId,
+      organizationId,
+      active: true,
+    });
+    if (!member) throw new NotFoundError();
+    member.active = false;
+    await member.save();
+  }
 }

diff --git a/index.ts b/index.ts
--- a/index.ts
+++ b/index.ts
@@ -20,7 +20,9 @@ import modelSessionRoutes from './controllers/model_sessions';
 import modelVersionRoutes from './controllers/model_versions';
 import modelRoutes from './controllers/models';
 import organizationRoutes from './controllers/organizations';
+import resourcePermissionRoutes from './controllers/resource_permissions';
 import taskRoutes from './controllers/tasks';
+import teamRoutes from './controllers/teams';
 import trainingRoutes from './controllers/training';
 import uploadRoutes from './controllers/uploads';
 import userRoutes from './controllers/users';
@@ -214,12 +216,14 @@ export class Server {
     logger.token('user-id', req =>
       req.user ? `user-${req.user.id}` : `anonymous`
     );
-    this.express.use(
-      logger(
-        // Apache common format replacing :remote-user with :user-id
-        `:remote-addr - :user-id [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length]`
-      )
-    );
+    if (process.env['HTTP_LOGS'] !== '0') {
+      this.express.use(
+        logger(
+          // Apache common format replacing :remote-user with :user-id
+          `:remote-addr - :user-id [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length]`
+        )
+      );
+    }
   }

   private routes(): void {
@@ -238,6 +242,8 @@ export class Server {
     this.express.use('/', endpointRoutes());
     this.express.use('/', assetRoutes());
     this.express.use('/', annotationRoutes());
+    this.express.use('/', teamRoutes());
+    this.express.use('/', resourcePermissionRoutes());
     // eslint-disable-next-line @typescript-eslint/no-unused-vars
     this.express.use((err, _, res, _2) => {
       console.error(err.stack);

diff --git a/lib/utils.ts b/lib/utils.ts
--- a/lib/utils.ts
+++ b/lib/utils.ts
@@ -13,6 +13,7 @@ import {
   createConnection,
   getConnection,
   getManager,
+  getRepository,
 } from 'typeorm';
 import * as uuid from 'uuid';

@@ -33,12 +34,12 @@ import { Model } from '../entity/models/Model';
 import { ModelBuild } from '../entity/models/ModelBuild';
 import { ModelCheckpoint } from '../entity/models/ModelCheckpoint';
 import { ModelImage } from '../entity/models/ModelImage';
-import { ModelPermission } from '../entity/models/ModelPermission';
 import { ModelRating } from '../entity/models/ModelRating';
 import { ModelSession } from '../entity/models/ModelSession';
 import { ModelVersion } from '../entity/models/ModelVersion';
 import { Payment } from '../entity/payments/Payment';
 import { PayPalPaymentsUserInfo } from '../entity/payments/PayPalPaymentsUserInfo';
+import { ResourcePermission } from '../entity/ResourcePermission';
 import { S3ObjectDeletion } from '../entity/S3ObjectDeletion';
 import { Setting } from '../entity/Setting';
 import { Task } from '../entity/tasks/Task';
@@ -123,7 +124,7 @@ export const establishDatabaseConnection = async (
     ModelImage,
     ModelSession,
     ModelBuild,
-    ModelPermission,
+    ResourcePermission,
     Setting,
     CouponCode,
     CouponCodeRedemption,
@@ -576,3 +577,13 @@ export const randomABTestIsLessThanPercent = (percent: number): boolean => {
 export const randomABTest = (): boolean => {
   return randomABTestIsLessThanPercent(0.5);
 };
+
+export const usernameExists = async (username: string): Promise<boolean> => {
+  const existingWithUsername = await getRepository(User)
+    .createQueryBuilder()
+    .where('LOWER(username) = :username', {
+      username: username.toLowerCase(),
+    })
+    .getOne();
+  return !!existingWithUsername;
+};

diff --git a/migrations/1596036965905-AddResourcePermissionsTable.ts b/migrations/1596036965905-AddResourcePermissionsTable.ts
--- a/migrations/1596036965905-AddResourcePermissionsTable.ts
+++ b/migrations/1596036965905-AddResourcePermissionsTable.ts
@@ -0,0 +1,80 @@
+import { MigrationInterface, QueryRunner } from 'typeorm';
+
+import { ModelPermission } from '../entity/models/ModelPermission';
+import { ResourceType } from '../entity/ResourcePermission';
+
+export class AddResourcePermissionsTable1596036965905
+  implements MigrationInterface {
+  public async up(queryRunner: QueryRunner): Promise<any> {
+    await queryRunner.query(`
+        CREATE TABLE "resource_permissions"
+        (
+            "id" uuid NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
+            "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
+            "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
+            "userId" integer NOT NULL REFERENCES "users"("id"),
+            "resourceId" varchar NOT NULL,
+            "resourceType" varchar NOT NULL,
+            "mode" integer NOT NULL,
+            "deleted" bool NOT NULL DEFAULT false,
+            UNIQUE("userId", "resourceId", "resourceType", "deleted")
+        )
+    `);
+
+    await queryRunner.query(
+      `CREATE INDEX "RESOURCE_PERMISSION_USER_ID" ON "resource_permissions" ("userId")`
+    );
+
+    await queryRunner.commitTransaction();
+    await queryRunner.startTransaction();
+
+    const modelPermissions = await ModelPermission.find();
+    for (const modelPermission of modelPermissions) {
+      // We can't use ModelPermission.Create() here because that code has been updated to
+      // include changes from later migrations. Instead we create the rows manually.
+      await queryRunner.query(
+        `
+        INSERT INTO "resource_permissions"
+        (
+          "createdAt",
+          "updatedAt",
+          "userId",
+          "resourceId",
+          "resourceType",
+          "mode",
+          "deleted"
+        )
+        VALUES
+        (
+          DEFAULT,
+          DEFAULT,
+          $1,
+          $2,
+          $3,
+          $4,
+          DEFAULT
+        )
+      `,
+        [
+          modelPermission.userId,
+          modelPermission.modelId.toString(),
+          ResourceType.MODEL,
+          modelPermission.mode,
+        ]
+      );
+
+      queryRunner.query(
+        `UPDATE "resource_permissions" SET "createdAt" = $1, "updatedAt" = $2 WHERE "resourceId" = $3`,
+        [
+          modelPermission.createdAt,
+          modelPermission.updatedAt,
+          modelPermission.modelId,
+        ]
+      );
+    }
+  }
+
+  public async down(queryRunner: QueryRunner): Promise<any> {
+    await queryRunner.query(`DROP TABLE "resource_permissions"`);
+  }
+}

diff --git a/migrations/1596480111109-AddCreatedByColumnToUserMembershipsTable.ts b/migrations/1596480111109-AddCreatedByColumnToUserMembershipsTable.ts
--- a/migrations/1596480111109-AddCreatedByColumnToUserMembershipsTable.ts
+++ b/migrations/1596480111109-AddCreatedByColumnToUserMembershipsTable.ts
@@ -0,0 +1,31 @@
+import { MigrationInterface, QueryRunner } from 'typeorm';
+
+export class AddCreatedByColumnToUserMembershipsTable1596480111109
+  implements MigrationInterface {
+  public async up(queryRunner: QueryRunner): Promise<any> {
+    await queryRunner.query(
+      `ALTER TABLE "user_memberships" ADD COLUMN "createdBy" INTEGER NOT NULL REFERENCES "users" ("id") DEFAULT 173`
+    );
+    //// TODO: COME BACK AND UNCOMMENT THIS BEFORE MERGE. THIS IS TEMPORARILY REMOVED FOR
+    //// BACKWARDS COMPATIBILITY WITH OTHER STAGE BRANCHES
+    // await queryRunner.query(
+    //   `ALTER TABLE "user_memberships" ALTER COLUMN "createdBy" DROP DEFAULT`
+    // );
+
+    await queryRunner.query(
+      `CREATE INDEX "USER_MEMBERSHIPS_CREATED_BY" ON "user_memberships" ("createdBy")`
+    );
+
+    await queryRunner.query(
+      `CREATE INDEX "USER_MEMBERSHIPS_USER_ID_CREATED_BY" ON "user_memberships" ("userId", "createdBy")`
+    );
+  }
+
+  public async down(queryRunner: QueryRunner): Promise<any> {
+    await queryRunner.query(`DROP INDEX "USER_MEMBERSHIPS_CREATED_BY"`);
+    await queryRunner.query(`DROP INDEX "USER_MEMBERSHIPS_USER_ID_CREATED_BY"`);
+    await queryRunner.query(
+      `ALTER TABLE "user_memberships" DROP COLUMN "createdBy"`
+    );
+  }
+}

diff --git a/migrations/1596481925121-AddCreatedByColumnToResourcePermissionTable.ts b/migrations/1596481925121-AddCreatedByColumnToResourcePermissionTable.ts
--- a/migrations/1596481925121-AddCreatedByColumnToResourcePermissionTable.ts
+++ b/migrations/1596481925121-AddCreatedByColumnToResourcePermissionTable.ts
@@ -0,0 +1,34 @@
+import { MigrationInterface, QueryRunner } from 'typeorm';
+
+export class AddCreatedByColumnToResourcePermissionTable1596481925121
+  implements MigrationInterface {
+  public async up(queryRunner: QueryRunner): Promise<any> {
+    await queryRunner.query(
+      `ALTER TABLE "resource_permissions" ADD COLUMN "createdBy" INTEGER NOT NULL REFERENCES "users" ("id") DEFAULT 173`
+    );
+
+    //// TODO: COME BACK AND UNCOMMENT THIS BEFORE MERGE. THIS IS TEMPORARILY REMOVED FOR
+    //// BACKWARDS COMPATIBILITY WITH OTHER STAGE BRANCHES
+    // await queryRunner.query(
+    //   `ALTER TABLE "resource_permissions" ALTER COLUMN "createdBy" DROP DEFAULT`
+    // );
+
+    await queryRunner.query(
+      `CREATE INDEX "RESOURCE_PERMISSIONS_CREATED_BY" ON "resource_permissions" ("createdBy")`
+    );
+
+    await queryRunner.query(
+      `CREATE INDEX "RESOURCE_PERMISSIONS_USER_ID_CREATED_BY" ON "resource_permissions" ("userId", "createdBy")`
+    );
+  }
+
+  public async down(queryRunner: QueryRunner): Promise<any> {
+    await queryRunner.query(`DROP INDEX "RESOURCE_PERMISSIONS_CREATED_BY"`);
+    await queryRunner.query(
+      `DROP INDEX "RESOURCE_PERMISSIONS_USER_ID_CREATED_BY"`
+    );
+    await queryRunner.query(
+      `ALTER TABLE "resource_permissions" DROP COLUMN "createdBy"`
+    );
+  }
+}

diff --git a/migrations/1597249158961-UpdateUniqueEmailConstraintToAllowDuplicateEmailsForOrganizations.ts b/migrations/1597249158961-UpdateUniqueEmailConstraintToAllowDuplicateEmailsForOrganizations.ts
--- a/migrations/1597249158961-UpdateUniqueEmailConstraintToAllowDuplicateEmailsForOrganizations.ts
+++ b/migrations/1597249158961-UpdateUniqueEmailConstraintToAllowDuplicateEmailsForOrganizations.ts
@@ -0,0 +1,22 @@
+import { MigrationInterface, QueryRunner } from 'typeorm';
+
+export class UpdateUniqueEmailConstraintToAllowDuplicateEmailsForOrganizations1597249158961
+  implements MigrationInterface {
+  public async up(queryRunner: QueryRunner): Promise<any> {
+    await queryRunner.query(
+      `ALTER TABLE "users" DROP CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3"`
+    );
+    await queryRunner.query(
+      `CREATE UNIQUE INDEX "USERS_UNIQUE_LOWERCASE_EMAIL_NOT_IS_ORGANIZATION" ON "users" (lower(("email")::text)) WHERE "isOrganization" = FALSE;`
+    );
+  }
+
+  public async down(queryRunner: QueryRunner): Promise<any> {
+    await queryRunner.query(
+      `DROP INDEX "USERS_UNIQUE_LOWERCASE_EMAIL_NOT_IS_ORGANIZATION"`
+    );
+    await queryRunner.query(
+      `ALTER TABLE "users" ADD CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email")`
+    );
+  }
+}

diff --git a/package.json b/package.json
--- a/package.json
+++ b/package.json
@@ -6,6 +6,7 @@
   "scripts": {
     "build": "tsc",
     "test": "STAGE=dev AUTOMATED_TEST=1 nyc mocha --file tests/before_tests.ts --timeout 10000 tests/**/*.ts",
+    "test-file": "STAGE=dev AUTOMATED_TEST=1 nyc mocha --file tests/before_tests.ts --timeout 10000",
     "test-stage": "STAGE=stage AUTOMATED_TEST=1 nyc mocha --file tests/before_tests.ts --timeout 10000 tests/**/*.ts",
     "test-e2e": "STAGE=dev AUTOMATED_TEST=1 AUTOMATED_TEST_E2E=1 nyc mocha --file tests/before_tests.ts --timeout 10000 tests/**/*.ts",
     "test-e2e-stage": "STAGE=stage AUTOMATED_TEST=1 AUTOMATED_TEST_E2E=1 nyc mocha --file tests/before_tests.ts --timeout 10000 tests/**/*.ts",

diff --git a/scripts/add_emails_to_web_beta.ts b/scripts/add_emails_to_web_beta.ts
--- a/scripts/add_emails_to_web_beta.ts
+++ b/scripts/add_emails_to_web_beta.ts
@@ -7,6 +7,7 @@ import { BetaAccessList } from '../entity/users/BetaAccessList';
 import { User } from '../entity/users/User';
 import {
   MembershipRole,
+  RUNWAY_ORG_ID,
   RUNWAY_WEB_BETA_ACCESS_ORG_ID,
   UserMembership,
 } from '../entity/users/UserMembership';
@@ -75,6 +76,7 @@ const main = async () => {
         userId: existingUser.id,
         organizationId: RUNWAY_WEB_BETA_ACCESS_ORG_ID,
         organizationRole: MembershipRole.Member,
+        createdBy: RUNWAY_ORG_ID,
       });
     } else {
       console.log(`Skipping ${email} as it is already in the web beta org`);

diff --git a/tests/SharableResourceClient.ts b/tests/SharableResourceClient.ts
--- a/tests/SharableResourceClient.ts
+++ b/tests/SharableResourceClient.ts
@@ -0,0 +1,158 @@
+/**
+ * This module provides a light abstraction around CRUD-related REST API calls for routes
+ * which manage SharableResources. This allows us to run the same permission checking
+ * tests suite (see tests/integratin/teams.ts) for all sharable resources, even though
+ * each maintains a different set of routes (e.g. /v1/models, `/v1/datasets`, etc.)
+ */
+import * as request from 'supertest';
+
+import { ResourceType } from '../entity/ResourcePermission';
+import { getTestApi } from './test_utils';
+
+export const getSharableResourceClientGenerator = (
+  configs: SharableResourceClientConfig[]
+) => {
+  const clients = configs.map(config => new SharableResourceClient(config));
+  function* clientGenerator() {
+    for (const client of clients) {
+      console.log(
+        `Using sharable resource client [resource type: ${client.getType()}${
+          client.params.id ? ', id:' + client.params.id : ''
+        }]`
+      );
+      yield client;
+    }
+  }
+  return clientGenerator;
+};
+
+export type HTTPMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
+export type APIAction = {
+  path: string;
+  method: HTTPMethod;
+  data?: { [key: string]: any } | (() => { [key: string]: any });
+  before?: (args: RequestArguments) => Promise<void>;
+  after?: (args: RequestArguments) => Promise<void>;
+  expectSuccessStatusCode?: number;
+  expectFailureStatusCode?: number;
+};
+export type SharableResourceClientConfig = {
+  resourceType: ResourceType;
+  actions: {
+    create: APIAction;
+    read: APIAction;
+    update: APIAction;
+    delete: APIAction;
+    list: APIAction;
+    transferOwnership?: APIAction;
+  };
+};
+export type RequestArguments = {
+  token: string;
+  expectSuccess: boolean;
+  callback?: (response: request.Response) => Promise<void>;
+};
+
+export class SharableResourceClient {
+  public params: { [name: string]: string };
+  private api;
+  private config: SharableResourceClientConfig;
+  constructor(config: SharableResourceClientConfig) {
+    this.config = config;
+    this.params = {};
+    this.api = getTestApi();
+  }
+
+  create(args: RequestArguments) {
+    return this.query(this.config.actions.create, args);
+  }
+
+  read(args: RequestArguments) {
+    return this.query(this.config.actions.read, args);
+  }
+
+  update(args: RequestArguments) {
+    return this.query(this.config.actions.update, args);
+  }
+
+  delete(args: RequestArguments) {
+    return this.query(this.config.actions.delete, args);
+  }
+
+  list(args: RequestArguments) {
+    return this.query(this.config.actions.list, args);
+  }
+
+  transferOwnership(args: RequestArguments) {
+    return this.query(this.config.actions.transferOwnership, args);
+  }
+
+  getType(): ResourceType {
+    return this.config.resourceType;
+  }
+
+  private async query(template: APIAction, args: RequestArguments) {
+    if (template.before) await template.before(args);
+    const response = await this.test(template, args);
+    if (template.after) await template.after(args);
+    return response;
+  }
+
+  private async test(
+    template: APIAction,
+    args: RequestArguments
+  ): Promise<request.Response> {
+    const api = await this.api;
+    const supertest = request(api);
+    const path = this.replacePathParams(template.path);
+    const statusCode = args.expectSuccess
+      ? template.expectSuccessStatusCode || 200
+      : template.expectFailureStatusCode || 401;
+    let test = this.method(supertest, template.method)(path);
+    test = this.auth(test, args.token);
+    test = this.body(test, template.data);
+    test = this.expect(test, statusCode);
+    return new Promise(resolve => test.then(resolve));
+  }
+
+  private method(
+    test: request.SuperTest<request.Test>,
+    methodName: HTTPMethod
+  ) {
+    switch (methodName) {
+      case 'GET':
+        return test.get;
+      case 'POST':
+        return test.post;
+      case 'PUT':
+        return test.put;
+      case 'DELETE':
+        return test.delete;
+      case 'PATCH':
+        return test.patch;
+      default:
+        throw new Error(`Invalid methodName: ${methodName}`);
+    }
+  }
+
+  private auth(test: request.Test, token: string) {
+    return test.set('Authorization', `Bearer ${token}`);
+  }
+
+  private body(
+    test: request.Test,
+    data: { [key: string]: any } | (() => { [key: string]: any })
+  ) {
+    const body = typeof data === 'function' ? data() : data;
+    return test.send(body);
+  }
+
+  private expect(test: request.Test, expect: number) {
+    return test.expect(expect);
+  }
+
+  private replacePathParams(path: string) {
+    // We can do this nicer if we use more params eventually
+    return path.replace(':id', this.params.id);
+  }
+}

diff --git a/tests/integration/messages.ts b/tests/integration/messages.ts
--- a/tests/integration/messages.ts
+++ b/tests/integration/messages.ts
@@ -55,14 +55,12 @@ describe('integration/messages', async function() {
     before(async function() {
       // Temporarily add the test user to the Runway org. We can't do this via
       // UserMembership.Create() because of DB connection issues :(
-      await getConnection()
-        .getRepository(UserMembership)
-        .insert({
-          userId: testUserId,
-          organizationId: RUNWAY_ORG_ID,
-          organizationRole: MembershipRole.Member,
-          active: true,
-        });
+      await UserMembership.Create({
+        userId: testUserId,
+        organizationId: RUNWAY_ORG_ID,
+        organizationRole: MembershipRole.Member,
+        createdBy: RUNWAY_ORG_ID,
+      });
     });

     after(async function() {

diff --git a/tests/integration/teams.ts b/tests/integration/teams.ts
--- a/tests/integration/teams.ts
+++ b/tests/integration/teams.ts
@@ -0,0 +1,894 @@
+import { assert } from 'chai';
+import * as request from 'supertest';
+import * as uuid from 'uuid';
+
+import { ResourceType } from '../../entity/ResourcePermission';
+import { MembershipRoleName } from '../../entity/users/UserMembership';
+import {
+  getSharableResourceClientGenerator,
+  RequestArguments,
+  SharableResourceClientConfig,
+} from '../SharableResourceClient';
+import {
+  assertResourceInListResponse,
+  assertValidResourcePermission,
+  getTestApi,
+  getTestUserToken,
+  registerNewTestUser,
+  STYLEGAN_ID,
+  TestUserInfo,
+  uploadFileToS3UsingPresignedPOST,
+} from '../test_utils';
+
+describe('integration/permissions', async function() {
+  enum User {
+    ADMIN_MEMBER = 'ADMIN_MEMBER',
+    ADMIN_PEER_MEMBER = 'ADMIN_PEER_MEMBER',
+    RESOURCE_OWNER = 'RESOURCE_OWNER',
+    PEER_MEMBER = 'PEER_MEMBER',
+    PAST_MEMBER = 'PAST_MEMBER',
+    RANDOM_USER = 'RANDOM_USER',
+  }
+  const users: {
+    [key: string]: {
+      user: TestUserInfo;
+      token: string;
+    };
+  } = {};
+
+  const allMemberKeys = [
+    User.ADMIN_MEMBER,
+    User.ADMIN_PEER_MEMBER,
+    User.PEER_MEMBER,
+    User.RESOURCE_OWNER,
+  ];
+  const allMemberKeysExcludingResourceOwner = [
+    User.ADMIN_MEMBER,
+    User.ADMIN_PEER_MEMBER,
+    User.PEER_MEMBER,
+  ];
+  const adminMemberKeys = [User.ADMIN_MEMBER, User.ADMIN_PEER_MEMBER];
+  const regularMemberKeys = [User.RESOURCE_OWNER, User.PEER_MEMBER];
+
+  let datasetUploadUrl;
+  const sharableResourceClientConfigs: SharableResourceClientConfig[] = [
+    {
+      resourceType: ResourceType.MODEL,
+      actions: {
+        create: {
+          path: `/v1/models/${STYLEGAN_ID}/clone`,
+          method: 'POST',
+        },
+        read: {
+          path: `/v1/models/:id`,
+          method: 'GET',
+        },
+        list: {
+          path: `/v1/models`,
+          method: 'GET',
+        },
+        update: {
+          path: `/v1/models/:id`,
+          method: 'PUT',
+          data: () => {
+            const name = `New-Resource-Name-${uuid().split('-')[0]}`;
+            return { model: { name } };
+          },
+        },
+        delete: {
+          path: `/v1/models/:id`,
+          method: 'DELETE',
+        },
+      },
+    },
+    {
+      resourceType: ResourceType.DATASET,
+      actions: {
+        create: {
+          path: `/v1/datasets`,
+          method: 'POST',
+          before: async (args: RequestArguments) => {
+            await request(api)
+              .post('/v1/datasets/upload')
+              .set('Authorization', `Bearer ${args.token}`)
+              .expect(200)
+              .then(response => {
+                const { uploadUrl } = response.body;
+                datasetUploadUrl = uploadUrl;
+              });
+            const file = `${__dirname}/../data/test-dataset.tar`;
+            await uploadFileToS3UsingPresignedPOST(file, datasetUploadUrl);
+          },
+          data: () => {
+            return {
+              name: `Test dataset ${uuid()}`,
+              type: { name: 'images', type: 'images' },
+              filename: datasetUploadUrl.fields.key,
+              previewFilenames: [],
+              fileCount: 1000,
+              description:
+                'A dataset created via automated API tests to check permissions.',
+            };
+          },
+        },
+        read: {
+          path: `/v1/datasets/:id`,
+          method: 'GET',
+          expectFailureStatusCode: 404,
+        },
+        list: {
+          path: `/v1/datasets`,
+          method: 'GET',
+        },
+        update: {
+          path: `/v1/datasets/:id`,
+          method: 'PUT',
+          data: () => {
+            const name = `New-Resource-Name-${uuid().split('-')[0]}`;
+            return { name };
+          },
+          expectFailureStatusCode: 404,
+        },
+        delete: {
+          path: `/v1/datasets/:id`,
+          method: 'DELETE',
+          expectFailureStatusCode: 404,
+        },
+      },
+    },
+  ];
+
+  const sharableResources = getSharableResourceClientGenerator(
+    sharableResourceClientConfigs
+  );
+
+  let api;
+  let teamId: number;
+
+  function getToken(user: User): string {
+    return users[user].token;
+  }
+
+  function getUser(user: User): TestUserInfo {
+    return users[user].user;
+  }
+
+  before(async function() {
+    api = await getTestApi();
+    for (const key in User) {
+      const user = await registerNewTestUser();
+      const token = await getTestUserToken(user);
+      users[key] = {
+        user,
+        token,
+      };
+    }
+  });
+
+  describe('setup teams', function() {
+    const originalTeamName = `Test-Team-${uuid()}`;
+    const newTeamName = `Test-Team-${uuid()}`;
+
+    it('ADMIN_MEMBER should create a new team', async function() {
+      await request(api)
+        .post('/v1/teams')
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_MEMBER)}`)
+        .send({ name: originalTeamName })
+        .expect(200)
+        .then(response => {
+          teamId = response.body.team.id;
+          assert.isNumber(teamId);
+        });
+    });
+
+    it('ADMIN_MEMBER should see the new team appear in their teams list', async function() {
+      await request(api)
+        .get('/v1/teams')
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_MEMBER)}`)
+        .expect(200)
+        .then(response => {
+          assert.equal(
+            response.body.teams.filter(
+              team => team.username === originalTeamName
+            ).length,
+            1
+          );
+        });
+    });
+
+    it('ADMIN_MEMBER should be an admin for the team they just created', async function() {
+      const adminMember = getUser(User.ADMIN_MEMBER);
+      await request(api)
+        .get(`/v1/teams/${teamId}/members`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_MEMBER)}`)
+        .expect(200)
+        .then(response => {
+          const { members } = response.body;
+          const member = members.find(
+            member => member.user.id === adminMember.id
+          );
+          assert.equal(member.user.id, adminMember.id);
+          assert.equal(member.role, MembershipRoleName.Admin);
+        });
+    });
+
+    it("ADMIN_MEMBER should not be able to add ADMIN_PEER_MEMBER to the team using a role that doesn't exist", async function() {
+      await request(api)
+        .post(`/v1/teams/${teamId}/members`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_MEMBER)}`)
+        .send({ userId: getUser(User.ADMIN_PEER_MEMBER).id, role: 'sudoer' })
+        .expect(400);
+    });
+
+    it('ADMIN_MEMBER should add ADMIN_PEER_MEMBER to the team as an admin', async function() {
+      await request(api)
+        .post(`/v1/teams/${teamId}/members`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_MEMBER)}`)
+        .send({ userId: getUser(User.ADMIN_PEER_MEMBER).id, role: 'admin' })
+        .expect(200)
+        .then(response => {
+          assert.deepEqual(response.body, { success: true });
+        });
+    });
+
+    it("ADMIN_PEER_MEMBER should lower ADMIN_MEMBER's role to member", async function() {
+      await request(api)
+        .put(`/v1/teams/${teamId}/members/${getUser(User.ADMIN_MEMBER).id}`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_PEER_MEMBER)}`)
+        .send({ role: 'member' })
+        .expect(200)
+        .then(response => {
+          assert.deepEqual(response.body, { success: true });
+        });
+    });
+
+    it('ADMIN_MEMBER should appear in the membership list as a regular member', async function() {
+      const adminMember = getUser(User.ADMIN_MEMBER);
+      await request(api)
+        .get(`/v1/teams/${teamId}/members`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_MEMBER)}`)
+        .expect(200)
+        .then(response => {
+          const { members } = response.body;
+          const member = members.find(
+            member => member.user.id === adminMember.id
+          );
+          assert.equal(member.user.id, adminMember.id);
+          assert.equal(member.role, MembershipRoleName.Member);
+        });
+    });
+
+    it('ADMIN_MEMBER should be unable to raise their membership level as a regular user', async function() {
+      await request(api)
+        .put(`/v1/teams/${teamId}/members/${getUser(User.ADMIN_MEMBER).id}`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_MEMBER)}`)
+        .send({ role: 'admin' })
+        .expect(401);
+    });
+
+    it("PEER_MEMBER should be unable to raise ADMIN_MEMBER's membership level as a regular user", async function() {
+      await request(api)
+        .put(`/v1/teams/${teamId}/members/${getUser(User.ADMIN_MEMBER).id}`)
+        .set('Authorization', `Bearer ${getToken(User.PEER_MEMBER)}`)
+        .send({ role: 'admin' })
+        .expect(401);
+    });
+
+    it("ADMIN_PEER_MEMBER should raise ADMIN_MEMBER's role to admin", async function() {
+      await request(api)
+        .put(`/v1/teams/${teamId}/members/${getUser(User.ADMIN_MEMBER).id}`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_PEER_MEMBER)}`)
+        .send({ role: 'admin' })
+        .expect(200)
+        .then(response => {
+          assert.deepEqual(response.body, { success: true });
+        });
+    });
+
+    it('ADMIN_PEER_MEMBER should add RESOURCE_OWNER to the team as a member', async function() {
+      await request(api)
+        .post(`/v1/teams/${teamId}/members`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_PEER_MEMBER)}`)
+        .send({ userId: getUser(User.RESOURCE_OWNER).id, role: 'member' })
+        .expect(200)
+        .then(response => {
+          assert.deepEqual(response.body, { success: true });
+        });
+    });
+
+    it('ADMIN_PEER_MEMBER should add PAST_MEMBER to the team as a member', async function() {
+      await request(api)
+        .post(`/v1/teams/${teamId}/members`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_PEER_MEMBER)}`)
+        .send({ userId: getUser(User.PAST_MEMBER).id, role: 'member' })
+        .expect(200)
+        .then(response => {
+          assert.deepEqual(response.body, { success: true });
+        });
+    });
+
+    it('ADMIN_MEMBER should remove PAST_MEMBER from the team', async function() {
+      await request(api)
+        .delete(`/v1/teams/${teamId}/members/${getUser(User.PAST_MEMBER).id}`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_PEER_MEMBER)}`)
+        .expect(200)
+        .then(response => {
+          assert.deepEqual(response.body, { success: true });
+        });
+    });
+
+    it('ADMIN_MEMBER should re-add PAST_MEMBER to the team as a member', async function() {
+      await request(api)
+        .post(`/v1/teams/${teamId}/members`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_MEMBER)}`)
+        .send({ userId: getUser(User.PAST_MEMBER).id, role: 'member' })
+        .expect(200)
+        .then(response => {
+          assert.deepEqual(response.body, { success: true });
+        });
+    });
+
+    it('PEER_MEMBER should not be able to remove PAST_MEMBER from the team', async function() {
+      await request(api)
+        .delete(`/v1/teams/${teamId}/members/${getUser(User.PAST_MEMBER).id}`)
+        .set('Authorization', `Bearer ${getToken(User.PEER_MEMBER)}`)
+        .expect(401);
+    });
+
+    it('RANDOM_USER should not be able to remove PAST_MEMBER from the team', async function() {
+      await request(api)
+        .delete(`/v1/teams/${teamId}/members/${getUser(User.PAST_MEMBER).id}`)
+        .set('Authorization', `Bearer ${getToken(User.RANDOM_USER)}`)
+        .expect(401);
+    });
+
+    it('PAST_MEMBER should remove themselves from the team as a member', async function() {
+      await request(api)
+        .delete(`/v1/teams/${teamId}/members/${getUser(User.PAST_MEMBER).id}`)
+        .set('Authorization', `Bearer ${getToken(User.PAST_MEMBER)}`)
+        .expect(200);
+    });
+
+    it("RESOURCE_OWNER should not be able to add PEER_MEMBER to the team because they don't have admin permissions", async function() {
+      await request(api)
+        .post(`/v1/teams/${teamId}/members`)
+        .set('Authorization', `Bearer ${getToken(User.RESOURCE_OWNER)}`)
+        .send({ userId: getUser(User.PEER_MEMBER).id, role: 'member' })
+        .expect(401);
+    });
+
+    it('ADMIN_PEER_MEMBER should add PEER_MEMBER to the team', async function() {
+      await request(api)
+        .post(`/v1/teams/${teamId}/members`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_MEMBER)}`)
+        .send({ userId: getUser(User.PEER_MEMBER).id, role: 'member' })
+        .expect(200)
+        .then(response => {
+          assert.deepEqual(response.body, { success: true });
+        });
+    });
+
+    it('RESOURCE_OWNER should see all members in team members list', async function() {
+      await request(api)
+        .get(`/v1/teams/${teamId}/members`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_MEMBER)}`)
+        .expect(200)
+        .then(response => {
+          const { members } = response.body;
+          // Are all members present?
+          assert.equal(members.length, allMemberKeys.length);
+
+          // Are admin members present?
+          const admins = members.filter(
+            member =>
+              allMemberKeys
+                .map(memberKey => getUser(memberKey).id)
+                .includes(member.user.id) &&
+              member.role === MembershipRoleName.Admin
+          );
+          assert.equal(admins.length, adminMemberKeys.length);
+
+          // Are regular members present?
+          const regular = members.filter(
+            member =>
+              regularMemberKeys
+                .map(memberKey => getUser(memberKey).id)
+                .includes(member.user.id) &&
+              member.role === MembershipRoleName.Member
+          );
+          assert.equal(regular.length, regularMemberKeys.length);
+        });
+    });
+
+    it('RANDOM_USER should not be able to see team members list', async function() {
+      await request(api)
+        .get(`/v1/teams/${teamId}/members`)
+        .set('Authorization', `Bearer ${getToken(User.RANDOM_USER)}`)
+        .expect(401);
+    });
+
+    it('RANDOM_USER should not be able to add themselves as a member to the team', async function() {
+      await request(api)
+        .post(`/v1/teams/${teamId}/members`)
+        .set('Authorization', `Bearer ${getToken(User.RANDOM_USER)}`)
+        .send({ userId: getUser(User.RANDOM_USER).id, role: 'member' })
+        .expect(401);
+    });
+
+    it('RANDOM_USER should not be able to add themselves as an admin to the team', async function() {
+      await request(api)
+        .post(`/v1/teams/${teamId}/members`)
+        .set('Authorization', `Bearer ${getToken(User.RANDOM_USER)}`)
+        .send({ userId: getUser(User.RANDOM_USER).id, role: 'admin' })
+        .expect(401);
+    });
+
+    it('PEER_MEMBER should not be able to rename the team', async function() {
+      await request(api)
+        .put(`/v1/teams/${teamId}`)
+        .set('Authorization', `Bearer ${getToken(User.PEER_MEMBER)}`)
+        .send({ name: newTeamName })
+        .expect(401);
+    });
+
+    it('PEER_MEMBER should still see the original team name in their teams list', async function() {
+      await request(api)
+        .get('/v1/teams')
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_MEMBER)}`)
+        .expect(200)
+        .then(response => {
+          assert.equal(
+            response.body.teams.filter(
+              team => team.username === originalTeamName
+            ).length,
+            1
+          );
+        });
+    });
+
+    it('ADMIN_MEMBER should be able to rename the team', async function() {
+      await request(api)
+        .put(`/v1/teams/${teamId}`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_MEMBER)}`)
+        .send({ name: newTeamName })
+        .expect(200);
+    });
+
+    it('PEER_MEMBER should see the new team name in their teams list', async function() {
+      await request(api)
+        .get('/v1/teams')
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_MEMBER)}`)
+        .expect(200)
+        .then(response => {
+          assert.equal(
+            response.body.teams.filter(team => team.username === newTeamName)
+              .length,
+            1
+          );
+        });
+    });
+  });
+
+  describe('sharable resource permission checks', function() {
+    it('RESOURCE_OWNER should have no resource permissions yet', async function() {
+      await request(api)
+        .get(`/v1/resource_permissions`)
+        .set('Authorization', `Bearer ${getToken(User.RESOURCE_OWNER)}`)
+        .expect(200)
+        .then(response => {
+          const { resourcePermissions } = response.body;
+          assert.deepEqual(resourcePermissions, []);
+        });
+    });
+
+    it('RESOURCE_OWNER should create a new resource', async function() {
+      for (const resource of sharableResources()) {
+        const response = await resource.create({
+          token: getToken(User.RESOURCE_OWNER),
+          expectSuccess: true,
+        });
+        let id;
+        for (const key in response.body) {
+          if (typeof response.body[key].id !== 'undefined') {
+            id = response.body[key].id;
+          }
+        }
+        assert.exists(
+          id,
+          'Could not parse an id from a top-level object inside the response body'
+        );
+        resource.params.id = id;
+      }
+    });
+
+    it('RANDOM_USER should not be able to get the resource', async function() {
+      for (const resource of sharableResources()) {
+        await resource.read({
+          token: getToken(User.RANDOM_USER),
+          expectSuccess: false,
+        });
+      }
+    });
+
+    it('all team members excluding RESOURCE_OWNER should not be able to get the resource', async function() {
+      for (const user of allMemberKeysExcludingResourceOwner) {
+        for (const resource of sharableResources()) {
+          await resource.read({
+            token: getToken(user),
+            expectSuccess: false,
+          });
+        }
+      }
+    });
+
+    it('RESOURCE_OWNER should grant RANDOM_USER read access to the resource', async function() {
+      for (const resource of sharableResources()) {
+        await request(api)
+          .post(`/v1/resource_permissions`)
+          .set('Authorization', `Bearer ${getToken(User.RESOURCE_OWNER)}`)
+          .send({
+            userId: getUser(User.RANDOM_USER).id,
+            resourceType: resource.getType(),
+            resourceId: resource.params.id,
+            mode: 'read',
+          })
+          .expect(200)
+          .then(response => {
+            const { resourcePermission } = response.body;
+            assertValidResourcePermission(resourcePermission);
+            resource.params.individualResourcePermissionId =
+              resourcePermission.id;
+          });
+      }
+    });
+
+    it('RESOURCE_OWNER should grant its team read access to the resource', async function() {
+      for (const resource of sharableResources()) {
+        await request(api)
+          .post(`/v1/resource_permissions`)
+          .set('Authorization', `Bearer ${getToken(User.RESOURCE_OWNER)}`)
+          .send({
+            userId: teamId,
+            resourceType: resource.getType(),
+            resourceId: resource.params.id,
+            mode: 'read',
+          })
+          .expect(200)
+          .then(response => {
+            const { resourcePermission } = response.body;
+            assertValidResourcePermission(resourcePermission);
+            resource.params.teamResourcePermissionId = resourcePermission.id;
+          });
+      }
+    });
+
+    it('RESOURCE_OWNER should see the new permission in their resource permission list', async function() {
+      await request(api)
+        .get(`/v1/resource_permissions`)
+        .set('Authorization', `Bearer ${getToken(User.RESOURCE_OWNER)}`)
+        .expect(200)
+        .then(response => {
+          const { resourcePermissions } = response.body;
+          for (const resource of sharableResources()) {
+            const resourcePermission = resourcePermissions.find(
+              perm => perm.resourceId === resource.params.id
+            );
+            assertValidResourcePermission(resourcePermission);
+          }
+        });
+    });
+
+    it('RANDOM_USER should see the new permission in their resource permission list', async function() {
+      await request(api)
+        .get(`/v1/resource_permissions`)
+        .set('Authorization', `Bearer ${getToken(User.RANDOM_USER)}`)
+        .expect(200)
+        .then(response => {
+          const { resourcePermissions } = response.body;
+          for (const resource of sharableResources()) {
+            const resourcePermission = resourcePermissions.find(
+              perm => perm.resourceId === resource.params.id
+            );
+            assertValidResourcePermission(resourcePermission);
+          }
+        });
+    });
+
+    it('All team members should see the new permission in their resource permission list', async function() {
+      for (const user of allMemberKeys) {
+        await request(api)
+          .get(`/v1/resource_permissions`)
+          .set('Authorization', `Bearer ${getToken(user)}`)
+          .expect(200)
+          .then(response => {
+            const { resourcePermissions } = response.body;
+            for (const resource of sharableResources()) {
+              const resourcePermission = resourcePermissions.find(
+                perm => perm.resourceId === resource.params.id
+              );
+              assertValidResourcePermission(resourcePermission);
+            }
+          });
+      }
+    });
+
+    it('RANDOM_USER should be able to get the resource', async function() {
+      for (const resource of sharableResources()) {
+        await resource.read({
+          token: getToken(User.RANDOM_USER),
+          expectSuccess: true,
+        });
+      }
+    });
+
+    it('All team members should be able to get the resource', async function() {
+      for (const user of allMemberKeys) {
+        for (const resource of sharableResources()) {
+          await resource.read({
+            token: getToken(user),
+            expectSuccess: true,
+          });
+        }
+      }
+    });
+
+    it('RANDOM_USER should see the resource appear in the list of all resources', async function() {
+      for (const resource of sharableResources()) {
+        const response = await resource.list({
+          token: getToken(User.RANDOM_USER),
+          expectSuccess: true,
+        });
+        assertResourceInListResponse(response, resource.params.id);
+      }
+    });
+
+    it('All team members should see the resource appear in the list of all resources', async function() {
+      for (const user of allMemberKeys) {
+        for (const resource of sharableResources()) {
+          const response = await resource.list({
+            token: getToken(user),
+            expectSuccess: true,
+          });
+          assertResourceInListResponse(response, resource.params.id);
+        }
+      }
+    });
+
+    it('RANDOM_USER should not be able to update the resource', async function() {
+      for (const resource of sharableResources()) {
+        await resource.update({
+          token: getToken(User.RANDOM_USER),
+          expectSuccess: false,
+        });
+      }
+    });
+
+    it('All team members excluding RESOURCE_OWNER should not be able to update the resource', async function() {
+      for (const user of allMemberKeysExcludingResourceOwner) {
+        for (const resource of sharableResources()) {
+          await resource.update({
+            token: getToken(user),
+            expectSuccess: false,
+          });
+        }
+      }
+    });
+
+    it('RANDOM_USER should not be able to delete the resource', async function() {
+      for (const resource of sharableResources()) {
+        await resource.delete({
+          token: getToken(User.RANDOM_USER),
+          expectSuccess: false,
+        });
+      }
+    });
+
+    it('All team members excluding RESOURCE_OWNER should not be able to delete the resource', async function() {
+      for (const user of allMemberKeysExcludingResourceOwner) {
+        for (const resource of sharableResources()) {
+          await resource.delete({
+            token: getToken(user),
+            expectSuccess: false,
+          });
+        }
+      }
+    });
+
+    it('RESOURCE_OWNER should grant RANDOM_USER write access to the resource', async function() {
+      for (const resource of sharableResources()) {
+        await request(api)
+          .put(
+            `/v1/resource_permissions/${resource.params.individualResourcePermissionId}`
+          )
+          .set('Authorization', `Bearer ${getToken(User.RESOURCE_OWNER)}`)
+          .send({ mode: 'write' })
+          .expect(200);
+      }
+    });
+
+    it('RESOURCE_OWNER should grant its team write access to the resource resource', async function() {
+      for (const resource of sharableResources()) {
+        await request(api)
+          .put(
+            `/v1/resource_permissions/${resource.params.teamResourcePermissionId}`
+          )
+          .set('Authorization', `Bearer ${getToken(User.RESOURCE_OWNER)}`)
+          .send({ mode: 'write' })
+          .expect(200);
+      }
+    });
+
+    it('RANDOM_USER should be able to update the resource', async function() {
+      for (const resource of sharableResources()) {
+        await resource.update({
+          token: getToken(User.RANDOM_USER),
+          expectSuccess: true,
+        });
+      }
+    });
+
+    it('All team members should be able to update the resource', async function() {
+      for (const user of allMemberKeys) {
+        for (const resource of sharableResources()) {
+          await resource.update({
+            token: getToken(user),
+            expectSuccess: true,
+          });
+        }
+      }
+    });
+
+    it('RANDOM_USER should not be able to delete the resource', async function() {
+      for (const resource of sharableResources()) {
+        await resource.delete({
+          token: getToken(User.RANDOM_USER),
+          expectSuccess: false,
+        });
+      }
+    });
+
+    it('All team members excluding RESOURCE_OWNER should not be able to delete the resource', async function() {
+      for (const user of allMemberKeysExcludingResourceOwner) {
+        for (const resource of sharableResources()) {
+          await resource.delete({
+            token: getToken(user),
+            expectSuccess: false,
+          });
+        }
+      }
+    });
+
+    it('RANDOM_USER should not be able to delete the resource permission', async function() {
+      for (const resource of sharableResources()) {
+        await request(api)
+          .delete(
+            `/v1/resource_permissions/${resource.params.individualResourcePermissionId}`
+          )
+          .set('Authorization', `Bearer ${getToken(User.RANDOM_USER)}`)
+          .expect(401);
+      }
+    });
+
+    it('All team members excluding RESOURCE_OWNER should not be able to delete the resource permission', async function() {
+      for (const user of allMemberKeysExcludingResourceOwner) {
+        for (const resource of sharableResources()) {
+          await request(api)
+            .delete(
+              `/v1/resource_permissions/${resource.params.teamResourcePermissionId}`
+            )
+            .set('Authorization', `Bearer ${getToken(user)}`)
+            .expect(401);
+        }
+      }
+    });
+
+    it('RANDOM_USER should not be able to transfer ownership of the resource');
+    it(
+      'All team members excluding RESOURCE_OWNER should not be able to transfer ownership of the resource'
+    );
+
+    it('RESOURCE_OWNER should delete the individual resource permission', async function() {
+      for (const resource of sharableResources()) {
+        await request(api)
+          .delete(
+            `/v1/resource_permissions/${resource.params.individualResourcePermissionId}`
+          )
+          .set('Authorization', `Bearer ${getToken(User.RESOURCE_OWNER)}`)
+          .expect(200);
+      }
+    });
+
+    it('RANDOM_USER should not be able to get the resource', async function() {
+      for (const resource of sharableResources()) {
+        await resource.read({
+          token: getToken(User.RANDOM_USER),
+          expectSuccess: false,
+        });
+      }
+    });
+
+    it('RESOURCE_OWNER should create a new individual resource permission granting RANDOM_USER admin access to the resource', async function() {
+      for (const resource of sharableResources()) {
+        await request(api)
+          .post(`/v1/resource_permissions`)
+          .set('Authorization', `Bearer ${getToken(User.RESOURCE_OWNER)}`)
+          .send({
+            userId: getUser(User.RANDOM_USER).id,
+            resourceType: resource.getType(),
+            resourceId: resource.params.id,
+            mode: 'admin',
+          })
+          .expect(200)
+          .then(response => {
+            const { resourcePermission } = response.body;
+            assertValidResourcePermission(resourcePermission);
+            resource.params.newIndividualResourcePermissionId =
+              resourcePermission.id;
+          });
+      }
+    });
+
+    it('RESOURCE_OWNER should grant the team admin permission to the resource');
+    it('PEER_MEMBER should transfer ownership of the resource to themselves');
+    it('PEER_MEMBER should delete the team resource permission');
+
+    it('RANDOM_USER should be able to delete the resource', async function() {
+      for (const resource of sharableResources()) {
+        await resource.delete({
+          token: getToken(User.RANDOM_USER),
+          expectSuccess: true,
+        });
+      }
+    });
+  });
+
+  describe('teardown teams', function() {
+    it('RANDOM_USER should not be able to delete the team', async function() {
+      await request(api)
+        .delete(`/v1/teams/${teamId}`)
+        .set('Authorization', `Bearer ${getToken(User.RANDOM_USER)}`)
+        .expect(401);
+    });
+
+    it('RESOURCE_OWNER should not be able to delete the team', async function() {
+      await request(api)
+        .delete(`/v1/teams/${teamId}`)
+        .set('Authorization', `Bearer ${getToken(User.RESOURCE_OWNER)}`)
+        .expect(401);
+    });
+
+    it('PEER_MEMBER should not be able to delete the team', async function() {
+      await request(api)
+        .delete(`/v1/teams/${teamId}`)
+        .set('Authorization', `Bearer ${getToken(User.PEER_MEMBER)}`)
+        .expect(401);
+    });
+
+    it('ADMIN_PEER_MEMBER should be able to delete the team', async function() {
+      await request(api)
+        .delete(`/v1/teams/${teamId}`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_PEER_MEMBER)}`)
+        .expect(200)
+        .then(response => assert.deepEqual(response.body, { success: true }));
+    });
+
+    it('ADMIN_PEER_MEMBER should not be able to list members of a team that no longer exists', async function() {
+      await request(api)
+        .get(`/v1/teams/${teamId}/members`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_PEER_MEMBER)}`)
+        .expect(404);
+    });
+
+    it('ADMIN_PEER_MEMBER should not be able to add members to a team that no longer exists', async function() {
+      await request(api)
+        .post(`/v1/teams/${teamId}/members`)
+        .set('Authorization', `Bearer ${getToken(User.ADMIN_PEER_MEMBER)}`)
+        .send({ userId: getUser(User.PAST_MEMBER).id, role: 'member' })
+        .expect(404);
+    });
+
+    it(
+      'ADMIN_PEER_MEMBER should no longer be able to access resources they were granted permission to through their team membership'
+    );
+  });
+});

diff --git a/tests/test_utils.ts b/tests/test_utils.ts
--- a/tests/test_utils.ts
+++ b/tests/test_utils.ts
@@ -2,6 +2,7 @@ import { assert } from 'chai';
 import * as FormData from 'form-data';
 import { readFileSync } from 'fs';
 import * as jwt from 'jsonwebtoken';
+import { isArray } from 'lodash';
 import * as fetch from 'node-fetch';
 import * as request from 'supertest';
 import { getConnection } from 'typeorm';
@@ -10,6 +11,7 @@ import * as uuid from 'uuid';

 import { User } from '../entity/users/User';
 import { Server } from '../index';
+import { UUID_REGEX } from '../lib/constants';

 export function timeout(millis: number): Promise<void> {
   return new Promise(resolve => {
@@ -57,41 +59,50 @@ export interface TestUserInfo {
   id: number;
   email: string;
 }
+
+export async function registerNewTestUser(): Promise<TestUserInfo> {
+  let userInfo: TestUserInfo;
+  const username = `test_user_${uuid().split('-')[0]}`;
+  const email = `${username}@runwayml.com`;
+  const password = uuid();
+  await request(await getTestApi())
+    .post('/v1/register')
+    .send({
+      username,
+      email,
+      password,
+      firstName: 'Jane',
+      lastName: 'Doe',
+      machineId: uuid(),
+      role: 'Artist',
+    })
+    .expect(200)
+    .then(response => {
+      const decoded: any = jwt.decode(response.body.token);
+      userInfo = {
+        username,
+        password,
+        email,
+        id: decoded.id,
+      };
+    });
+  return userInfo;
+}
+
 let testUserInfo: TestUserInfo;
 export async function getTestUserInfo(): Promise<TestUserInfo> {
   if (!testUserInfo) {
-    const username = `test_user_${uuid().split('-')[0]}`;
-    const email = `${username}@runwayml.com`;
-    const password = uuid();
-    await request(await getTestApi())
-      .post('/v1/register')
-      .send({
-        username,
-        email,
-        password,
-        firstName: 'Jane',
-        lastName: 'Doe',
-        machineId: uuid(),
-        role: 'Artist',
-      })
-      .expect(200)
-      .then(response => {
-        const decoded: any = jwt.decode(response.body.token);
-        testUserInfo = {
-          username,
-          password,
-          email,
-          id: decoded.id,
-        };
-      });
+    testUserInfo = await registerNewTestUser();
   }
   return testUserInfo;
 }

 // This function will always login to get a new token.
-export async function getTestUserToken(): Promise<string> {
+export async function getTestUserToken(
+  userInfo?: TestUserInfo
+): Promise<string> {
   let token: string;
-  const { username, password } = await getTestUserInfo();
+  const { username, password } = userInfo || (await getTestUserInfo());
   await request(await getTestApi())
     .post('/v1/login')
     .send({ username, password })
@@ -239,6 +250,16 @@ export function assertValidMessageSummary(message) {
   assert.isBoolean(message.read);
 }

+export function assertValidResourcePermission(resourcePermission) {
+  assert.exists(resourcePermission);
+  assert.match(resourcePermission.id, new RegExp(UUID_REGEX));
+  assert.isString(resourcePermission.createdAt);
+  assert.isNumber(resourcePermission.userId);
+  assert.exists(resourcePermission.resourceId);
+  assert.isString(resourcePermission.resourceType);
+  assert.include(['read', 'write', 'admin'], resourcePermission.mode);
+}
+
 export async function uploadFileToS3UsingPresignedPOST(
   path: string,
   uploadURL: any
@@ -454,4 +475,19 @@ export async function createTestTrainingExperimentAndProgressVideoTask(
   };
 }

+export function assertResourceInListResponse(
+  response: request.Response,
+  resourceId: string
+) {
+  let item;
+  for (const key in response.body) {
+    const list = response.body[key];
+    if (isArray(list)) {
+      item = list.find(item => item.id === resourceId);
+      if (item) break;
+    }
+  }
+  assert.exists(item);
+}
+
 export const STYLEGAN_ID = 34;