PR #2625: add generation pair and generation review tables
================================================================================

diff --git a/entity/GenerationBatch.ts b/entity/GenerationBatch.ts
--- a/entity/GenerationBatch.ts
+++ b/entity/GenerationBatch.ts
@@ -0,0 +1,43 @@
+import {
+  Column,
+  CreateDateColumn,
+  Entity,
+  PrimaryGeneratedColumn,
+} from 'typeorm';
+
+import {
+  ExportFieldToRedshift,
+  ExportTableToRedshift,
+  RunwayBaseEntity,
+} from '../lib/RunwayBaseEntity';
+
+@ExportTableToRedshift({
+  rowsAreImmutable: true,
+  comment: 'Record of a batch of generated videos',
+})
+@Entity({ name: 'generation_batches' })
+export class GenerationBatch extends RunwayBaseEntity {
+  @ExportFieldToRedshift()
+  @PrimaryGeneratedColumn('uuid')
+  id!: string;
+
+  @ExportFieldToRedshift()
+  @CreateDateColumn()
+  createdAt!: Date;
+
+  @ExportFieldToRedshift()
+  @Column({ type: 'varchar' })
+  checkpointFile!: string;
+
+  @ExportFieldToRedshift()
+  @Column({ type: 'int4' })
+  steps!: number;
+
+  @ExportFieldToRedshift()
+  @Column({ type: 'varchar' })
+  sampler!: string;
+
+  @ExportFieldToRedshift()
+  @Column({ type: 'numeric' })
+  cfgScale!: number;
+}

diff --git a/entity/GenerationPair.ts b/entity/GenerationPair.ts
--- a/entity/GenerationPair.ts
+++ b/entity/GenerationPair.ts
@@ -0,0 +1,50 @@
+import {
+  Column,
+  CreateDateColumn,
+  Entity,
+  PrimaryGeneratedColumn,
+  UpdateDateColumn,
+} from 'typeorm';
+
+import {
+  ExportFieldToRedshift,
+  ExportTableToRedshift,
+  RunwayBaseEntity,
+} from '../lib/RunwayBaseEntity';
+
+@ExportTableToRedshift({
+  softDeleteColumn: 'deleted',
+  comment: 'Collections of videos to be voted on quality for',
+})
+@Entity({ name: 'generation_pairs' })
+export class GenerationPair extends RunwayBaseEntity {
+  @ExportFieldToRedshift()
+  @PrimaryGeneratedColumn('uuid')
+  id!: string;
+
+  @ExportFieldToRedshift()
+  @CreateDateColumn()
+  createdAt!: Date;
+
+  @ExportFieldToRedshift()
+  @UpdateDateColumn()
+  updatedAt!: Date;
+
+  @ExportFieldToRedshift()
+  @Column({
+    type: 'json',
+    comment: 'An array of URLs to be voted on.',
+  })
+  assetUrls!: string[];
+
+  @ExportFieldToRedshift()
+  @Column({ type: 'uuid' })
+  batchId!: string;
+
+  @ExportFieldToRedshift()
+  @Column({ type: 'json' })
+  metadata!: Record<string, unknown>;
+
+  @Column({ default: false })
+  deleted!: boolean;
+}

diff --git a/entity/GenerationReview.ts b/entity/GenerationReview.ts
--- a/entity/GenerationReview.ts
+++ b/entity/GenerationReview.ts
@@ -0,0 +1,54 @@
+import {
+  Column,
+  CreateDateColumn,
+  Entity,
+  PrimaryGeneratedColumn,
+  UpdateDateColumn,
+} from 'typeorm';
+
+import {
+  ExportFieldToRedshift,
+  ExportTableToRedshift,
+  RunwayBaseEntity,
+} from '../lib/RunwayBaseEntity';
+
+@ExportTableToRedshift({
+  comment:
+    'Reviews of what video is the best from a group generated with the same prompt',
+})
+@Entity({ name: 'generation_reviews' })
+export class GenerationReview extends RunwayBaseEntity {
+  @ExportFieldToRedshift()
+  @PrimaryGeneratedColumn('uuid')
+  id!: string;
+
+  @ExportFieldToRedshift()
+  @Column({ type: 'uuid' })
+  generationPairId!: string;
+
+  @ExportFieldToRedshift()
+  @CreateDateColumn()
+  createdAt!: Date;
+
+  @ExportFieldToRedshift()
+  @UpdateDateColumn()
+  updatedAt!: Date;
+
+  @ExportFieldToRedshift()
+  @Column({ type: 'uuid' })
+  winnerId!: string;
+
+  @ExportFieldToRedshift()
+  @Column({
+    type: 'int4',
+    comment: 'What index in the array of urls the winner was located at',
+  })
+  winnerIndex!: number;
+
+  @ExportFieldToRedshift()
+  @Column({
+    type: 'int4',
+    comment: 'How long it took for the user to review the videos',
+  })
+  reviewTimeMs!: number;
+}

diff --git a/lib/dbConnection.ts b/lib/dbConnection.ts
--- a/lib/dbConnection.ts
+++ b/lib/dbConnection.ts
@@ -27,7 +27,10 @@ import { FeatureUsageCounter } from '../entity/FeatureUsageCounter';
 import { UserFeedback } from '../entity/Feedback';
 import { GeneratedAudioTranscript } from '../entity/GeneratedAudioTranscript';
 import { GeneratedVoice } from '../entity/GeneratedVoice';
+import { GenerationBatch } from '../entity/GenerationBatch';
 import { GenerationFeedback } from '../entity/GenerationFeedback';
+import { GenerationPair } from '../entity/GenerationPair';
+import { GenerationReview } from '../entity/GenerationReview';
 import { IdempotentFeatureUseToken } from '../entity/IdempotentFeatureUseToken';
 import { JupiterReview } from '../entity/JupiterReview';
 import { ModelVariant } from '../entity/modelVariants/ModelVariant';
@@ -157,6 +160,9 @@ export function getEntities(): Array<
     SeatTracker,
     CloneVoiceVerification,
     EmailChangeRecord,
+    GenerationPair,
+    GenerationReview,
+    GenerationBatch,
   ];
 }


diff --git a/migrations/1718222708225-AddGenerationPairsTable.ts b/migrations/1718222708225-AddGenerationPairsTable.ts
--- a/migrations/1718222708225-AddGenerationPairsTable.ts
+++ b/migrations/1718222708225-AddGenerationPairsTable.ts
@@ -0,0 +1,51 @@
+import { MigrationInterface, QueryRunner, Table } from 'typeorm';
+
+export class AddGenerationPairsTable1718222708225
+  implements MigrationInterface
+{
+  public async up(queryRunner: QueryRunner): Promise<void> {
+    await queryRunner.createTable(
+      new Table({
+        name: 'generation_pairs',
+        columns: [
+          {
+            name: 'id',
+            type: 'uuid',
+            isPrimary: true,
+            isGenerated: true,
+            generationStrategy: 'uuid',
+          },
+          {
+            name: 'createdAt',
+            type: 'timestamp',
+            default: 'now()',
+          },
+          {
+            name: 'assetUrls',
+            type: 'json',
+            isNullable: false,
+          },
+          {
+            name: 'batchId',
+            type: 'uuid',
+            isNullable: false,
+          },
+          {
+            name: 'metadata',
+            type: 'json',
+            isNullable: false,
+          },
+          {
+            name: 'deleted',
+            type: 'boolean',
+            default: false,
+          },
+        ],
+      })
+    );
+  }
+
+  public async down(queryRunner: QueryRunner): Promise<void> {
+    await queryRunner.dropTable('generation_pairs');
+  }
+}

diff --git a/migrations/1718224493166-AddGenerationReviewsTable.ts b/migrations/1718224493166-AddGenerationReviewsTable.ts
--- a/migrations/1718224493166-AddGenerationReviewsTable.ts
+++ b/migrations/1718224493166-AddGenerationReviewsTable.ts
@@ -0,0 +1,76 @@
+import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';
+
+export class AddGenerationReviewsTable1718224493166
+  implements MigrationInterface
+{
+  public async up(queryRunner: QueryRunner): Promise<void> {
+    await queryRunner.createTable(
+      new Table({
+        name: 'generation_reviews',
+        columns: [
+          {
+            name: 'id',
+            type: 'uuid',
+            isPrimary: true,
+            isGenerated: true,
+            generationStrategy: 'uuid',
+          },
+          { name: 'generationPairId', type: 'uuid', isNullable: false },
+          { name: 'userId', type: 'int', isNullable: false },
+          {
+            name: 'createdAt',
+            type: 'timestamp',
+            default: 'now()',
+          },
+          {
+            name: 'updatedAt',
+            type: 'timestamp',
+            default: 'now()',
+          },
+          {
+            name: 'winnerId',
+            type: 'uuid',
+          },
+          {
+            name: 'winnerIndex',
+            type: 'int',
+          },
+          {
+            name: 'reviewTimeMs',
+            type: 'int',
+          },
+        ],
+        indices: [
+          new TableIndex({
+            name: 'generation_reviews_userId',
+            columnNames: ['userId'],
+          }),
+          new TableIndex({
+            name: 'generation_reviews_generationPairId',
+            columnNames: ['generationPairId'],
+          }),
+        ],
+        foreignKeys: [
+          {
+            name: 'generation_reviews__user',
+            columnNames: ['userId'],
+            referencedTableName: 'users',
+            referencedColumnNames: ['id'],
+            onDelete: 'CASCADE',
+          },
+          {
+            name: 'generation_reviews__generation_pair_id',
+            columnNames: ['generationPairId'],
+            referencedTableName: 'generation_pairs',
+            referencedColumnNames: ['id'],
+            onDelete: 'CASCADE',
+          },
+        ],
+      })
+    );
+  }
+
+  public async down(queryRunner: QueryRunner): Promise<void> {
+    await queryRunner.dropTable('generation_reviews');
+  }
+}

diff --git a/migrations/1718726230467-AddGenerationBatchesTable.ts b/migrations/1718726230467-AddGenerationBatchesTable.ts
--- a/migrations/1718726230467-AddGenerationBatchesTable.ts
+++ b/migrations/1718726230467-AddGenerationBatchesTable.ts
@@ -0,0 +1,48 @@
+import { MigrationInterface, QueryRunner, Table } from 'typeorm';
+
+export class AddGenerationBatchesTable1718726230467
+  implements MigrationInterface
+{
+  public async up(queryRunner: QueryRunner): Promise<void> {
+    await queryRunner.createTable(
+      new Table({
+        name: 'generation_batches',
+        columns: [
+          {
+            name: 'id',
+            type: 'uuid',
+            isPrimary: true,
+            isGenerated: true,
+            generationStrategy: 'uuid',
+          },
+          {
+            name: 'createdAt',
+            type: 'timestamp',
+            default: 'now()',
+          },
+          {
+            name: 'checkpointFile',
+            type: 'varchar',
+          },
+          {
+            name: 'steps',
+            type: 'int4',
+          },
+          {
+            name: 'sampler',
+            type: 'varchar',
+          },
+          {
+            name: 'cfgScale',
+            type: 'numeric',
+            isNullable: false,
+          },
+        ],
+      })
+    );
+  }
+
+  public async down(queryRunner: QueryRunner): Promise<void> {
+    await queryRunner.dropTable('generation_batches');
+  }
+}