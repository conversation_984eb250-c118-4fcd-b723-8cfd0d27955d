PR #2225: [GenerateAudio] Remove the default value for privateInTeam
================================================================================

diff --git a/controllers/audio_generation.ts b/controllers/audio_generation.ts
--- a/controllers/audio_generation.ts
+++ b/controllers/audio_generation.ts
@@ -2,7 +2,7 @@ import * as Sentry from '@sentry/node';
 import * as express from 'express';
 import { In } from 'typeorm';

-import { Dataset, DatasetConfig } from '../entity/datasets/Dataset';
+import { Dataset } from '../entity/datasets/Dataset';
 import {
   GeneratedAudioTranscript,
   TranscriptConfig,
@@ -11,12 +11,9 @@ import { GeneratedVoice } from '../entity/GeneratedVoice';
 import { TaskArtifact } from '../entity/tasks/TaskArtifact';
 import { MembershipRole, UserMembership } from '../entity/users/UserMembership';
 import { delete_, get, post, put, validation } from '../lib/apiInterface';
-import { verifyEphemeralAssetURL } from '../lib/assetURLs';
-import { DATASETS_BUCKET } from '../lib/constants';
 import {
   addVoice,
   deleteVoice,
-  getVoices,
   getVoicesAsGeneratedVoiceEntitiesForUser,
 } from '../lib/eleven-labs';
 import { BadRequestError } from '../lib/errors';
@@ -27,8 +24,6 @@ import {
   Success,
   SuccessWithBody,
 } from '../lib/responses';
-import { getObjectSizeS3, parseS3URLToParts } from '../lib/s3';
-import { automatedTest } from '../lib/utils';
 const VOICE_CLONE_FLAG = 'grizzly-bear';

 export default () => {
@@ -72,79 +67,6 @@ export default () => {
       }
     }
   );
-  //TODO Depcreacate this endpoint
-  post(
-    router,
-    '/v1/generated_audio/add_to_assets',
-    {
-      body: {
-        url: validation.string,
-        name: validation.string,
-        transcript: validation.string,
-        asTeamId: validation.legacyAsTeamId,
-        privateInTeam: validation.optionalBoolean,
-      },
-    },
-    async (req, res) => {
-      const {
-        url,
-        name: fname,
-        asTeamId,
-        privateInTeam = true,
-        transcript,
-      } = req.body;
-      const s3Url = verifyEphemeralAssetURL(url);
-      if (!s3Url) {
-        throw new BadRequestError('Invalid url');
-      }
-      const { key } = parseS3URLToParts(s3Url);
-      const team = await UserMembership.GetTeamIfSet({
-        user: req.user,
-        asTeamId,
-        role: MembershipRole.Editor,
-      });
-      const [nonConflictingName, size] = await Promise.all([
-        Dataset.GetNonConflictingName({
-          userId: team.id,
-          createdBy: req.user.id,
-          name: fname,
-          privateInTeam,
-        }),
-        // Skip checking the size if we are running a test
-        // Test will use a fake file to avoid uploading
-        // to s3 for no reason
-        automatedTest() ? 0 : getObjectSizeS3(DATASETS_BUCKET, key),
-      ]);
-      const config: DatasetConfig = {
-        user: req.user.id,
-        name: nonConflictingName,
-        type: { name: 'audio', type: 'audio' },
-        url: s3Url,
-        filename: fname,
-        previewFilenames: [],
-        fileCount: 1,
-        size,
-        private: true,
-        privateInTeam,
-        asTeamId: team.id,
-        isUserUpload: false,
-        createdBy: req.user.id,
-      };
-      const dataset = await Dataset.Create(config);
-      const transcriptConfig: TranscriptConfig = {
-        userId: team.id,
-        datasetId: dataset.id,
-        text: transcript,
-      };
-      const genAudioTranscript = await GeneratedAudioTranscript.Create(
-        transcriptConfig
-      );
-      return res.status(200).json({
-        asset: await dataset.asset({ user: team }),
-        transcript: genAudioTranscript.text,
-      });
-    }
-  );
   post(
     router,
     '/v1/generated_audio/:assetId/link_transcript',
@@ -193,12 +115,6 @@ export default () => {
     }
   );

-  //TODO Deprecate this endpoint
-  get(router, '/v1/voices', {}, async (_, res) => {
-    const resp = await getVoices();
-    return res.status(200).json(resp.data);
-  });
-
   get(
     router,
     '/v1/generated_audio/voices',
@@ -209,7 +125,7 @@ export default () => {
       },
     },
     async (req, res) => {
-      const { asTeamId, privateInTeam = true } = req.query;
+      const { asTeamId, privateInTeam } = req.query;
       const voicesThatUserCanAccess =
         await getVoicesAsGeneratedVoiceEntitiesForUser({
           asTeamId,
@@ -236,17 +152,23 @@ export default () => {
       query: {
         asTeamId: validation.legacyAsTeamId,
         privateInTeam: validation.optionalBoolean,
+
+        // When SI queries this route
+        // it does so using a team jwt
+        // We need to know what the userId is
+        // in that case so we can find the appropriate voices
+        userId: validation.legacyAsTeamId,
       },
     },
     async (req, res) => {
       const { voiceId } = req.params;
-      const { asTeamId, privateInTeam = true } = req.query;
+      const { asTeamId, privateInTeam, userId } = req.query;

       const voicesThatUserCanAccess =
         await getVoicesAsGeneratedVoiceEntitiesForUser({
           asTeamId,
           privateInTeam,
-          userId: req.user.id,
+          userId: userId ?? req.user.id,
         });
       const voice = voicesThatUserCanAccess.find(
         v => v.elevenLabsId === voiceId
@@ -341,11 +263,11 @@ export default () => {
     async (req, res) => {
       const { user } = req;
       const { voiceId } = req.params;
-      const { asTeamId, privateInTeam = true } = req.query;
+      const { asTeamId, privateInTeam } = req.query;
       const teamId = await UserMembership.GetOwnerIdWithActiveMembershipCheck({
         userId: req.user.id,
         asTeamId,
-        role: MembershipRole.Viewer,
+        role: MembershipRole.Editor,
       });

       const userVoice = await GeneratedVoice.getOne({

diff --git a/lib/eleven-labs/index.ts b/lib/eleven-labs/index.ts
--- a/lib/eleven-labs/index.ts
+++ b/lib/eleven-labs/index.ts
@@ -93,7 +93,7 @@ export const getVoicesAsGeneratedVoiceEntitiesForUser = async ({
 }: {
   asTeamId?: number;
   userId: number;
-  privateInTeam: boolean;
+  privateInTeam?: boolean;
 }): Promise<GeneratedVoice[]> => {
   const teamId = await UserMembership.GetOwnerIdWithActiveMembershipCheck({
     userId: userId,
@@ -108,15 +108,36 @@ export const getVoicesAsGeneratedVoiceEntitiesForUser = async ({
   } = await getVoices();
   const { voices } = data;

-  const existingRunwayDBVoices = await GeneratedVoice.find({
-    where: {
-      userId: teamId,
-      elevenLabsId: In(voices.map(v => v.voice_id)),
-      createdBy: privateInTeam ? userId : undefined,
-      deleted: false,
-      privateInTeam,
-    },
-  });
+  let existingRunwayDBVoices: GeneratedVoice[];
+  if (privateInTeam === undefined) {
+    existingRunwayDBVoices = await GeneratedVoice.find({
+      where: [
+        {
+          userId: teamId,
+          elevenLabsId: In(voices.map(v => v.voice_id)),
+          deleted: false,
+          privateInTeam: false,
+        },
+        {
+          userId: teamId,
+          elevenLabsId: In(voices.map(v => v.voice_id)),
+          createdBy: userId,
+          deleted: false,
+          privateInTeam: true,
+        },
+      ],
+    });
+  } else {
+    existingRunwayDBVoices = await GeneratedVoice.find({
+      where: {
+        userId: teamId,
+        elevenLabsId: In(voices.map(v => v.voice_id)),
+        createdBy: privateInTeam ? userId : undefined,
+        deleted: false,
+        privateInTeam,
+      },
+    });
+  }

   const publicVoices = voices
     .filter(voice => {

diff --git a/tests/integration/audio_generation.ts b/tests/integration/audio_generation.ts
--- a/tests/integration/audio_generation.ts
+++ b/tests/integration/audio_generation.ts
@@ -83,7 +83,7 @@ describe('integration/audio generation', async function () {
     it('can get voices', async function () {
       mock.onGet(`${ELEVEN_LABS_HOST}/voices`).reply(200, mocks.publicVoices);
       await request(api)
-        .get(`/v1/voices`)
+        .get(`/v1/generated_audio/voices`)
         .set('Authorization', `Bearer ${token}`)
         .expect(200)
         .then(res => {
@@ -95,15 +95,15 @@ describe('integration/audio generation', async function () {
     it('handles eleven labs 404', async function () {
       mock.onGet(`${ELEVEN_LABS_HOST}/voices`).reply(404);
       await request(api)
-        .get(`/v1/voices`)
+        .get(`/v1/generated_audio/voices`)
         .set('Authorization', `Bearer ${token}`)
         .expect(503);
     });

     it('handles eleven labs timeout', async function () {
       mock.onGet(`${ELEVEN_LABS_HOST}/voices`).timeout();
       await request(api)
-        .get(`/v1/voices`)
+        .get(`/v1/generated_audio/voices`)
         .set('Authorization', `Bearer ${token}`)
         .expect(503);
     });
@@ -716,19 +716,62 @@ describe('integration/audio generation', async function () {
             const voiceIds = voices.map(v => v.voiceId);
             assert(
               !voiceIds.includes(publicTeamVoiceId),
-              'no iteam public voice'
+              'public team voice was included, it should not be'
             );
             assert(
               !voiceIds.includes(privateTeamVoiceId),
-              'no team private voice'
+              'team private voice was included, it should not be'
             );
             assert(
               !voiceIds.includes(nonTeamUserPublicTeamVoiceId),
-              'no public voide'
+              'public non team user voice was included, it should not be'
+            );
+            assert(
+              voiceIds.includes(nonTeamUserPrivateTeamVoiceId),
+              'private non team user voice was not included, it should be'
+            );
+          });
+      });
+
+      it('editor team user gets public and their own private in team voices when privateInTeam is not passed', async function () {
+        mock
+          .onGet(`${ELEVEN_LABS_HOST}/voices`)
+          .reply(
+            200,
+            addToVoiceList(
+              mocks.publicVoices,
+              mocks.userVoice,
+              mocks.teamOwnerPublicVoice,
+              mocks.teamOwnerPrivateVoice,
+              mocks.teamMemberPublicVoice,
+              mocks.teamMemberPrivateVoice
+            )
+          );
+        await request(api)
+          .get(`/v1/generated_audio/voices?asTeamId=${teamId}`)
+          .set('Authorization', `Bearer ${nonTeamUserToken}`)
+          .expect(200)
+          .then(res => {
+            const { voices } = res.body;
+            const voiceIds = voices.map(v => v.voiceId);
+            console.log('TEAM PUBLIC');
+            console.log(publicTeamVoiceId);
+
+            assert(
+              voiceIds.includes(publicTeamVoiceId),
+              'team public voice is not included'
+            );
+            assert(
+              !voiceIds.includes(privateTeamVoiceId),
+              'team private voice is not included'
+            );
+            assert(
+              voiceIds.includes(nonTeamUserPublicTeamVoiceId),
+              'public voice is not included'
             );
             assert(
               voiceIds.includes(nonTeamUserPrivateTeamVoiceId),
-              'gets private voicee'
+              'private voice is not included'
             );
           });
       });