PR #2147: Voice cloning API
================================================================================

diff --git a/controllers/audio_generation.ts b/controllers/audio_generation.ts
--- a/controllers/audio_generation.ts
+++ b/controllers/audio_generation.ts
@@ -1,30 +1,34 @@
 import * as Sentry from '@sentry/node';
-import { default as axios } from 'axios';
-import axiosRetry from 'axios-retry';
 import * as express from 'express';
+import { In } from 'typeorm';

 import { Dataset, DatasetConfig } from '../entity/datasets/Dataset';
 import {
   GeneratedAudioTranscript,
   TranscriptConfig,
 } from '../entity/GeneratedAudioTranscript';
+import { GeneratedVoice } from '../entity/GeneratedVoice';
 import { MembershipRole, UserMembership } from '../entity/users/UserMembership';
-import { get, post, validation } from '../lib/apiInterface';
+import { delete_, get, post, put, validation } from '../lib/apiInterface';
 import { verifyEphemeralAssetURL } from '../lib/assetURLs';
 import { DATASETS_BUCKET } from '../lib/constants';
+import {
+  addVoice,
+  deleteVoice,
+  getVoices,
+  getVoicesAsGeneratedVoiceEntitiesForUser,
+} from '../lib/eleven-labs';
 import { BadRequestError } from '../lib/errors';
-import { ErrorNotFound, respondWith500 } from '../lib/responses';
+import { getFlagValue } from '../lib/launch_darkly';
+import {
+  ErrorForbidden,
+  ErrorNotFound,
+  Success,
+  SuccessWithBody,
+} from '../lib/responses';
 import { getObjectSizeS3, parseS3URLToParts } from '../lib/s3';
 import { automatedTest } from '../lib/utils';
-export const ELEVEN_LABS_API_KEY = process.env.ELEVEN_LABS_API_KEY;
-export const ELEVEN_LABS_HOST = 'https://api.elevenlabs.io/v1';
-export const ELEVEN_LABS_AXIOS_CONFIG = {
-  retries: 3,
-  shouldResetTimeout: true,
-};
-
-export const axiosInstance = axios.create();
-axiosRetry(axiosInstance, ELEVEN_LABS_AXIOS_CONFIG);
+const VOICE_CLONE_FLAG = 'grizzly-bear';

 export default () => {
   const router = express.Router();
@@ -186,19 +190,251 @@ export default () => {
     }
   );

+  //TODO Deprecate this endpoint
   get(router, '/v1/voices', {}, async (_, res) => {
-    try {
-      const resp = await axiosInstance.get(`${ELEVEN_LABS_HOST}/voices`, {
-        headers: {
-          'xi-api-key': ELEVEN_LABS_API_KEY,
+    const resp = await getVoices();
+    return res.status(200).json(resp.data);
+  });
+
+  get(
+    router,
+    '/v1/generated_audio/voices',
+    {
+      query: {
+        asTeamId: validation.legacyAsTeamId,
+        privateInTeam: validation.optionalBoolean,
+      },
+    },
+    async (req, res) => {
+      const { asTeamId, privateInTeam = true } = req.query;
+      const voicesThatUserCanAccess =
+        await getVoicesAsGeneratedVoiceEntitiesForUser({
+          asTeamId,
+          privateInTeam,
+          userId: req.user.id,
+        });
+
+      return res.status(200).json({
+        voices: [...voicesThatUserCanAccess.map(v => v.summary())],
+      });
+    }
+  );
+
+  get(
+    router,
+    '/v1/generated_audio/voices/:voiceId',
+    {
+      params: {
+        // NOTE voiceId
+        // This is either the voiceId of a custom voice
+        // or of a public Eleven Labs Voice
+        voiceId: validation.string,
+      },
+      query: {
+        asTeamId: validation.legacyAsTeamId,
+        privateInTeam: validation.optionalBoolean,
+      },
+    },
+    async (req, res) => {
+      const { voiceId } = req.params;
+      const { asTeamId, privateInTeam = true } = req.query;
+
+      const voicesThatUserCanAccess =
+        await getVoicesAsGeneratedVoiceEntitiesForUser({
+          asTeamId,
+          privateInTeam,
+          userId: req.user.id,
+        });
+      const voice = voicesThatUserCanAccess.find(
+        v => v.elevenLabsId === voiceId
+      );
+
+      // Voice.id is not defined if this voice was constructed
+      // from eleven labs
+      if (voice?.id) {
+        voice.lastUsedAt = new Date();
+        await voice.save();
+      }
+
+      if (voice) {
+        return res.status(200).json({
+          voice: voice.summary(),
+        });
+      } else {
+        return ErrorNotFound(res);
+      }
+    }
+  );
+
+  put(
+    router,
+    '/v1/generated_audio/voices/:id',
+    {
+      params: {
+        // This is a runway uuid ID
+        id: validation.uuid,
+      },
+      body: {
+        name: validation.optionalString,
+        description: validation.optionalString,
+      },
+      query: {
+        asTeamId: validation.legacyAsTeamId,
+        privateInTeam: validation.optionalBoolean,
+      },
+    },
+    async (req, res) => {
+      const { id } = req.params;
+      const { name: _name, description } = req.body;
+      const { asTeamId, privateInTeam = true } = req.query;
+      const team = await UserMembership.GetTeamIfSet({
+        user: req.user,
+        asTeamId,
+        role: MembershipRole.Editor,
+      });
+
+      const gv = await GeneratedVoice.getOneBy({
+        id,
+        userId: team.id,
+        privateInTeam,
+        createdBy: privateInTeam ? req.user.id : undefined,
+      });
+
+      gv.name = _name ?? gv.name;
+      gv.description = description ?? gv.description;
+      const now = new Date();
+      gv.lastUsedAt = now;
+      gv.updatedAt = now;
+      await gv.save();
+      return res.status(200).json({
+        voice: gv.summary(),
+      });
+    }
+  );
+
+  delete_(
+    router,
+    '/v1/generated_audio/voices/:voiceId',
+    {
+      params: {
+        // NOTE voiceId
+        // This is either the voiceId of a custom voice
+        // or of a public Eleven Labs Voice
+        voiceId: validation.string,
+      },
+      query: {
+        asTeamId: validation.legacyAsTeamId,
+        privateInTeam: validation.optionalBoolean,
+      },
+    },
+    async (req, res) => {
+      const { user } = req;
+      const { voiceId } = req.params;
+      const { asTeamId, privateInTeam = true } = req.query;
+      const teamId = await UserMembership.GetOwnerIdWithActiveMembershipCheck({
+        userId: req.user.id,
+        asTeamId,
+        role: MembershipRole.Viewer,
+      });
+
+      const userVoice = await GeneratedVoice.getOne({
+        where: {
+          userId: teamId,
+          privateInTeam,
+          elevenLabsId: voiceId,
+          createdBy: privateInTeam ? user.id : undefined,
+          deleted: false,
         },
       });
-      return res.status(200).json(resp.data);
-    } catch (err) {
-      Sentry.captureException(err);
-      return respondWith500(res);
+
+      await deleteVoice(voiceId);
+      await userVoice.delete();
+      return Success(res);
     }
-  });
+  );
+
+  post(
+    router,
+    '/v1/generated_audio/voices',
+    {
+      query: {
+        asTeamId: validation.legacyAsTeamId,
+        privateInTeam: validation.optionalBoolean,
+      },
+      body: {
+        name: validation.string,
+        description: validation.optionalString,
+        samples: validation.arrayOfStrings,
+      },
+    },
+    async (req, res) => {
+      const user = req.user!;
+      const { asTeamId, privateInTeam } = req.query;
+      const { name, description, samples } = req.body;
+
+      const team = await UserMembership.GetTeamIfSet({
+        user,
+        asTeamId,
+        role: MembershipRole.Viewer,
+      });
+
+      const hasAccessToFeature = await getFlagValue(VOICE_CLONE_FLAG, team);
+      if (!hasAccessToFeature) {
+        return ErrorNotFound(res);
+      }
+
+      const feature = 'voiceClone';
+      const count = 1;
+
+      const canUseFeature = await team.hasEnoughCreditsToUseFeature({
+        count,
+        feature,
+      });
+      if (!canUseFeature) {
+        return ErrorForbidden(res);
+      }
+
+      const datasets = await Dataset.findBy({
+        id: In(samples),
+        userId: team.id,
+        privateInTeam,
+        deleted: false,
+      });
+      if (datasets.length !== samples.length) {
+        return ErrorNotFound(res);
+      }
+
+      datasets.forEach(d => {
+        if (d.mediaType() !== 'audio') {
+          throw new BadRequestError('Invalid sample provided');
+        }
+      });
+
+      const newVoiceFromEleven = await addVoice({
+        name,
+        description,
+        urls: datasets.map(d => d.url),
+      });
+      const newVoice = await GeneratedVoice.Create({
+        name,
+        elevenLabsId: newVoiceFromEleven.data.voice_id,
+        description,
+        datasets,
+        userId: team.id,
+        createdBy: user.id,
+        privateInTeam: privateInTeam,
+      });
+
+      await team.spendPlanAndPaidCredits({
+        feature,
+        count,
+        createdBy: req.user.id,
+      });
+      return SuccessWithBody(res, {
+        voice: newVoice.summary(),
+      });
+    }
+  );

   return router;
 };

diff --git a/entity/FeatureUsageCounter.ts b/entity/FeatureUsageCounter.ts
--- a/entity/FeatureUsageCounter.ts
+++ b/entity/FeatureUsageCounter.ts
@@ -113,6 +113,10 @@ export const FEATURE_PRICES_CREDITS = {
     unit: 'chunk-50char',
     creditPricePerUnit: 1,
   } as FeaturePricing,
+  voiceClone: {
+    unit: 'model',
+    creditPricePerUnit: 300,
+  },
 };
 export type PricedFeatures = keyof typeof FEATURE_PRICES_CREDITS;

@@ -278,6 +282,7 @@ export class FeatureUsageCounter extends BaseEntity {
       case 'upscaleVideo2x':
       case 'upscaleVideo4x':
       case 'generateAudio':
+      case 'voiceClone':
         // this will prevent floating point precision errors
         return {
           cost: Math.round(creditPricePerUnit * count),

diff --git a/entity/GeneratedVoice.ts b/entity/GeneratedVoice.ts
--- a/entity/GeneratedVoice.ts
+++ b/entity/GeneratedVoice.ts
@@ -0,0 +1,185 @@
+import {
+  Column,
+  CreateDateColumn,
+  Entity,
+  PrimaryGeneratedColumn,
+} from 'typeorm';
+
+import { DATASETS_BUCKET } from '../lib/constants';
+import { BadRequestError } from '../lib/errors';
+import {
+  ExportFieldToRedshift,
+  ExportTableToRedshift,
+} from '../lib/RunwayBaseEntity';
+import {
+  copyObjectInS3,
+  getObjectUrlHTTP,
+  isS3URL,
+  parseS3URLToParts,
+} from '../lib/s3';
+import { Dataset } from './datasets/Dataset';
+import { S3ObjectDeletion } from './S3ObjectDeletion';
+import { ResourceType } from './sharing/ResourcePermission';
+import { SharableResourceEntity } from './sharing/SharableResourceEntity';
+
+type CreateOptions = {
+  name: string;
+  description?: string;
+  userId: number;
+  createdBy: number;
+  privateInTeam?: boolean;
+  datasets: Dataset[];
+  elevenLabsId: string;
+};
+
+@ExportTableToRedshift({ softDeleteColumn: 'deleted' })
+@Entity({ name: 'generated_voices' })
+export class GeneratedVoice extends SharableResourceEntity {
+  @ExportFieldToRedshift()
+  @PrimaryGeneratedColumn('uuid')
+  id!: string;
+
+  @ExportFieldToRedshift()
+  @Column({ type: 'varchar', nullable: false })
+  elevenLabsId!: string;
+
+  @ExportFieldToRedshift()
+  @Column({ type: 'varchar', nullable: false })
+  name!: string;
+
+  @Column({ type: 'varchar' })
+  description!: string;
+
+  @ExportFieldToRedshift()
+  @Column({ type: 'int4' })
+  userId!: number;
+
+  @ExportFieldToRedshift()
+  @Column({ default: true, nullable: false })
+  privateInTeam!: boolean;
+
+  @ExportFieldToRedshift()
+  @Column({
+    default: false,
+    comment: 'Private datasets are not visible publicly.',
+  })
+  private!: boolean;
+
+  @ExportFieldToRedshift()
+  @Column({ type: 'int4' })
+  createdBy!: number;
+
+  @ExportFieldToRedshift()
+  @Column({
+    type: 'json',
+    comment:
+      'A JSON array of URLs that are audio files which are used to create the voice',
+    nullable: false,
+  })
+  files!: string[];
+
+  @Column({
+    type: 'json',
+    comment: 'A JSON object of labels for the voice',
+    nullable: false,
+  })
+  labels!: Record<string, string>;
+
+  @ExportFieldToRedshift()
+  @CreateDateColumn()
+  createdAt!: Date;
+
+  @ExportFieldToRedshift()
+  @CreateDateColumn()
+  updatedAt!: Date;
+
+  @ExportFieldToRedshift()
+  @Column({ nullable: true, type: 'timestamp' })
+  lastUsedAt!: Date | null;
+
+  @ExportFieldToRedshift()
+  @Column({ default: false, nullable: false })
+  deleted!: boolean;
+
+  @ExportFieldToRedshift()
+  @Column({ default: true, nullable: false })
+  active!: boolean;
+
+  summary() {
+    const {
+      id,
+      createdAt,
+      lastUsedAt,
+      name,
+      description,
+      elevenLabsId,
+      labels,
+    } = this;
+    return {
+      id,
+      voiceId: elevenLabsId,
+      createdAt,
+      lastUsedAt,
+      name,
+      description,
+      labels,
+    };
+  }
+
+  static async Create({
+    userId,
+    name,
+    description,
+    createdBy,
+    privateInTeam = true,
+    datasets,
+    elevenLabsId,
+  }: CreateOptions) {
+    const voice = new GeneratedVoice();
+    voice.userId = userId;
+    voice.name = name;
+    voice.description = description ?? '';
+    voice.createdBy = createdBy;
+    voice.privateInTeam = privateInTeam;
+    voice.elevenLabsId = elevenLabsId;
+
+    const copiedFiles = await Promise.all(
+      datasets.map(async ({ url }) => {
+        if (!isS3URL(url)) {
+          throw new BadRequestError('Invalid file provided');
+        }
+
+        const { bucket, key } = parseS3URLToParts(url);
+        const destBucket = bucket;
+        const destKey = `generated-voice-sample-${key}`;
+        await copyObjectInS3(bucket, key, bucket, destKey);
+        return getObjectUrlHTTP(destBucket, destKey);
+      })
+    );
+    voice.files = copiedFiles;
+    await voice.save();
+    return voice;
+  }
+
+  async delete() {
+    this.deleted = true;
+    this.active = false;
+
+    const deletions = this.files.map(async f => {
+      const key = f.replace(`https://${DATASETS_BUCKET}.s3.amazonaws.com/`, '');
+
+      await S3ObjectDeletion.Create({
+        bucket: DATASETS_BUCKET,
+        key,
+        sourceType: 'generated_voice',
+        sourceId: this.id,
+      });
+    });
+
+    await Promise.all(deletions);
+  }
+
+  public getSharableResourceType() {
+    return ResourceType.GENERATED_VOICE;
+  }
+}

diff --git a/entity/S3ObjectDeletion.ts b/entity/S3ObjectDeletion.ts
--- a/entity/S3ObjectDeletion.ts
+++ b/entity/S3ObjectDeletion.ts
@@ -12,9 +12,14 @@ import { DEFAULT_S3_OBJECT_DELETION_DELAY_DAYS } from '../lib/constants';
 import { deleteObjectS3 } from '../lib/s3';
 import { getDateDaysInFuture, isProduction } from '../lib/utils';
 import { Dataset } from './datasets/Dataset';
+import { GeneratedVoice } from './GeneratedVoice';
 import { TaskArtifact } from './tasks/TaskArtifact';

-export type SourceType = 'dataset' | 'model_checkpoint' | 'task_artifact';
+export type SourceType =
+  | 'dataset'
+  | 'model_checkpoint'
+  | 'task_artifact'
+  | 'generated_voice';
 export interface S3ObjectDeletionCreateOpts {
   bucket: string;
   key: string;
@@ -88,6 +93,8 @@ export class S3ObjectDeletion extends BaseEntity {
       throw new Error('Cannot restore model checkpoints');
     } else if (this.sourceType === 'task_artifact') {
       return TaskArtifact;
+    } else if (this.sourceType === 'generated_voice') {
+      return GeneratedVoice;
     } else {
       throw new Error(`"${this.sourceType}" is not a valid source type`);
     }

diff --git a/entity/datasets/Dataset.ts b/entity/datasets/Dataset.ts
--- a/entity/datasets/Dataset.ts
+++ b/entity/datasets/Dataset.ts
@@ -90,6 +90,7 @@ export const AllowedDatasetMimeTypes = [
   'application/x-tar',
   'text/plain',
   'application/gzip',
+  'audio/mpeg',
 ];

 export const bigint = {

diff --git a/entity/sharing/ResourcePermission.ts b/entity/sharing/ResourcePermission.ts
--- a/entity/sharing/ResourcePermission.ts
+++ b/entity/sharing/ResourcePermission.ts
@@ -47,6 +47,7 @@ export enum ResourceType {
   ENDPOINT = 'endpoint',
   VIDEO_COMPOSITION = 'videoComposition',
   ASSET_GROUP = 'assetGroup',
+  GENERATED_VOICE = 'generatedVoice',
 }

 export type ResourceId = number | string;

diff --git a/entity/users/User.ts b/entity/users/User.ts
--- a/entity/users/User.ts
+++ b/entity/users/User.ts
@@ -91,6 +91,7 @@ import {
   PricedFeatures,
   ResetBucketType,
 } from '../FeatureUsageCounter';
+import { GeneratedVoice } from '../GeneratedVoice';
 import { Quota } from '../Quota';
 import { SharedAsset } from '../SharedAsset';
 import { Task } from '../tasks/Task';
@@ -1173,13 +1174,15 @@ export class User extends RunwayBaseEntity {
   async delete() {
     this.deleted = true;
     await this.save();
-    const [tasks, datasets] = await Promise.all([
+    const [tasks, datasets, generatedVoices] = await Promise.all([
       Task.findBy({ userId: this.id, deleted: false }),
       Dataset.findBy({ userId: this.id, deleted: false }),
+      GeneratedVoice.findBy({ userId: this.id, deleted: false }),
     ]);
     await Promise.all([
       ...tasks.map(task => task.delete()),
       ...datasets.map(dataset => dataset.delete()),
+      ...generatedVoices.map(dataset => dataset.delete()),
       getRepository(UserMembership)
         .createQueryBuilder('membership')
         .update(UserMembership)

diff --git a/lib/axiosIntercept.ts b/lib/axiosIntercept.ts
--- a/lib/axiosIntercept.ts
+++ b/lib/axiosIntercept.ts
@@ -0,0 +1,47 @@
+import { AxiosInstance } from 'axios';
+import * as fs from 'fs';
+
+export const recordedResponses = {};
+
+export const axiosIntercept = (axiosInstance: AxiosInstance) => {
+  axiosInstance.interceptors.response.use(
+    response => {
+      const requestKey = `${response.config.method}_${response.config.url}`;
+
+      recordedResponses[requestKey] = [
+        ...(recordedResponses[requestKey] ?? []),
+        response.data,
+      ];
+      return response;
+    },
+    error => {
+      console.error(error);
+      return Promise.reject(error);
+    }
+  );
+};
+
+export const finish = (filename: string) => {
+  const responses = Object.entries(recordedResponses).reduce((accum, entry) => {
+    //@ts-ignore
+    const [key, respList]: [string, Array<any>] = entry;
+    console.log(key, respList.length);
+    const respsToAdd = respList.reduce((accum, resp, i) => {
+      return {
+        ...accum,
+        [`${key}--${i}`]: resp,
+      };
+    }, {});
+
+    return {
+      ...accum,
+      ...respsToAdd,
+    };
+  }, {});
+  try {
+    fs.writeFileSync(filename, JSON.stringify(responses));
+    console.log('Recorded responses saved successfully.');
+  } catch (err) {
+    console.error('Error writing recorded responses:', err);
+  }
+};

diff --git a/lib/dbConnection.ts b/lib/dbConnection.ts
--- a/lib/dbConnection.ts
+++ b/lib/dbConnection.ts
@@ -29,6 +29,7 @@ import { ExternalUser } from '../entity/ExternalUser';
 import { FeatureUsageCounter } from '../entity/FeatureUsageCounter';
 import { UserFeedback } from '../entity/Feedback';
 import { GeneratedAudioTranscript } from '../entity/GeneratedAudioTranscript';
+import { GeneratedVoice } from '../entity/GeneratedVoice';
 import { GenerationFeedback } from '../entity/GenerationFeedback';
 import { IdempotentFeatureUseToken } from '../entity/IdempotentFeatureUseToken';
 import { ModelVariant } from '../entity/modelVariants/ModelVariant';
@@ -140,6 +141,7 @@ export function getEntities(): Array<
     GeneratedAudioTranscript,
     Storyboard,
     GenerationFeedback,
+    GeneratedVoice,
     ModelVariant,
     ModelVariantAccess,
   ];

diff --git a/lib/eleven-labs/index.ts b/lib/eleven-labs/index.ts
--- a/lib/eleven-labs/index.ts
+++ b/lib/eleven-labs/index.ts
@@ -0,0 +1,195 @@
+import * as Sentry from '@sentry/node';
+import { AxiosError, AxiosResponse, default as axios } from 'axios';
+import axiosRetry from 'axios-retry';
+import { In } from 'typeorm';
+
+import { GeneratedVoice } from '../../entity/GeneratedVoice';
+import {
+  MembershipRole,
+  UserMembership,
+} from '../../entity/users/UserMembership';
+import { ServiceUnavailableError } from '../errors';
+import { parseS3URLToParts, readS3FileAsBuffer } from '../s3';
+
+export const ELEVEN_LABS_API_KEY = process.env.ELEVEN_LABS_API_KEY;
+export const ELEVEN_LABS_HOST = 'https://api.elevenlabs.io/v1';
+export const ELEVEN_LABS_AXIOS_CONFIG = {
+  retries: 3,
+  shouldResetTimeout: true,
+};
+export const axiosInstance = axios.create();
+axiosRetry(axiosInstance, ELEVEN_LABS_AXIOS_CONFIG);
+
+const ELEVEN_LABS_HEADERS = {
+  'xi-api-key': ELEVEN_LABS_API_KEY,
+};
+
+export const ELEVEN_LABS_USER_VOICE_LABELS = {
+  voiceType: 'user',
+};
+
+type PromiseReturningFunction<T extends any[]> = (
+  ...args: T
+) => Promise<AxiosResponse>;
+
+const errorWrapper = <T extends any[]>(
+  promiseReturner: PromiseReturningFunction<T>
+): PromiseReturningFunction<T> => {
+  return async (...args: T) => {
+    try {
+      const resp = await promiseReturner(...args);
+      return resp;
+    } catch (err) {
+      console.error((err as AxiosError).response?.data);
+      Sentry.captureException(err);
+      throw new ServiceUnavailableError('Something went wrong');
+    }
+  };
+};
+
+export const deleteVoice = errorWrapper(
+  async (id: string): Promise<AxiosResponse> => {
+    return await axiosInstance.delete(`${ELEVEN_LABS_HOST}/voices/${id}`, {
+      headers: ELEVEN_LABS_HEADERS,
+    });
+  }
+);
+export type ElevenVoice = {
+  voice_id: string;
+  name: string;
+  samples: any[] | null;
+  category: string;
+  fine_tuning: any;
+  labels: Record<string, string>;
+  description: null | string;
+  preview_url: string | null;
+  available_for_tiers: any[];
+  settings: any;
+  sharing: any;
+  high_quality_base_model_ids: any[];
+};
+export const getVoice = errorWrapper(
+  async (id: string): Promise<AxiosResponse<ElevenVoice>> => {
+    return await axiosInstance.get(`${ELEVEN_LABS_HOST}/voices/${id}`, {
+      headers: ELEVEN_LABS_HEADERS,
+    });
+  }
+);
+export type ElevenVoiceList = {
+  voices: ElevenVoice[];
+};
+export const getVoices = errorWrapper(
+  async (): Promise<AxiosResponse<ElevenVoiceList>> => {
+    return await axiosInstance.get(`${ELEVEN_LABS_HOST}/voices`, {
+      headers: ELEVEN_LABS_HEADERS,
+    });
+  }
+);
+
+export const getVoicesAsGeneratedVoiceEntitiesForUser = async ({
+  asTeamId,
+  userId,
+  privateInTeam,
+}: {
+  asTeamId?: number;
+  userId: number;
+  privateInTeam: boolean;
+}): Promise<GeneratedVoice[]> => {
+  const teamId = await UserMembership.GetOwnerIdWithActiveMembershipCheck({
+    userId: userId,
+    asTeamId,
+    role: MembershipRole.Viewer,
+  });
+
+  const {
+    data,
+  }: {
+    data: ElevenVoiceList;
+  } = await getVoices();
+  const { voices } = data;
+
+  const existingRunwayDBVoices = await GeneratedVoice.find({
+    where: {
+      userId: teamId,
+      elevenLabsId: In(voices.map(v => v.voice_id)),
+      createdBy: privateInTeam ? userId : undefined,
+      deleted: false,
+      privateInTeam,
+    },
+  });
+
+  const publicVoices = voices
+    .filter(voice => {
+      return voice.labels.voiceType !== ELEVEN_LABS_USER_VOICE_LABELS.voiceType;
+    })
+    .map(voice => {
+      const gv = GeneratedVoice.create({
+        userId: userId,
+        name: voice.name,
+        description: voice.description ?? '',
+        createdBy: userId,
+        privateInTeam: true,
+        elevenLabsId: voice.voice_id,
+        createdAt: new Date(),
+        lastUsedAt: new Date(),
+        files: [],
+        labels: voice.labels,
+        deleted: false,
+        active: true,
+      });
+      return gv;
+    });
+
+  return [...publicVoices, ...existingRunwayDBVoices];
+};
+
+type ElevenLabsVoiceConfig = {
+  name: string;
+  description?: string;
+  urls: string[];
+};
+type ElevenVoiceAddResponse = {
+  voice_id: string;
+};
+export const addVoice = errorWrapper(
+  async (
+    config: ElevenLabsVoiceConfig
+  ): Promise<AxiosResponse<ElevenVoiceAddResponse>> => {
+    // read urls to binary data
+    const { description, urls } = config;
+
+    const form = new FormData();
+    form.append('name', config.name);
+    if (description) {
+      form.append('description', description);
+    }
+
+    form.append('labels', JSON.stringify(ELEVEN_LABS_USER_VOICE_LABELS));
+
+    let blobs: Blob[] = [];
+
+    blobs = await Promise.all(
+      urls.map(async url => {
+        const { bucket, key } = parseS3URLToParts(url);
+        const buffer = await readS3FileAsBuffer(bucket, key);
+        const blob = new Blob([buffer], {
+          type: 'audio/mpeg',
+        });
+        return blob;
+      })
+    );
+
+    blobs.forEach(b => {
+      if (b) {
+        form.append('files', b);
+      }
+    });
+
+    return await axiosInstance.post(`${ELEVEN_LABS_HOST}/voices/add`, form, {
+      headers: {
+        ...ELEVEN_LABS_HEADERS,
+        'Content-Type': 'multipart/form-data',
+      },
+    });
+  }
+);

diff --git a/lib/eleven-labs/mock.ts b/lib/eleven-labs/mock.ts
--- a/lib/eleven-labs/mock.ts
+++ b/lib/eleven-labs/mock.ts
@@ -0,0 +1,252 @@
+export const publicVoices = {
+  voices: [
+    {
+      voice_id: '21m00Tcm4TlvDq8ikWAM',
+      name: 'Rachel',
+      samples: null,
+      category: 'premade',
+      fine_tuning: {
+        language: null,
+        is_allowed_to_fine_tune: false,
+        fine_tuning_requested: false,
+        finetuning_state: 'not_started',
+        verification_attempts: null,
+        verification_failures: [],
+        verification_attempts_count: 0,
+        slice_ids: null,
+        manual_verification: null,
+        manual_verification_requested: false,
+      },
+      labels: {
+        accent: 'american',
+        description: 'calm',
+        age: 'young',
+        gender: 'female',
+        'use case': 'narration',
+      },
+      description: null,
+      preview_url:
+        'https://storage.googleapis.com/eleven-public-prod/premade/voices/21m00Tcm4TlvDq8ikWAM/df6788f9-5c96-470d-8312-aab3b3d8f50a.mp3',
+      available_for_tiers: [],
+      settings: null,
+      sharing: null,
+      high_quality_base_model_ids: [],
+    },
+    {
+      voice_id: '29vD33N1CtxCmqQRPOHJ',
+      name: 'Drew',
+      samples: null,
+      category: 'premade',
+      fine_tuning: {
+        language: null,
+        is_allowed_to_fine_tune: false,
+        fine_tuning_requested: false,
+        finetuning_state: 'not_started',
+        verification_attempts: null,
+        verification_failures: [],
+        verification_attempts_count: 0,
+        slice_ids: null,
+        manual_verification: null,
+        manual_verification_requested: false,
+      },
+      labels: {
+        accent: 'american',
+        description: 'well-rounded',
+        age: 'middle aged',
+        gender: 'male',
+        'use case': 'news',
+      },
+      description: null,
+      preview_url:
+        'https://storage.googleapis.com/eleven-public-prod/premade/voices/29vD33N1CtxCmqQRPOHJ/e8b52a3f-9732-440f-b78a-16d5e26407a1.mp3',
+      available_for_tiers: [],
+      settings: null,
+      sharing: null,
+      high_quality_base_model_ids: [],
+    },
+  ],
+};
+
+export const addUserVoice = {
+  voice_id: 'tDNd68Wq1pVgXggEyzIV',
+};
+export const userVoice = {
+  voice_id: 'tDNd68Wq1pVgXggEyzIV',
+  name: 'My new voice',
+  samples: [
+    {
+      sample_id: '5F0DUm8zMbZqA7rQumF2',
+      file_name: 'blob.mp3',
+      mime_type: 'audio/mpeg',
+      size_bytes: 5779,
+      hash: '70f32287f33433513a0ee3ca477fa240',
+    },
+  ],
+  category: 'cloned',
+  fine_tuning: {
+    language: null,
+    is_allowed_to_fine_tune: false,
+    fine_tuning_requested: false,
+    finetuning_state: 'not_started',
+    verification_attempts: null,
+    verification_failures: [],
+    verification_attempts_count: 0,
+    slice_ids: null,
+    manual_verification: null,
+    manual_verification_requested: false,
+  },
+  labels: { voiceType: 'user' },
+  description: 'Description of voice',
+  preview_url: null,
+  available_for_tiers: [],
+  settings: null,
+  sharing: null,
+  high_quality_base_model_ids: [],
+};
+export const deleteUserVoice = {
+  status: 'ok',
+};
+export const teamOwnerAddPublicVoice = {
+  voice_id: 'gSrXPimU0zEwtx9kLnJp',
+};
+export const teamOwnerAddPrivateVoice = {
+  voice_id: '68i8kI4VDehZwLdoSdA3',
+};
+export const teamOwnerPublicVoice = {
+  voice_id: 'gSrXPimU0zEwtx9kLnJp',
+  name: 'My public team voice',
+  samples: [
+    {
+      sample_id: 'Nr5mK7PGJFLpinn1rBQM',
+      file_name: 'blob.mp3',
+      mime_type: 'audio/mpeg',
+      size_bytes: 5779,
+      hash: '70f32287f33433513a0ee3ca477fa240',
+    },
+  ],
+  category: 'cloned',
+  fine_tuning: {
+    language: null,
+    is_allowed_to_fine_tune: false,
+    fine_tuning_requested: false,
+    finetuning_state: 'not_started',
+    verification_attempts: null,
+    verification_failures: [],
+    verification_attempts_count: 0,
+    slice_ids: null,
+    manual_verification: null,
+    manual_verification_requested: false,
+  },
+  labels: { voiceType: 'user' },
+  description: 'Description of voice',
+  preview_url: null,
+  available_for_tiers: [],
+  settings: null,
+  sharing: null,
+  high_quality_base_model_ids: [],
+};
+export const teamOwnerPrivateVoice = {
+  voice_id: '68i8kI4VDehZwLdoSdA3',
+  name: 'My private team voice',
+  samples: [
+    {
+      sample_id: 'FZFdi5EdEsUb6aSmkwmA',
+      file_name: 'blob.mp3',
+      mime_type: 'audio/mpeg',
+      size_bytes: 5779,
+      hash: '70f32287f33433513a0ee3ca477fa240',
+    },
+  ],
+  category: 'cloned',
+  fine_tuning: {
+    language: null,
+    is_allowed_to_fine_tune: false,
+    fine_tuning_requested: false,
+    finetuning_state: 'not_started',
+    verification_attempts: null,
+    verification_failures: [],
+    verification_attempts_count: 0,
+    slice_ids: null,
+    manual_verification: null,
+    manual_verification_requested: false,
+  },
+  labels: { voiceType: 'user' },
+  description: 'Description of voice',
+  preview_url: null,
+  available_for_tiers: [],
+  settings: null,
+  sharing: null,
+  high_quality_base_model_ids: [],
+};
+export const teamMemberAddPublicVoice = {
+  voice_id: 'RmJyx8c82jMz5AsWkvQZ',
+};
+export const teamMemberAddPrivateVoice = {
+  voice_id: 'CLgjEV0R9zxtlGt2M6Pf',
+};
+export const teamMemberPublicVoice = {
+  voice_id: 'RmJyx8c82jMz5AsWkvQZ',
+  name: 'My private team voice',
+  samples: [
+    {
+      sample_id: 'GISgTh6W0KXrtAHX4tlN',
+      file_name: 'blob.mp3',
+      mime_type: 'audio/mpeg',
+      size_bytes: 5779,
+      hash: '70f32287f33433513a0ee3ca477fa240',
+    },
+  ],
+  category: 'cloned',
+  fine_tuning: {
+    language: null,
+    is_allowed_to_fine_tune: false,
+    fine_tuning_requested: false,
+    finetuning_state: 'not_started',
+    verification_attempts: null,
+    verification_failures: [],
+    verification_attempts_count: 0,
+    slice_ids: null,
+    manual_verification: null,
+    manual_verification_requested: false,
+  },
+  labels: { voiceType: 'user' },
+  description: 'Description of voice',
+  preview_url: null,
+  available_for_tiers: [],
+  settings: null,
+  sharing: null,
+  high_quality_base_model_ids: [],
+};
+export const teamMemberPrivateVoice = {
+  voice_id: 'CLgjEV0R9zxtlGt2M6Pf',
+  name: 'My private team voice',
+  samples: [
+    {
+      sample_id: 'BbrfvBR2E1e0WmdY3iu2',
+      file_name: 'blob.mp3',
+      mime_type: 'audio/mpeg',
+      size_bytes: 5779,
+      hash: '70f32287f33433513a0ee3ca477fa240',
+    },
+  ],
+  category: 'cloned',
+  fine_tuning: {
+    language: null,
+    is_allowed_to_fine_tune: false,
+    fine_tuning_requested: false,
+    finetuning_state: 'not_started',
+    verification_attempts: null,
+    verification_failures: [],
+    verification_attempts_count: 0,
+    slice_ids: null,
+    manual_verification: null,
+    manual_verification_requested: false,
+  },
+  labels: { voiceType: 'user' },
+  description: 'Description of voice',
+  preview_url: null,
+  available_for_tiers: [],
+  settings: null,
+  sharing: null,
+  high_quality_base_model_ids: [],
+};

diff --git a/lib/eleven-labs/mocking.ts b/lib/eleven-labs/mocking.ts
--- a/lib/eleven-labs/mocking.ts
+++ b/lib/eleven-labs/mocking.ts
@@ -0,0 +1,41 @@
+import { ElevenVoice, ElevenVoiceList } from './index';
+import {
+  addUserVoice,
+  deleteUserVoice,
+  publicVoices,
+  teamMemberAddPrivateVoice,
+  teamMemberAddPublicVoice,
+  teamMemberPrivateVoice,
+  teamMemberPublicVoice,
+  teamOwnerAddPrivateVoice,
+  teamOwnerAddPublicVoice,
+  teamOwnerPrivateVoice,
+  teamOwnerPublicVoice,
+  userVoice,
+} from './mock';
+
+export const addToVoiceList = (
+  voiceList: ElevenVoiceList,
+  ...rest: ElevenVoice[]
+): ElevenVoiceList => {
+  return {
+    voices: [...voiceList.voices, ...rest],
+  };
+};
+
+export const mocks = {
+  publicVoices,
+  addUserVoice,
+  userVoice,
+  deleteUserVoice,
+
+  teamOwnerAddPublicVoice,
+  teamOwnerAddPrivateVoice,
+  teamOwnerPublicVoice,
+  teamOwnerPrivateVoice,
+
+  teamMemberAddPublicVoice,
+  teamMemberAddPrivateVoice,
+  teamMemberPublicVoice,
+  teamMemberPrivateVoice,
+};

diff --git a/lib/s3.ts b/lib/s3.ts
--- a/lib/s3.ts
+++ b/lib/s3.ts
@@ -292,6 +292,12 @@ export const readS3File = async (bucket: string, key: string) => {
     .then(result => result.Body!.transformToString());
 };

+export const readS3FileAsBuffer = async (bucket: string, key: string) => {
+  return getS3Client()
+    .send(new GetObjectCommand({ Bucket: bucket, Key: key }))
+    .then(result => result.Body!.transformToByteArray());
+};
+
 export const writeToS3 = (bucket: string, key: string, content: string) => {
   return getS3Client().send(
     new PutObjectCommand({ Bucket: bucket, Key: key, Body: content })

diff --git a/migrations/1702914061007-AddGeneratedVoice.ts b/migrations/1702914061007-AddGeneratedVoice.ts
--- a/migrations/1702914061007-AddGeneratedVoice.ts
+++ b/migrations/1702914061007-AddGeneratedVoice.ts
@@ -0,0 +1,120 @@
+import {
+  MigrationInterface,
+  QueryRunner,
+  Table,
+  TableColumn,
+  TableIndex,
+} from 'typeorm';
+
+export class AddGeneratedVoice1702914061007 implements MigrationInterface {
+  public async up(queryRunner: QueryRunner): Promise<void> {
+    await queryRunner.createTable(
+      new Table({
+        name: 'generated_voices',
+        columns: [
+          new TableColumn({
+            name: 'id',
+            type: 'uuid',
+            isNullable: false,
+            isPrimary: true,
+            generationStrategy: 'uuid',
+            isGenerated: true,
+          }),
+          new TableColumn({
+            name: 'elevenLabsId',
+            type: 'varchar',
+            isNullable: false,
+          }),
+          new TableColumn({
+            name: 'name',
+            type: 'varchar',
+            isNullable: false,
+          }),
+          new TableColumn({
+            name: 'description',
+            type: 'varchar',
+          }),
+          new TableColumn({
+            name: 'userId',
+            type: 'int4',
+            isNullable: false,
+          }),
+          new TableColumn({
+            name: 'createdBy',
+            type: 'int4',
+            isNullable: false,
+          }),
+          new TableColumn({
+            name: 'privateInTeam',
+            type: 'boolean',
+            isNullable: false,
+          }),
+          new TableColumn({
+            name: 'private',
+            type: 'boolean',
+            default: 'FALSE',
+            isNullable: false,
+          }),
+          new TableColumn({
+            name: 'files',
+            type: 'json',
+            isNullable: false,
+          }),
+          new TableColumn({
+            name: 'labels',
+            type: 'json',
+            default: "'{}'",
+            isNullable: false,
+          }),
+          new TableColumn({
+            name: 'createdAt',
+            type: 'timestamp',
+            default: 'now()',
+            isNullable: false,
+          }),
+          new TableColumn({
+            name: 'updatedAt',
+            type: 'timestamp',
+            default: 'now()',
+            isNullable: false,
+          }),
+
+          new TableColumn({
+            name: 'lastUsedAt',
+            type: 'timestamp',
+            default: 'now()',
+            isNullable: false,
+          }),
+          new TableColumn({
+            name: 'deleted',
+            type: 'boolean',
+            default: 'FALSE',
+            isNullable: false,
+          }),
+          new TableColumn({
+            name: 'active',
+            type: 'boolean',
+            default: 'TRUE',
+            isNullable: false,
+          }),
+        ],
+      })
+    );
+
+    await queryRunner.createIndex(
+      'generated_voices',
+      new TableIndex({
+        name: 'generated_voices_UserId_Active',
+        columnNames: ['userId', 'active'],
+      })
+    );
+  }
+
+  public async down(queryRunner: QueryRunner): Promise<void> {
+    await queryRunner.dropIndex(
+      'generated_voices',
+      'generated_voices_UserId_Active'
+    );
+    await queryRunner.dropTable('generated_voices', true);
+  }
+}

diff --git a/tests/data/test-voice.mp3 b/tests/data/test-voice.mp3
--- a/tests/data/test-voice.mp3
+++ b/tests/data/test-voice.mp3
(No changes in this file)

diff --git a/tests/integration/audio_generation.ts b/tests/integration/audio_generation.ts
--- a/tests/integration/audio_generation.ts
+++ b/tests/integration/audio_generation.ts
@@ -1,19 +1,21 @@
-import * as MockAdapter from 'axios-mock-adapter';
 import { assert, expect } from 'chai';
 import * as request from 'supertest';
 import * as uuid from 'uuid';

-import {
-  axiosInstance,
-  ELEVEN_LABS_HOST,
-} from '../../controllers/audio_generation';
 import { Dataset, DatasetConfig } from '../../entity/datasets/Dataset';
+import { addToVoiceList, mocks } from '../../lib/eleven-labs/mocking';
+import { clearTestMocks, setTestMock } from '../../lib/launch_darkly';
 import {
   createTestUser,
   getTestApi,
   getTestUserToken,
   TestUserInfo,
+  uploadAndCreateTestDataset,
 } from '../test_utils';
+import MockAdapter = require('axios-mock-adapter');
+import { User } from '../../entity/users/User';
+import { axiosInstance, ELEVEN_LABS_HOST } from '../../lib/eleven-labs';
+
 const createDatasetForUserAndTeam = async ({
   asTeamId,
   privateInTeam = false,
@@ -50,27 +52,20 @@ describe('integration/audio generation', async function () {

   before(async function () {
     api = await getTestApi();
-    testUserInfo = await createTestUser();
+    testUserInfo = await createTestUser({ gpuCredits: 10000 });
     token = await getTestUserToken(testUserInfo);
   });
-  beforeEach(async () => {
-    mock = new MockAdapter(axiosInstance);
-  });
-  afterEach(() => {
-    mock.restore();
-  });

   describe('audio generation', function () {
-    it('can get voices', async function () {
-      mock.onGet(`${ELEVEN_LABS_HOST}/voices`).reply(200, {
-        voices: [
-          {
-            voice_id: '3b8EQSEUpBlhuSeuMkJ4',
-            name: 'Rebekah - calm and modulated',
-          },
-        ],
-      });
+    beforeEach(async () => {
+      mock = new MockAdapter(axiosInstance);
+    });
+    afterEach(() => {
+      mock.restore();
+    });

+    it('can get voices', async function () {
+      mock.onGet(`${ELEVEN_LABS_HOST}/voices`).reply(200, mocks.publicVoices);
       await request(api)
         .get(`/v1/voices`)
         .set('Authorization', `Bearer ${token}`)
@@ -81,6 +76,22 @@ describe('integration/audio generation', async function () {
         });
     });

+    it('handles eleven labs 404', async function () {
+      mock.onGet(`${ELEVEN_LABS_HOST}/voices`).reply(404);
+      await request(api)
+        .get(`/v1/voices`)
+        .set('Authorization', `Bearer ${token}`)
+        .expect(503);
+    });
+
+    it('handles eleven labs timeout', async function () {
+      mock.onGet(`${ELEVEN_LABS_HOST}/voices`).timeout();
+      await request(api)
+        .get(`/v1/voices`)
+        .set('Authorization', `Bearer ${token}`)
+        .expect(503);
+    });
+
     it('can add to assets', async function () {
       const dset = await createDatasetForUserAndTeam({
         user: testUserInfo,
@@ -145,8 +156,11 @@ describe('integration/audio generation', async function () {
           .set('Authorization', `Bearer ${teamUserToken}`)
           .send({ name: 'CoffeeCo' })
           .expect(200)
-          .then(response => {
+          .then(async response => {
             teamId = response.body.team.id;
+            const team = await User.GetOneNotDeletedOrError(Number(teamId));
+            team.gpuCredits = 10000;
+            await team.save();
           });
       });

@@ -170,7 +184,6 @@ describe('integration/audio generation', async function () {
             publicTeamAssetId = res.body.asset.id;
           });
       });
-
       it('can add private assets to the team', async function () {
         const dset = await createDatasetForUserAndTeam({
           user: teamUser,
@@ -296,4 +309,553 @@ describe('integration/audio generation', async function () {
       });
     });
   });
+  describe('voice cloning', function () {
+    let datasetId: string;
+    let customVoiceId: string;
+    let customVoiceElevenId: string;
+    let nonTeamUserToken: string;
+    let teamId: number;
+    let nonTeamUserInfo: TestUserInfo;
+
+    before(async function () {
+      nonTeamUserInfo = await createTestUser({ gpuCredits: 10000 });
+      nonTeamUserToken = await getTestUserToken(nonTeamUserInfo);
+
+      setTestMock('grizzly-bear', true);
+      datasetId = await uploadAndCreateTestDataset({
+        token,
+        mediaType: { name: 'audio', type: 'audio' },
+        localDataFile: 'test-voice.mp3',
+        contentType: 'audio/mpeg',
+        isUserUpload: true,
+      });
+    });
+    after(async function () {
+      clearTestMocks();
+    });
+
+    beforeEach(async () => {
+      mock = new MockAdapter(axiosInstance);
+    });
+    afterEach(() => {
+      mock.restore();
+    });
+
+    it('should allow voice to be created for a user', async function () {
+      mock
+        .onPost(`${ELEVEN_LABS_HOST}/voices/add`)
+        .reply(200, mocks.addUserVoice);
+
+      await request(api)
+        .post(`/v1/generated_audio/voices`)
+        .send({
+          name: 'My new voice',
+          description: 'Description of voice',
+          samples: [datasetId],
+        })
+        .set('Authorization', `Bearer ${token}`)
+        .expect(200)
+        .then(res => {
+          customVoiceId = res.body.voice.id;
+          customVoiceElevenId = res.body.voice.voiceId;
+        });
+    });
+
+    it('can get voices', async function () {
+      mock
+        .onGet(`${ELEVEN_LABS_HOST}/voices`)
+        .reply(200, addToVoiceList(mocks.publicVoices, mocks.userVoice));
+      await request(api)
+        .get(`/v1/generated_audio/voices`)
+        .set('Authorization', `Bearer ${token}`)
+        .expect(200)
+        .then(res => {
+          const { voices } = res.body;
+          assert.isArray(voices);
+          assert.includeMembers(
+            voices.map(v => v.voiceId),
+            [customVoiceElevenId]
+          );
+        });
+    });
+
+    it('can update voice', async function () {
+      await request(api)
+        .put(`/v1/generated_audio/voices/${customVoiceId}`)
+        .set('Authorization', `Bearer ${token}`)
+        .send({
+          name: 'new name',
+          description: 'new desc',
+        })
+        .expect(200)
+        .then(res => {
+          const {
+            voice: { name: _name, description },
+          } = res.body;
+          assert.equal(_name, 'new name');
+          assert.equal(description, 'new desc');
+        });
+    });
+
+    it('can delete voice', async function () {
+      mock
+        .onDelete(`${ELEVEN_LABS_HOST}/voices/${customVoiceElevenId}`)
+        .reply(200, mocks.deleteUserVoice);
+      await request(api)
+        .delete(`/v1/generated_audio/voices/${customVoiceElevenId}`)
+        .set('Authorization', `Bearer ${token}`)
+        .expect(200);
+    });
+
+    it('cannot delete voice that doesnt exist', async function () {
+      await request(api)
+        .delete(`/v1/generated_audio/voices/invalidId`)
+        .set('Authorization', `Bearer ${token}`)
+        .expect(404);
+    });
+
+    it('cannot update voice that doesnt exist', async function () {
+      await request(api)
+        .put(`/v1/generated_audio/voices/${uuid()}`)
+        .set('Authorization', `Bearer ${token}`)
+        .send({
+          name: 'new name',
+          description: 'new desc',
+        })
+        .expect(404);
+    });
+
+    it('non team user with no custom voices only gets public voices', async function () {
+      mock
+        .onGet(`${ELEVEN_LABS_HOST}/voices`)
+        .reply(200, addToVoiceList(mocks.publicVoices, mocks.userVoice));
+
+      await request(api)
+        .get(`/v1/generated_audio/voices`)
+        .set('Authorization', `Bearer ${nonTeamUserToken}`)
+        .expect(200)
+        .then(res => {
+          const { voices } = res.body;
+          assert.isArray(voices);
+          assert(!voices.map(v => v.voiceId).includes(customVoiceId));
+        });
+    });
+    describe('can work with voices in teams', function () {
+      let privateTeamVoiceId: string;
+      let publicTeamVoiceId: string;
+      let nonTeamUserPublicTeamVoiceId: string;
+      let nonTeamUserPrivateTeamVoiceId: string;
+
+      it('team user can create team', async function () {
+        await request(api)
+          .post('/v1/teams')
+          .set('Authorization', `Bearer ${token}`)
+          .send({ name: 'CoffeeCo' })
+          .expect(200)
+          .then(async response => {
+            teamId = response.body.team.id;
+          });
+      });
+
+      it('should fail if not enough credits', async function () {
+        const team = await User.GetOneNotDeletedOrError(teamId);
+        team.workspaceConfig['allowance.numPlanCredits'] = 0;
+        await team.save();
+
+        mock
+          .onPost(`${ELEVEN_LABS_HOST}/voices/add`)
+          .reply(200, mocks.teamOwnerAddPublicVoice);
+
+        const teamPublicDatasetId = await uploadAndCreateTestDataset({
+          token,
+          mediaType: { name: 'audio', type: 'audio' },
+          localDataFile: 'test-voice.mp3',
+          contentType: 'audio/mpeg',
+          isUserUpload: true,
+          asTeamId: teamId,
+          privateInTeam: false,
+        });
+
+        await request(api)
+          .post(
+            `/v1/generated_audio/voices?asTeamId=${teamId}&privateInTeam=false`
+          )
+          .send({
+            name: 'My public team voice',
+            description: 'Description of voice',
+            samples: [teamPublicDatasetId],
+          })
+          .set('Authorization', `Bearer ${token}`)
+          .expect(403)
+          .then(async () => {
+            team.workspaceConfig['allowance.numPlanCredits'] = undefined;
+            team.gpuCredits = 10000;
+            await team.save();
+          });
+      });
+
+      it('should update credits', async function () {});
+
+      it('should allow a voice to be created public in team', async function () {
+        mock
+          .onPost(`${ELEVEN_LABS_HOST}/voices/add`)
+          .reply(200, mocks.teamOwnerAddPublicVoice);
+
+        const teamPublicDatasetId = await uploadAndCreateTestDataset({
+          token,
+          mediaType: { name: 'audio', type: 'audio' },
+          localDataFile: 'test-voice.mp3',
+          contentType: 'audio/mpeg',
+          isUserUpload: true,
+          asTeamId: teamId,
+          privateInTeam: false,
+        });
+
+        await request(api)
+          .post(
+            `/v1/generated_audio/voices?asTeamId=${teamId}&privateInTeam=false`
+          )
+          .send({
+            name: 'My public team voice',
+            description: 'Description of voice',
+            samples: [teamPublicDatasetId],
+          })
+          .set('Authorization', `Bearer ${token}`)
+          .expect(200)
+          .then(res => {
+            publicTeamVoiceId = res.body.voice.voiceId;
+          });
+      });
+
+      it('should allow a voice to be created private in team', async function () {
+        mock
+          .onPost(`${ELEVEN_LABS_HOST}/voices/add`)
+          .reply(200, mocks.teamOwnerAddPrivateVoice);
+
+        const teamPrivateDatasetId = await uploadAndCreateTestDataset({
+          token,
+          mediaType: { name: 'audio', type: 'audio' },
+          localDataFile: 'test-voice.mp3',
+          contentType: 'audio/mpeg',
+          isUserUpload: true,
+          asTeamId: teamId,
+          privateInTeam: true,
+        });
+
+        await request(api)
+          .post(
+            `/v1/generated_audio/voices?asTeamId=${teamId}&privateInTeam=true`
+          )
+          .send({
+            name: 'My private team voice',
+            description: 'Description of voice',
+            samples: [teamPrivateDatasetId],
+          })
+          .set('Authorization', `Bearer ${token}`)
+          .expect(200)
+          .then(res => {
+            privateTeamVoiceId = res.body.voice.voiceId;
+          });
+      });
+
+      it('non team user cannot try to use a voice that they do not have access to', async function () {
+        mock
+          .onGet(`${ELEVEN_LABS_HOST}/voices`)
+          .reply(
+            200,
+            addToVoiceList(
+              mocks.publicVoices,
+              mocks.userVoice,
+              mocks.teamOwnerPublicVoice,
+              mocks.teamOwnerPrivateVoice
+            )
+          );
+
+        await request(api)
+          .get(
+            `/v1/generated_audio/voices/${privateTeamVoiceId}/asTeamId=${teamId}`
+          )
+          .set('Authorization', `Bearer ${nonTeamUserToken}`)
+          .expect(404);
+
+        await request(api)
+          .get(`/v1/generated_audio/voices/${privateTeamVoiceId}`)
+          .set('Authorization', `Bearer ${nonTeamUserToken}`)
+          .expect(404);
+
+        await request(api)
+          .put(`/v1/generated_audio/voices/${privateTeamVoiceId}`)
+          .set('Authorization', `Bearer ${nonTeamUserToken}`)
+          .send({
+            name: 'new name',
+            description: 'new desc',
+          })
+          .expect(400);
+      });
+
+      it('add non team user to team as editor', async function () {
+        await request(api)
+          .post(`/v1/teams/${teamId}/members`)
+          .set('Authorization', `Bearer ${token}`)
+          .send({
+            email: nonTeamUserInfo.email,
+            role: 'editor',
+          })
+          .expect(200)
+          .then(response => {
+            assert.deepEqual(response.body, { success: true });
+          });
+      });
+      it('non team member  user accepts invite to team', async function () {
+        await request(api)
+          .put(`/v1/teams/${teamId}/members/accept`)
+          .set('Authorization', `Bearer ${nonTeamUserToken}`)
+          .expect(200);
+      });
+
+      it('editor team  user can create a private voice', async function () {
+        mock
+          .onPost(`${ELEVEN_LABS_HOST}/voices/add`)
+          .reply(200, mocks.teamMemberAddPrivateVoice);
+
+        const teamPrivateDatasetId = await uploadAndCreateTestDataset({
+          token,
+          mediaType: { name: 'audio', type: 'audio' },
+          localDataFile: 'test-voice.mp3',
+          contentType: 'audio/mpeg',
+          isUserUpload: true,
+          asTeamId: teamId,
+          privateInTeam: true,
+        });
+
+        await request(api)
+          .post(
+            `/v1/generated_audio/voices?asTeamId=${teamId}&privateInTeam=true`
+          )
+          .send({
+            name: 'My private team voice',
+            description: 'Description of voice',
+            samples: [teamPrivateDatasetId],
+          })
+          .set('Authorization', `Bearer ${nonTeamUserToken}`)
+          .expect(200)
+          .then(res => {
+            nonTeamUserPrivateTeamVoiceId = res.body.voice.voiceId;
+          });
+      });
+
+      it('editor team  user can create a public voice', async function () {
+        mock
+          .onPost(`${ELEVEN_LABS_HOST}/voices/add`)
+          .reply(200, mocks.teamMemberAddPublicVoice);
+
+        const teamPrivateDatasetId = await uploadAndCreateTestDataset({
+          token,
+          mediaType: { name: 'audio', type: 'audio' },
+          localDataFile: 'test-voice.mp3',
+          contentType: 'audio/mpeg',
+          isUserUpload: true,
+          asTeamId: teamId,
+          privateInTeam: false,
+        });
+
+        await request(api)
+          .post(
+            `/v1/generated_audio/voices?asTeamId=${teamId}&privateInTeam=false`
+          )
+          .send({
+            name: 'My private team voice',
+            description: 'Description of voice',
+            samples: [teamPrivateDatasetId],
+          })
+          .set('Authorization', `Bearer ${nonTeamUserToken}`)
+          .expect(200)
+          .then(res => {
+            nonTeamUserPublicTeamVoiceId = res.body.voice.voiceId;
+          });
+      });
+
+      it('editor team user gets public and their own private in team voices', async function () {
+        mock
+          .onGet(`${ELEVEN_LABS_HOST}/voices`)
+          .reply(
+            200,
+            addToVoiceList(
+              mocks.publicVoices,
+              mocks.userVoice,
+              mocks.teamOwnerPublicVoice,
+              mocks.teamOwnerPrivateVoice,
+              mocks.teamMemberPublicVoice,
+              mocks.teamMemberPrivateVoice
+            )
+          );
+        await request(api)
+          .get(
+            `/v1/generated_audio/voices?asTeamId=${teamId}&privateInTeam=${true}`
+          )
+          .set('Authorization', `Bearer ${nonTeamUserToken}`)
+          .expect(200)
+          .then(res => {
+            const { voices } = res.body;
+            const voiceIds = voices.map(v => v.voiceId);
+            assert(
+              !voiceIds.includes(publicTeamVoiceId),
+              'no iteam public voice'
+            );
+            assert(
+              !voiceIds.includes(privateTeamVoiceId),
+              'no team private voice'
+            );
+            assert(
+              !voiceIds.includes(nonTeamUserPublicTeamVoiceId),
+              'no public voide'
+            );
+            assert(
+              voiceIds.includes(nonTeamUserPrivateTeamVoiceId),
+              'gets private voicee'
+            );
+          });
+      });
+
+      it('editor team user gets public and public in team voices', async function () {
+        mock
+          .onGet(`${ELEVEN_LABS_HOST}/voices`)
+          .reply(
+            200,
+            addToVoiceList(
+              mocks.publicVoices,
+              mocks.userVoice,
+              mocks.teamOwnerPublicVoice,
+              mocks.teamOwnerPrivateVoice,
+              mocks.teamMemberPublicVoice,
+              mocks.teamMemberPrivateVoice
+            )
+          );
+
+        await request(api)
+          .get(
+            `/v1/generated_audio/voices?asTeamId=${teamId}&privateInTeam=${false}`
+          )
+          .set('Authorization', `Bearer ${nonTeamUserToken}`)
+          .expect(200)
+          .then(res => {
+            const { voices } = res.body;
+            const voiceIds = voices.map(v => v.voiceId);
+            assert(voiceIds.includes(publicTeamVoiceId));
+            assert(!voiceIds.includes(privateTeamVoiceId));
+            assert(voiceIds.includes(nonTeamUserPublicTeamVoiceId));
+            assert(!voiceIds.includes(nonTeamUserPrivateTeamVoiceId));
+          });
+      });
+
+      it('team user gets public and their own private in team voices', async function () {
+        mock
+          .onGet(`${ELEVEN_LABS_HOST}/voices`)
+          .reply(
+            200,
+            addToVoiceList(
+              mocks.publicVoices,
+              mocks.userVoice,
+              mocks.teamOwnerPublicVoice,
+              mocks.teamOwnerPrivateVoice,
+              mocks.teamMemberPublicVoice,
+              mocks.teamMemberPrivateVoice
+            )
+          );
+
+        await request(api)
+          .get(
+            `/v1/generated_audio/voices?asTeamId=${teamId}&privateInTeam=${true}`
+          )
+          .set('Authorization', `Bearer ${token}`)
+          .expect(200)
+          .then(res => {
+            const { voices } = res.body;
+            const voiceIds = voices.map(v => v.voiceId);
+
+            assert(!voiceIds.includes(publicTeamVoiceId));
+            assert(voiceIds.includes(privateTeamVoiceId));
+            assert(!voiceIds.includes(nonTeamUserPublicTeamVoiceId));
+            assert(!voiceIds.includes(nonTeamUserPrivateTeamVoiceId));
+          });
+      });
+      it('team user gets public and public in team voices', async function () {
+        mock
+          .onGet(`${ELEVEN_LABS_HOST}/voices`)
+          .reply(
+            200,
+            addToVoiceList(
+              mocks.publicVoices,
+              mocks.userVoice,
+              mocks.teamOwnerPublicVoice,
+              mocks.teamOwnerPrivateVoice,
+              mocks.teamMemberPublicVoice,
+              mocks.teamMemberPrivateVoice
+            )
+          );
+
+        await request(api)
+          .get(
+            `/v1/generated_audio/voices?asTeamId=${teamId}&privateInTeam=${false}`
+          )
+          .set('Authorization', `Bearer ${token}`)
+          .expect(200)
+          .then(res => {
+            const { voices } = res.body;
+            const voiceIds = voices.map(v => v.voiceId);
+
+            assert(voiceIds.includes(publicTeamVoiceId));
+            assert(!voiceIds.includes(privateTeamVoiceId));
+            assert(voiceIds.includes(nonTeamUserPublicTeamVoiceId));
+            assert(!voiceIds.includes(nonTeamUserPrivateTeamVoiceId));
+          });
+      });
+
+      it('deleting a user deletes their voices', async function () {
+        await request(api)
+          .delete('/v1/profile')
+          .set('Authorization', `Bearer ${nonTeamUserToken}`)
+          .send({ password: nonTeamUserInfo.password })
+          .expect(200);
+
+        mock
+          .onGet(`${ELEVEN_LABS_HOST}/voices`)
+          .reply(
+            200,
+            addToVoiceList(
+              mocks.publicVoices,
+              mocks.userVoice,
+              mocks.teamOwnerPublicVoice,
+              mocks.teamOwnerPrivateVoice,
+              mocks.teamMemberPublicVoice
+            )
+          );
+
+        await request(api)
+          .get(
+            `/v1/generated_audio/voices?asTeamId=${teamId}&privateInTeam=${false}`
+          )
+          .set('Authorization', `Bearer ${token}`)
+          .expect(200)
+          .then(res => {
+            const { voices } = res.body;
+            const voiceIds = voices.map(v => v.voiceId);
+
+            assert(voiceIds.includes(publicTeamVoiceId), 'gets public');
+            assert(
+              !voiceIds.includes(privateTeamVoiceId),
+              'does not have private'
+            );
+            assert(
+              voiceIds.includes(nonTeamUserPublicTeamVoiceId),
+              'has team member public'
+            );
+            assert(
+              !voiceIds.includes(nonTeamUserPrivateTeamVoiceId),
+              'does not have team member private'
+            );
+          });
+      });
+    });
+  });
 });

diff --git a/tests/test_utils.ts b/tests/test_utils.ts
--- a/tests/test_utils.ts
+++ b/tests/test_utils.ts
@@ -8,6 +8,7 @@ import * as request from 'supertest';
 import { FindOptionsWhere } from 'typeorm';
 import * as uuid from 'uuid';

+import { MediaSubtype, MediaType } from '../controllers/assets';
 import { APIKey } from '../entity/APIKey';
 import { Payment } from '../entity/payments/Payment';
 import { Task } from '../entity/tasks/Task';
@@ -73,7 +74,7 @@ export interface TestUserInfo {
 }

 export async function createTestUser(
-  opts: { email?: string } = {}
+  opts: { email?: string; gpuCredits?: number } = {}
 ): Promise<TestUserInfo> {
   const username = `test_user_${uuid().split('-')[0]}`;
   const password = uuid();
@@ -84,7 +85,7 @@ export async function createTestUser(
     firstName: 'Jenny',
     lastName: 'Rosen',
     isVerified: true,
-    gpuCredits: NEW_REGISTRATIONS_FREE_CREDIT_AMOUNT,
+    gpuCredits: opts.gpuCredits ?? NEW_REGISTRATIONS_FREE_CREDIT_AMOUNT,
   });
   user.generateVerificationCode();
   user.setPassword(password);
@@ -536,6 +537,13 @@ type UploadAndCreateTestDatasetOpts = {
   privateInTeam?: boolean;
   isUserUpload?: boolean;
   sourceApplication?: string;
+  mediaType?: {
+    name: MediaType;
+    type: MediaType;
+    subType?: MediaSubtype;
+  };
+  localDataFile?: string;
+  contentType?: string;
 };

 export async function uploadAndCreateTestDataset(
@@ -547,6 +555,9 @@ export async function uploadAndCreateTestDataset(
     request(api)
       .post('/v1/datasets/upload')
       .set('Authorization', `Bearer ${opts.token}`)
+      .send({
+        contentType: opts.contentType,
+      })
       .expect(200)
       .then(response => {
         datasetUploadUrl = response.body.uploadUrl;
@@ -563,7 +574,7 @@ export async function uploadAndCreateTestDataset(

   await Promise.all([
     uploadFileToS3UsingPresignedPOST(
-      `${__dirname}/data/test-dataset.tar`,
+      `${__dirname}/data/${opts.localDataFile ?? 'test-dataset.tar'}`,
       datasetUploadUrl
     ),
     uploadFileToS3UsingPresignedPOST(
@@ -574,7 +585,7 @@ export async function uploadAndCreateTestDataset(

   const datasetId = await createTestDataset({
     ...opts,
-    type: { name: 'images', type: 'images' },
+    type: opts.mediaType ?? { name: 'images', type: 'images' },
     datasetUploadUrl,
     previewUploadUrl,
   });
@@ -592,6 +603,7 @@ type CreateTestDataSetOpts = {
   datasetUploadUrl?;
   previewUploadUrl?;
   createAsAdmin?: boolean;
+  uploadId?: string;
 };

 export async function createTestDataset({
@@ -604,6 +616,7 @@ export async function createTestDataset({
   datasetUploadUrl,
   previewUploadUrl,
   createAsAdmin = false,
+  uploadId,
 }: CreateTestDataSetOpts) {
   let datasetId: string;

@@ -626,6 +639,9 @@ export async function createTestDataset({
       ...(datasetUploadUrl && {
         filename: datasetUploadUrl.fields.key,
       }),
+      ...(uploadId && {
+        uploadId,
+      }),
       ...(previewUploadUrl && {
         previewFilenames: [previewUploadUrl.fields.key],
       }),