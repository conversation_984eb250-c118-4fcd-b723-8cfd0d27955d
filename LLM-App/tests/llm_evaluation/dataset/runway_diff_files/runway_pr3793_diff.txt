PR #3793: [athena] support custom prompt modifiers
================================================================================

diff --git a/lib/athena/prompt-modifier.ts b/lib/athena/prompt-modifier.ts
--- a/lib/athena/prompt-modifier.ts
+++ b/lib/athena/prompt-modifier.ts
@@ -1,3 +1,7 @@
+import { z } from 'zod';
+
+import { CustomPreset } from '../../entity/CustomPreset';
+
 enum Category {
   Camera = 'camera',
   Movement = 'movement',
@@ -18,14 +22,36 @@ export class PromptModifier {
     return MODIFIERS.get(modifierId);
   }

-  public static apply(prompt: string, modifier: PromptModifierItem): string {
+  public static apply(prompt: string, textPrompt: string): string {
     if (prompt.includes('{{subject}}')) {
       return prompt.replace('{{subject}}', prompt);
     }

     return prompt.endsWith('.')
-      ? `${prompt} ${modifier.textPrompt}`
-      : `${prompt}. ${modifier.textPrompt}`;
+      ? `${prompt} ${textPrompt}`
+      : `${prompt}. ${textPrompt}`;
+  }
+
+  public static async fetchPresetModifier(
+    presetId: number,
+    userId: number
+  ): Promise<string | null> {
+    const customPreset = await CustomPreset.getOneBy({
+      userId,
+      id: presetId,
+      taskType: 'athena',
+      deleted: false,
+    });
+
+    if (!customPreset) return null;
+
+    const result = z
+      .object({ textPrompt: z.string() })
+      .safeParse(customPreset.settings);
+
+    if (!result.success) return null;
+
+    return result.data.textPrompt;
   }
 }


diff --git a/lib/taskDefinitions/definitions/athena.ts b/lib/taskDefinitions/definitions/athena.ts
--- a/lib/taskDefinitions/definitions/athena.ts
+++ b/lib/taskDefinitions/definitions/athena.ts
@@ -57,7 +57,20 @@ const AthenaTaskOptionsSchema = z
     wathermark: z.boolean().optional(),
     exploreMode: z.boolean().optional(),
     assetGroupId: z.string().optional(),
-    prompt_modifiers: z.array(z.string()).optional(),
+    prompt_modifiers: z
+      .array(
+        z.union([
+          z.object({
+            type: z.literal('predefined'),
+            id: z.string(),
+          }),
+          z.object({
+            type: z.literal('custom'),
+            id: z.number(),
+          }),
+        ])
+      )
+      .optional(),
     // internal settings options:
     enhance_prompt: z.boolean().optional(),
     cfg_scale: z.number().optional(),
@@ -100,6 +113,8 @@ type AthenaTaskOptions = z.infer<typeof AthenaTaskOptionsSchema>;

 interface EnhancementOptions {
   backend_enhanced_prompt?: string | null;
+  text_prompt?: string;
+  user_text_prompt?: string;
 }

 export default {
@@ -157,7 +172,7 @@ export default {

     if (options.prompt_modifiers) {
       for (const modifierId of options.prompt_modifiers) {
-        if (!PromptModifier.has(modifierId)) {
+        if (typeof modifierId === 'string' && !PromptModifier.has(modifierId)) {
           throw new BadRequestError(`Invalid prompt modifier: ${modifierId}`);
         }
       }
@@ -258,31 +273,19 @@ export default {
       // generated as a result of enhancing the input
       const enhancementOptions: EnhancementOptions = {};

-      const userTextPrompt = getTextPrompt(taskOptions);
-
       const athenaTaskOptions = taskOptions as AthenaTaskOptions;
-
-      let textPrompt = userTextPrompt;
-      if (athenaTaskOptions.prompt_modifiers) {
-        const initialPrompt = userTextPrompt ?? '';
-        textPrompt = athenaTaskOptions.prompt_modifiers.reduce(
-          (prompt, modifierId) => {
-            const modifier = PromptModifier.get(modifierId);
-            if (!modifier) return prompt;
-            return PromptModifier.apply(prompt, modifier);
-          },
-          initialPrompt
-        );
-      }
+      const { textPrompt, updatedUserTextPrompt, originalUserTextPrompt } =
+        await parsePromptModifiers(athenaTaskOptions, owner.id);

       if (taskOptions.enhance_prompt === false) {
-        if (textPrompt && textPrompt !== userTextPrompt) {
-          enhancementOptions.backend_enhanced_prompt = textPrompt;
-        }
-
         return enhancementOptions;
       }

+      if (updatedUserTextPrompt) {
+        enhancementOptions.text_prompt = updatedUserTextPrompt;
+        enhancementOptions.user_text_prompt = originalUserTextPrompt;
+      }
+
       // Handle image to video case
       if (taskOptions.init_image) {
         return await handleImageToVideoEnhancement(
@@ -417,3 +420,66 @@ export default {

   publicAlias: ['athena'],
 } satisfies TaskConfig;
+
+async function parsePromptModifiers(
+  options: AthenaTaskOptions,
+  userId: number
+) {
+  const originalTextPrompt = getTextPrompt(options) ?? '';
+  const promptModifierIdList = options.prompt_modifiers;
+
+  let modifiedTextPrompt = originalTextPrompt;
+  let updatedUserTextPrompt = originalTextPrompt;
+
+  for (const modifierOption of promptModifierIdList ?? []) {
+    if (modifierOption.type === 'predefined') {
+      const modifier = PromptModifier.get(modifierOption.id);
+      if (!modifier) {
+        throw new BadRequestError(
+          `Invalid prompt modifier: ${modifierOption.id}`
+        );
+      }
+      modifiedTextPrompt = PromptModifier.apply(
+        modifiedTextPrompt,
+        modifier.textPrompt
+      );
+      continue;
+    }
+
+    // custom user-defined preset modifier
+    const presetModifier = await PromptModifier.fetchPresetModifier(
+      modifierOption.id,
+      userId
+    );
+    if (!presetModifier) {
+      throw new BadRequestError(
+        `Invalid prompt modifier: ${modifierOption.id}`
+      );
+    }
+    modifiedTextPrompt = PromptModifier.apply(
+      modifiedTextPrompt,
+      presetModifier
+    );
+    updatedUserTextPrompt = PromptModifier.apply(
+      updatedUserTextPrompt,
+      presetModifier
+    );
+  }
+
+  if (originalTextPrompt === modifiedTextPrompt) {
+    // no prompt modifications
+    return { textPrompt: originalTextPrompt };
+  }
+
+  if (originalTextPrompt === updatedUserTextPrompt) {
+    // no user-defined prompt modifications
+    return { textPrompt: modifiedTextPrompt };
+  }
+
+  // user-defined prompt modifications
+  return {
+    textPrompt: modifiedTextPrompt,
+    updatedUserTextPrompt,
+    originalUserTextPrompt: originalTextPrompt,
+  };
+}