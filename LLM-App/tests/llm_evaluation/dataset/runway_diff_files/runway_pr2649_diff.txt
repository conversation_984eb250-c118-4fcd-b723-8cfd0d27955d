PR #2649: add foreign key to generation pairs table
================================================================================

diff --git a/migrations/1718734535900-AddForeignKeyBatchIdToGenerationPair.ts b/migrations/1718734535900-AddForeignKeyBatchIdToGenerationPair.ts
--- a/migrations/1718734535900-AddForeignKeyBatchIdToGenerationPair.ts
+++ b/migrations/1718734535900-AddForeignKeyBatchIdToGenerationPair.ts
@@ -0,0 +1,25 @@
+import { MigrationInterface, QueryRunner, TableForeignKey } from 'typeorm';
+
+export class AddForeignKeyBatchIdToGenerationPair1718734535900
+  implements MigrationInterface
+{
+  public async up(queryRunner: QueryRunner): Promise<void> {
+    await queryRunner.createForeignKey(
+      'generation_pairs',
+      new TableForeignKey({
+        name: 'generation_pairs__generation_batch_id',
+        columnNames: ['batchId'],
+        referencedTableName: 'generation_batches',
+        referencedColumnNames: ['id'],
+        onDelete: 'CASCADE',
+      })
+    );
+  }
+
+  public async down(queryRunner: QueryRunner): Promise<void> {
+    await queryRunner.dropForeignKey(
+      'generation_pairs',
+      'generation_pairs__generation_batch_id'
+    );
+  }
+}