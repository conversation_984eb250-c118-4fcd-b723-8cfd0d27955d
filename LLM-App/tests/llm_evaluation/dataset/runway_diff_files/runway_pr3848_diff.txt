PR #3848: RW-16142: Custom presets privateInTeam
================================================================================

diff --git a/controllers/custom_presets.test.ts b/controllers/custom_presets.test.ts
--- a/controllers/custom_presets.test.ts
+++ b/controllers/custom_presets.test.ts
@@ -205,7 +205,7 @@ describe('custom presets', () => {
       .expect(400)
       .then(response => {
         expect(response.body.error).toBe(
-          'At least one of "name", "settings", or "tags" must be provided'
+          'At least one of "name", "settings", "tags", or "privateInTeam" must be provided'
         );
       });
   });

diff --git a/controllers/custom_presets.ts b/controllers/custom_presets.ts
--- a/controllers/custom_presets.ts
+++ b/controllers/custom_presets.ts
@@ -4,9 +4,9 @@ import { IsNull } from 'typeorm';
 import { CustomPreset } from '../entity/CustomPreset';
 import { Task } from '../entity/tasks/Task';
 import { User } from '../entity/users/User';
-import { UserMembership } from '../entity/users/UserMembership';
+import { MembershipRole, UserMembership } from '../entity/users/UserMembership';
 import { delete_, get, post, put, validation } from '../lib/apiInterface';
-import { BadRequestError } from '../lib/errors';
+import { BadRequestError, NotFoundError } from '../lib/errors';
 import { moderateTextPrompt } from '../lib/europa/europa';
 import { EUROPA_T2V_MODERATION_SYSTEM_PROMPT } from '../lib/europa/system_prompts_constants';
 import { ApiAuditContext } from '../lib/eventStreaming/transport';
@@ -61,13 +61,22 @@ export default () => {
         user: req.user,
         asTeamId: req.query.asTeamId,
       });
+      // first case -- legacy, pre-createdBy, public by default
+      // second case -- explicitly set to be public to the workspace
+      // third case -- belongs to the user
+      // TODO -- this can be cleaned up once things are backfilled
       const customPresets = await CustomPreset.find({
         where: [
           {
             userId: user.id,
             deleted: false,
             createdBy: IsNull(),
           },
+          {
+            userId: user.id,
+            deleted: false,
+            privateInTeam: false,
+          },
           {
             userId: user.id,
             deleted: false,
@@ -112,6 +121,12 @@ export default () => {
         deleted: false,
       });

+      if (customPreset.privateInTeam && customPreset.userId !== req.user.id) {
+        // if the preset is private and doesn't belong to this user, act as if
+        // it does not exist. Otherwise, the user can edit it
+        throw new NotFoundError();
+      }
+
       return SuccessWithBody(
         res,
         transformCustomPresetToResponse(customPreset)
@@ -129,15 +144,22 @@ export default () => {
         settings: validation.json,
         taskType: validation.enum(['gen2', 'text_to_image', 'athena']),
         tags: validation.optionalArrayOfEnums(ATHENA_PRESET_CATEGORIES),
+        privateInTeam: validation.optionalBoolean,
       },
     },
     async (req, res) => {
-      const { name, settings: rawSettings, tags = [] } = req.body;
+      const {
+        name,
+        settings: rawSettings,
+        tags = [],
+        privateInTeam,
+      } = req.body;
       const settings = Task.sanitizeOptions(rawSettings, OPTIONS_SANITIZATION);

       const owner = await UserMembership.GetTeamIfSet({
         user: req.user,
         asTeamId: req.body.asTeamId,
+        role: MembershipRole.Editor,
       });

       if (
@@ -147,6 +169,8 @@ export default () => {
           deleted: false,
         })
       ) {
+        // TODO -- it's possible this should be updated to allow the same name for private presets,
+        // but for now it's required to be unique per-workspace no matter what
         throw new BadRequestError(
           'A preset with this name already exists. Please choose a different name.'
         );
@@ -171,6 +195,7 @@ export default () => {
         taskType: req.body.taskType as TaskType,
         version: 1,
         tags: [...tags],
+        privateInTeam,
       });

       return SuccessWithBody(res, transformCustomPresetToResponse(preset));
@@ -187,28 +212,51 @@ export default () => {
         name: validation.optionalString,
         settings: validation.optionalJSON,
         tags: validation.optionalArrayOfEnums(ATHENA_PRESET_CATEGORIES),
+        privateInTeam: validation.optionalBoolean,
       },
     },
     async (req, res) => {
-      const { name, settings, tags } = req.body;
+      const { name, settings, tags, privateInTeam } = req.body;

-      if (!name && !settings && !tags) {
+      if (!name && !settings && !tags && privateInTeam === undefined) {
         throw new BadRequestError(
-          'At least one of "name", "settings", or "tags" must be provided'
+          'At least one of "name", "settings", "tags", or "privateInTeam" must be provided'
         );
       }

       // TODO: Wrap the body of this method in a concurrency lock
       const owner = await UserMembership.GetTeamIfSet({
         user: req.user,
         asTeamId: req.body.asTeamId,
+        role: MembershipRole.Editor,
       });
-      const customPreset = await CustomPreset.getOneBy({
-        userId: owner.id,
-        id: req.params.id,
-        deleted: false,
+      const customPreset = await CustomPreset.getOne({
+        where: [
+          {
+            userId: owner.id,
+            id: req.params.id,
+            deleted: false,
+            createdBy: IsNull(),
+          },
+          {
+            userId: owner.id,
+            id: req.params.id,
+            deleted: false,
+            privateInTeam: false,
+          },
+          {
+            userId: owner.id,
+            id: req.params.id,
+            deleted: false,
+            createdBy: req.user.id,
+          },
+        ],
       });

+      if (customPreset.privateInTeam && customPreset.userId !== req.user.id) {
+        throw new NotFoundError();
+      }
+
       if (name && name !== customPreset.name) {
         // Don't allow duplicate names
         if (
@@ -227,7 +275,6 @@ export default () => {

       if (settings) {
         const newSettings = settings as Record<string, unknown>;
-
         customPreset.settings = Task.sanitizeOptions(
           newSettings,
           OPTIONS_SANITIZATION
@@ -248,6 +295,10 @@ export default () => {
         customPreset.tags = [...tags];
       }

+      if (privateInTeam !== undefined) {
+        customPreset.privateInTeam = privateInTeam;
+      }
+
       await customPreset.save();

       return SuccessWithBody(
@@ -270,6 +321,7 @@ export default () => {
       const user = await UserMembership.GetTeamIfSet({
         user: req.user,
         asTeamId: req.query.asTeamId,
+        role: MembershipRole.Editor,
       });
       const customPreset = await CustomPreset.findOneBy({
         userId: user.id,
@@ -300,6 +352,7 @@ export default () => {
       const user = await UserMembership.GetTeamIfSet({
         user: req.user,
         asTeamId: req.body.asTeamId,
+        role: MembershipRole.Editor,
       });

       await CustomPreset.getRepository().increment(
@@ -333,6 +386,9 @@ const transformCustomPresetToResponse = (preset: CustomPreset) => {
     version: preset.version,
     taskType: preset.taskType,
     tags: preset.tags,
+    privateInTeam: preset.privateInTeam,
+    userId: preset.userId,
+    createdBy: preset.createdBy,
   };
 };


diff --git a/entity/CustomPreset.ts b/entity/CustomPreset.ts
--- a/entity/CustomPreset.ts
+++ b/entity/CustomPreset.ts
@@ -21,6 +21,7 @@ export type CustomPresetConfig = {
   settings: Record<string, unknown>;
   taskType: TaskType;
   tags?: string[];
+  privateInTeam?: boolean;
 };

 @ExportTableToRedshift({
@@ -100,6 +101,16 @@ export class CustomPreset extends RunwayBaseEntity {
   })
   version!: number;

+  @ExportFieldToRedshift()
+  @Column({
+    type: 'boolean',
+    default: true,
+    nullable: true, // for now, nullable to keep existing records as public -- TODO change
+    comment:
+      'Whether the preset is private to the team (can only be viewed by the creating user)',
+  })
+  privateInTeam!: boolean;
+
   @Column()
   deleted!: boolean;

@@ -112,6 +123,7 @@ export class CustomPreset extends RunwayBaseEntity {
       settings: config.settings,
       taskType: config.taskType || 'gen2',
       tags: config.tags || [],
+      privateInTeam: config.privateInTeam,
     }).save();
   }


diff --git a/migrations/1738877517636-AddPrivateInTeamToCustomPresets.ts b/migrations/1738877517636-AddPrivateInTeamToCustomPresets.ts
--- a/migrations/1738877517636-AddPrivateInTeamToCustomPresets.ts
+++ b/migrations/1738877517636-AddPrivateInTeamToCustomPresets.ts
@@ -0,0 +1,21 @@
+import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';
+
+export class AddPrivateInTeamToCustomPresets1738877517636
+  implements MigrationInterface
+{
+  public async up(queryRunner: QueryRunner): Promise<void> {
+    await queryRunner.addColumn(
+      'custom_presets',
+      new TableColumn({
+        name: 'privateInTeam',
+        type: 'boolean',
+        default: true,
+        isNullable: true,
+      })
+    );
+  }
+
+  public async down(queryRunner: QueryRunner): Promise<void> {
+    await queryRunner.dropColumn('custom_presets', 'privateInTeam');
+  }
+}