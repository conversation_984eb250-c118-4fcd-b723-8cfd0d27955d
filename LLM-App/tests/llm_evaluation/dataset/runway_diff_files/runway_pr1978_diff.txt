PR #1978: Project audio transcript search
================================================================================

diff --git a/controllers/audio_generation.ts b/controllers/audio_generation.ts
--- a/controllers/audio_generation.ts
+++ b/controllers/audio_generation.ts
@@ -4,11 +4,21 @@ import axiosRetry from 'axios-retry';
 import * as express from 'express';
 import * as uuid from 'uuid';

+import { Dataset, DatasetConfig } from '../entity/datasets/Dataset';
+import {
+  GeneratedAudioTranscript,
+  TranscriptConfig,
+} from '../entity/GeneratedAudioTranscript';
+import { MembershipRole, UserMembership } from '../entity/users/UserMembership';
 import { get, post, validation } from '../lib/apiInterface';
-import { makeAssetURLEphemeral } from '../lib/assetURLs';
+import {
+  makeAssetURLEphemeral,
+  verifyEphemeralAssetURL,
+} from '../lib/assetURLs';
 import { DATASETS_BUCKET } from '../lib/constants';
+import { BadRequestError } from '../lib/errors';
 import { respondWith500 } from '../lib/responses';
-import { uploadToS3 } from '../lib/s3';
+import { getObjectSizeS3, parseS3URLToParts, uploadToS3 } from '../lib/s3';
 const ELEVEN_LABS_API_KEY = process.env.ELEVEN_LABS_API_KEY;
 const ELEVEN_LABS_HOST = 'https://api.elevenlabs.io/v1';
 const ELEVEN_LABS_AXIOS_CONFIG = {
@@ -17,6 +27,115 @@ const ELEVEN_LABS_AXIOS_CONFIG = {
 };
 export default () => {
   const router = express.Router();
+
+  post(
+    router,
+    '/v1/generated_audio/search',
+    {
+      query: {
+        query: validation.string,
+        asTeamId: validation.legacyAsTeamId,
+      },
+    },
+    async (req, res) => {
+      const { query, asTeamId } = req.query;
+      const team = await UserMembership.GetTeamIfSet({
+        user: req.user,
+        asTeamId,
+        role: MembershipRole.Viewer,
+      });
+      try {
+        const results = await GeneratedAudioTranscript.Search({
+          userId: team.id,
+          createdBy: req.user.id,
+          query,
+        });
+
+        const cleanResults = await Promise.all(
+          results.map(async ({ dataset, transcript }) => ({
+            asset: await dataset.asset(),
+            transcript,
+          }))
+        );
+        return res.status(200).json({
+          results: cleanResults,
+        });
+      } catch (err) {
+        Sentry.captureException(err);
+        throw new Error('Search could not be completed');
+      }
+    }
+  );
+  post(
+    router,
+    '/v1/generated_audio/add_to_assets',
+    {
+      body: {
+        url: validation.string,
+        name: validation.string,
+        transcript: validation.string,
+        asTeamId: validation.legacyAsTeamId,
+        privateInTeam: validation.optionalBoolean,
+      },
+    },
+    async (req, res) => {
+      const {
+        url,
+        name: fname,
+        asTeamId,
+        privateInTeam = true,
+        transcript,
+      } = req.body;
+      const s3Url = verifyEphemeralAssetURL(url);
+      if (!s3Url) {
+        throw new BadRequestError('Invalid url');
+      }
+      const { key } = parseS3URLToParts(s3Url);
+      const team = await UserMembership.GetTeamIfSet({
+        user: req.user,
+        asTeamId,
+        role: MembershipRole.Editor,
+      });
+      const [nonConflictingName, size] = await Promise.all([
+        Dataset.GetNonConflictingName({
+          userId: team.id,
+          createdBy: req.user.id,
+          name: fname,
+          privateInTeam,
+        }),
+        getObjectSizeS3(DATASETS_BUCKET, key),
+      ]);
+      const config: DatasetConfig = {
+        user: req.user.id,
+        name: nonConflictingName,
+        type: 'audio',
+        url,
+        filename: fname,
+        previewFilenames: [],
+        fileCount: 1,
+        size,
+        private: true,
+        privateInTeam,
+        asTeamId: team.id,
+        isUserUpload: false,
+        createdBy: req.user.id,
+      };
+      const dataset = await Dataset.Create(config);
+      const transcriptConfig: TranscriptConfig = {
+        userId: team.id,
+        datasetId: dataset.id,
+        text: transcript,
+      };
+      const genAudioTranscript = await GeneratedAudioTranscript.Create(
+        transcriptConfig
+      );
+      return res.status(200).json({
+        asset: await dataset.asset({ user: team }),
+        transcript: genAudioTranscript.text,
+      });
+    }
+  );
+
   post(
     router,
     '/v1/text_to_speech/:id',
@@ -48,7 +167,6 @@ export default () => {
               stability,
             }
           : undefined;
-
       try {
         const resp = await client.post(
           `${ELEVEN_LABS_HOST}/text-to-speech/${id}`,

diff --git a/entity/GeneratedAudioTranscript.ts b/entity/GeneratedAudioTranscript.ts
--- a/entity/GeneratedAudioTranscript.ts
+++ b/entity/GeneratedAudioTranscript.ts
@@ -0,0 +1,112 @@
+import {
+  Column,
+  CreateDateColumn,
+  Entity,
+  getRepository,
+  In,
+  PrimaryGeneratedColumn,
+} from 'typeorm';
+
+import { RunwayBaseEntity } from '../lib/RunwayBaseEntity';
+import { Dataset } from './datasets/Dataset';
+
+export type TranscriptConfig = {
+  userId: number;
+  datasetId: string;
+  text: string;
+};
+
+export type SearchOpts = {
+  query: string;
+  userId: number;
+  createdBy: number;
+};
+
+export type TranscriptAndDataset = {
+  transcript: string;
+  dataset: Dataset;
+};
+@Entity({ name: 'generated_audio_transcripts' })
+export class GeneratedAudioTranscript extends RunwayBaseEntity {
+  @PrimaryGeneratedColumn()
+  id!: number;
+
+  @Column({ type: 'uuid', nullable: false })
+  datasetId!: string;
+
+  @CreateDateColumn()
+  createdAt!: Date;
+
+  @Column({ type: 'int4' })
+  userId!: number;
+
+  @Column({ type: 'text' })
+  text!: string;
+
+  // This column is updated by the database via a TRIGGER
+  // Typeorm should not manage it
+  @Column({ type: 'tsvector', readonly: true, update: false })
+  text_vector!: string;
+
+  static async Create(config: TranscriptConfig) {
+    const transcript = new GeneratedAudioTranscript();
+    transcript.datasetId = config.datasetId;
+    transcript.userId = config.userId;
+    transcript.text = config.text;
+    await transcript.save();
+    return transcript;
+  }
+
+  static async Search(opts: SearchOpts): Promise<TranscriptAndDataset[]> {
+    const transcripts = await getRepository(GeneratedAudioTranscript)
+      .createQueryBuilder('generated_audio_transcript')
+      .where('generated_audio_transcript."userId" = :userId', {
+        userId: opts.userId,
+      })
+      .andWhere(
+        "to_tsvector('public.generated_audio_transcripts_search', generated_audio_transcript.text) @@ to_tsquery('public.generated_audio_transcripts_search', :query)",
+        { query: opts.query }
+      )
+      .execute();
+
+    /*
+     * Some notes on the below code:
+     *
+     * generated_audio_trancripts has an onDelete CASCADE
+     * defined for datasetId
+     *
+     *
+     * This code will find any dataset that corresponds to
+     * a transcript. This is expected to be 1:1
+     *
+     * If for some reason it is not, we then rely on the
+     * number of datasets that are returned. If that dataset
+     * was found in the database, the transcript must have to
+     * have been as well.
+     */
+
+    const datasets = await Dataset.findBy({
+      id: In(transcripts.map(t => t.generated_audio_transcript_datasetId)),
+      userId: opts.userId,
+    });
+
+    const transcriptMapByDatasetId = transcripts.reduce((accum, t) => {
+      return {
+        ...accum,
+        [t.generated_audio_transcript_datasetId]: t,
+      };
+    }, {});
+
+    const combined = datasets
+      .filter(d => {
+        return d.privateInTeam ? d.createdBy === opts.createdBy : true;
+      })
+      .map(d => ({
+        transcript: transcriptMapByDatasetId[d.id]
+          .generated_audio_transcript_text as string,
+        dataset: d,
+      }));
+
+    return combined;
+  }
+}

diff --git a/lib/dbConnection.ts b/lib/dbConnection.ts
--- a/lib/dbConnection.ts
+++ b/lib/dbConnection.ts
@@ -33,6 +33,7 @@ import { EndpointRequests } from '../entity/endpoints/EndpointRequests';
 import { ExternalUser } from '../entity/ExternalUser';
 import { FeatureUsageCounter } from '../entity/FeatureUsageCounter';
 import { UserFeedback } from '../entity/Feedback';
+import { GeneratedAudioTranscript } from '../entity/GeneratedAudioTranscript';
 import { IdempotentFeatureUseToken } from '../entity/IdempotentFeatureUseToken';
 import { Model } from '../entity/models/Model';
 import { ModelBuild } from '../entity/models/ModelBuild';
@@ -173,6 +174,7 @@ export const establishDatabaseConnection = async (
     SSODomainRegistration,
     SSOOIDCConfig,
     SSOOIDCState,
+    GeneratedAudioTranscript,
   ];
   const exponentialRetry = customizeRetry({
     backoff: 'EXPONENTIAL',

diff --git a/migrations/1698091259275-AddGenerativeAudioTranscriptSearch.ts b/migrations/1698091259275-AddGenerativeAudioTranscriptSearch.ts
--- a/migrations/1698091259275-AddGenerativeAudioTranscriptSearch.ts
+++ b/migrations/1698091259275-AddGenerativeAudioTranscriptSearch.ts
@@ -0,0 +1,93 @@
+import {
+  MigrationInterface,
+  QueryRunner,
+  Table,
+  TableColumn,
+  TableForeignKey,
+} from 'typeorm';
+
+export class AddGenerativeAudioTranscriptSearch1698091259275
+  implements MigrationInterface
+{
+  public async up(queryRunner: QueryRunner): Promise<void> {
+    await queryRunner.query('CREATE EXTENSION IF NOT EXISTS "pg_trgm"');
+    await queryRunner.query(
+      `CREATE TEXT SEARCH CONFIGURATION public.generated_audio_transcripts_search (copy=simple)`
+    );
+    await queryRunner.query(
+      `ALTER TEXT SEARCH CONFIGURATION public.generated_audio_transcripts_search ALTER MAPPING FOR word WITH simple`
+    );
+
+    await queryRunner.createTable(
+      new Table({
+        name: 'generated_audio_transcripts',
+        columns: [
+          new TableColumn({
+            name: 'id',
+            type: 'SERIAL',
+            isNullable: false,
+            isPrimary: true,
+          }),
+          new TableColumn({
+            name: 'datasetId',
+            type: 'uuid',
+            isNullable: false,
+          }),
+          new TableColumn({
+            name: 'createdAt',
+            type: 'timestamp',
+            default: 'now()',
+          }),
+          new TableColumn({
+            name: 'userId',
+            type: 'int4',
+          }),
+          new TableColumn({
+            name: 'text',
+            type: 'TEXT',
+          }),
+          new TableColumn({
+            name: 'text_vector',
+            type: 'tsvector',
+          }),
+        ],
+      })
+    );
+    await queryRunner.query(`CREATE TRIGGER generated_audio_text_vector_update
+                            BEFORE INSERT OR UPDATE
+                            ON generated_audio_transcripts
+                            FOR EACH ROW
+                            EXECUTE FUNCTION tsvector_update_trigger(text_vector, 'public.generated_audio_transcripts_search', text);
+                            `);
+    await queryRunner.createForeignKey(
+      'generated_audio_transcripts',
+      new TableForeignKey({
+        columnNames: ['datasetId'],
+        referencedTableName: 'datasets',
+        referencedColumnNames: ['id'],
+        onDelete: 'CASCADE',
+      })
+    );
+
+    await queryRunner.createForeignKey(
+      'generated_audio_transcripts',
+      new TableForeignKey({
+        columnNames: ['userId'],
+        referencedTableName: 'users',
+        referencedColumnNames: ['id'],
+        onDelete: 'CASCADE',
+      })
+    );
+  }
+
+  public async down(queryRunner: QueryRunner): Promise<void> {
+    await queryRunner.query('DROP EXTENSION IF EXISTS "pg_trgm"');
+    await queryRunner.query(
+      `DROP TEXT SEARCH CONFIGURATION IF EXISTS generated_audio_transcripts_search`
+    );
+    await queryRunner.query(
+      `DROP TRIGGER IF EXISTS generated_audio_text_vector_update on generated_audio_transcripts`
+    );
+    await queryRunner.dropTable('generated_audio_transcripts', true);
+  }
+}

diff --git a/tests/integration/audio_generation.ts b/tests/integration/audio_generation.ts
--- a/tests/integration/audio_generation.ts
+++ b/tests/integration/audio_generation.ts
@@ -1,10 +1,11 @@
-import { assert } from 'chai';
+import { assert, expect } from 'chai';
 import * as request from 'supertest';

 import {
   getTestApi,
   getTestUserToken,
   registerNewTestUser,
+  TestUserInfo,
 } from '../test_utils';

 describe('integration/audio generation', async function () {
@@ -18,8 +19,8 @@ describe('integration/audio generation', async function () {
     token = await getTestUserToken(testUserInfo);
   });

-  describe('Audio generation', function () {
-    it('Can get voices', async function () {
+  describe('audio generation', function () {
+    it('can get voices', async function () {
       await request(api)
         .get(`/v1/voices`)
         .set('Authorization', `Bearer ${token}`)
@@ -30,7 +31,7 @@ describe('integration/audio generation', async function () {
         });
     });

-    it('Can create text to speech', async function () {
+    it('can create text to speech', async function () {
       await request(api)
         .post(`/v1/text_to_speech/${voiceId}`)
         .send({
@@ -42,5 +43,228 @@ describe('integration/audio generation', async function () {
           assert.isString(res.body.url);
         });
     });
+    it('can add to assets', async function () {
+      let mediaUrl;
+      const text = 'Hello world I am a test';
+      await request(api)
+        .post(`/v1/text_to_speech/${voiceId}`)
+        .send({
+          text,
+        })
+        .set('Authorization', `Bearer ${token}`)
+        .expect(200)
+        .then(res => {
+          mediaUrl = res.body.url;
+        });
+
+      await request(api)
+        .post(`/v1/generated_audio/add_to_assets`)
+        .send({
+          url: mediaUrl,
+          name: `Test-audio`,
+          transcript: `${text}`,
+        })
+        .set('Authorization', `Bearer ${token}`)
+        .expect(200);
+    });
+
+    it('can search for results', async function () {
+      await request(api)
+        .post(`/v1/generated_audio/search?query='test'`)
+        .set('Authorization', `Bearer ${token}`)
+        .expect(200)
+        .then(res => {
+          const { results } = res.body;
+          assert.isAbove(results.length, 0);
+        });
+    });
+
+    it('can search for expected 0 result', async function () {
+      await request(api)
+        .post(`/v1/generated_audio/search?query='notfound'`)
+        .set('Authorization', `Bearer ${token}`)
+        .expect(200)
+        .then(res => {
+          const { results } = res.body;
+          assert.equal(results, 0);
+        });
+    });
+
+    describe('can only search authorized team assets', function () {
+      let teamUser: TestUserInfo;
+      let teamUserToken: string;
+      let nonTeamUser: TestUserInfo;
+      let nonTeamUserToken: string;
+      let teamId: string;
+      let mediaUrl: string;
+
+      const teamPromptPublic = 'public team prompt';
+      const teamPromptPrivate = 'private team prompt';
+
+      let publicTeamAssetId: string;
+      let privateTeamAssetId: string;
+
+      before(async function () {
+        teamUser = await registerNewTestUser();
+        teamUserToken = await getTestUserToken(teamUser);
+
+        nonTeamUser = await registerNewTestUser();
+        nonTeamUserToken = await getTestUserToken(nonTeamUser);
+      });
+
+      it('can create team', async function () {
+        await request(api)
+          .post('/v1/teams')
+          .set('Authorization', `Bearer ${teamUserToken}`)
+          .send({ name: 'CoffeeCo' })
+          .expect(200)
+          .then(response => {
+            teamId = response.body.team.id;
+          });
+      });
+
+      it('can add public assets to the team', async function () {
+        await request(api)
+          .post(`/v1/text_to_speech/${voiceId}`)
+          .send({
+            text: teamPromptPublic,
+          })
+          .set('Authorization', `Bearer ${teamUserToken}`)
+          .expect(200)
+          .then(res => {
+            mediaUrl = res.body.url;
+          });
+        await request(api)
+          .post(`/v1/generated_audio/add_to_assets`)
+          .send({
+            url: mediaUrl,
+            name: `${teamPromptPublic.split(' ').join('-')}`,
+            transcript: `${teamPromptPublic}`,
+            asTeamId: teamId,
+            privateInTeam: false,
+          })
+          .set('Authorization', `Bearer ${teamUserToken}`)
+          .expect(200)
+          .then(res => {
+            publicTeamAssetId = res.body.asset.id;
+          });
+      });
+
+      it('can add private assets to the team', async function () {
+        await request(api)
+          .post(`/v1/generated_audio/add_to_assets`)
+          .send({
+            url: mediaUrl,
+            name: `${teamPromptPrivate.split(' ').join('-')}`,
+            transcript: `${teamPromptPrivate}`,
+            asTeamId: teamId,
+            privateInTeam: true,
+          })
+          .set('Authorization', `Bearer ${teamUserToken}`)
+          .expect(200)
+          .then(res => {
+            privateTeamAssetId = res.body.asset.id;
+          });
+      });
+
+      it('team owner can search for private and public team assets', async function () {
+        await request(api)
+          .post(`/v1/generated_audio/search?query='prompt'&asTeamId=${teamId}`)
+          .set('Authorization', `Bearer ${teamUserToken}`)
+          .expect(200)
+          .then(res => {
+            const { results } = res.body;
+            assert.includeMembers(
+              results.map(({ asset }) => asset.id),
+              [privateTeamAssetId, publicTeamAssetId]
+            );
+          });
+      });
+
+      it('non team member does not see results from team', async function () {
+        await request(api)
+          .post(`/v1/generated_audio/search?query='prompt'&asTeamId=${teamId}`)
+          .set('Authorization', `Bearer ${nonTeamUserToken}`)
+          .expect(401);
+      });
+
+      describe('team member only sees public in team results', async function () {
+        it('add non team user to team', async function () {
+          await request(api)
+            .post(`/v1/teams/${teamId}/members`)
+            .set('Authorization', `Bearer ${teamUserToken}`)
+            .send({
+              email: nonTeamUser.email,
+              role: 'editor',
+            })
+            .expect(200)
+            .then(response => {
+              assert.deepEqual(response.body, { success: true });
+            });
+        });
+
+        it('non team member  user accepts invite to team', async function () {
+          await request(api)
+            .put(`/v1/teams/${teamId}/members/accept`)
+            .set('Authorization', `Bearer ${nonTeamUserToken}`)
+            .expect(200);
+        });
+
+        it('new team member user can only see public in team results', async function () {
+          await request(api)
+            .post(
+              `/v1/generated_audio/search?query='prompt'&asTeamId=${teamId}`
+            )
+            .set('Authorization', `Bearer ${nonTeamUserToken}`)
+            .expect(200)
+            .then(res => {
+              const { results } = res.body;
+              assert.includeMembers(
+                results.map(({ asset }) => asset.id),
+                [publicTeamAssetId]
+              );
+              expect(
+                results.map(({ asset }) => asset.id)
+              ).to.not.include.members([privateTeamAssetId]);
+            });
+        });
+      });
+
+      describe('team owner cannot see team members private assets', async function () {
+        let memberPrivateAssetId;
+
+        it('new team member adds private asset', async function () {
+          await request(api)
+            .post(`/v1/generated_audio/add_to_assets`)
+            .send({
+              url: mediaUrl,
+              name: `${teamPromptPrivate.split(' ').join('-')}`,
+              transcript: `${teamPromptPrivate}`,
+              asTeamId: teamId,
+              privateInTeam: true,
+            })
+            .set('Authorization', `Bearer ${nonTeamUserToken}`)
+            .expect(200)
+            .then(res => {
+              memberPrivateAssetId = res.body.asset.id;
+            });
+        });
+
+        it('team owner cannot see member private asset', async function () {
+          await request(api)
+            .post(
+              `/v1/generated_audio/search?query='prompt'&asTeamId=${teamId}`
+            )
+            .set('Authorization', `Bearer ${teamUserToken}`)
+            .expect(200)
+            .then(res => {
+              const { results } = res.body;
+              expect(
+                results.map(({ asset }) => asset.id)
+              ).to.not.include.members([memberPrivateAssetId]);
+            });
+        });
+      });
+    });
   });
 });