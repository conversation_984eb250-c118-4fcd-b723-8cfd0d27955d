PR #3943: [preset] assign unique preset name
================================================================================

diff --git a/controllers/custom_presets.test.ts b/controllers/custom_presets.test.ts
--- a/controllers/custom_presets.test.ts
+++ b/controllers/custom_presets.test.ts
@@ -68,7 +68,7 @@ describe('custom presets', () => {
       });
   });

-  it('should not allow creating a custom preset with the same name', async () => {
+  it('should auto-increment name when creating preset with duplicate name', async () => {
     const PRESET_NAME = `test preset ${Date.now()}`;

     await request(api)
@@ -89,11 +89,9 @@ describe('custom presets', () => {
         settings: { someSetting: 'someValue' },
         taskType: 'gen2',
       })
-      .expect(400)
+      .expect(200)
       .then(response => {
-        expect(response.body.error).toBe(
-          'A preset with this name already exists. Please choose a different name.'
-        );
+        expect(response.body.name).toBe(`${PRESET_NAME} 1`);
       });
   });


diff --git a/controllers/custom_presets.ts b/controllers/custom_presets.ts
--- a/controllers/custom_presets.ts
+++ b/controllers/custom_presets.ts
@@ -166,19 +166,13 @@ export default () => {
         role: MembershipRole.Editor,
       });

-      if (
-        await CustomPreset.findOneBy({
-          userId: owner.id,
-          name,
-          deleted: false,
-        })
-      ) {
-        // TODO -- it's possible this should be updated to allow the same name for private presets,
-        // but for now it's required to be unique per-workspace no matter what
-        throw new BadRequestError(
-          'A preset with this name already exists. Please choose a different name.'
-        );
-      }
+      // Get next available name if there's a duplicate
+      const presetName = await getNextAvailablePresetName({
+        baseName: name,
+        team: owner,
+        user: req.user,
+        privateInTeam: privateInTeam ?? true,
+      });

       const textPrompt = settings.textPrompt as string | undefined;

@@ -194,7 +188,7 @@ export default () => {
       const preset = await CustomPreset.Create({
         userId: owner.id,
         createdBy: req.user.id,
-        name,
+        name: presetName,
         settings,
         taskType: req.body.taskType as TaskType,
         version: 1,
@@ -245,22 +239,6 @@ export default () => {
         },
       });

-      if (name && name !== customPreset.name) {
-        // Don't allow duplicate names
-        if (
-          await CustomPreset.findOneBy({
-            userId: owner.id,
-            name,
-            deleted: false,
-          })
-        ) {
-          throw new BadRequestError(
-            'A preset with this name already exists. Please choose a different name.'
-          );
-        }
-        customPreset.name = name;
-      }
-
       if (settings) {
         const newSettings = settings as Record<string, unknown>;
         customPreset.settings = Task.sanitizeOptions(
@@ -415,3 +393,32 @@ async function moderateCustomPresetTextPrompt(params: {
     throw new BadRequestError('Your inputs have been flagged.');
   }
 }
+
+async function getNextAvailablePresetName({
+  baseName,
+  team,
+  user,
+  privateInTeam,
+}: {
+  baseName: string;
+  team: User;
+  user: User;
+  privateInTeam: boolean;
+}): Promise<string> {
+  let currentName = baseName;
+  let counter = 1;
+
+  while (
+    await CustomPreset.existsBy({
+      userId: team.id,
+      name: currentName,
+      deleted: false,
+      ...(privateInTeam ? { createdBy: user.id } : {}),
+    })
+  ) {
+    currentName = `${baseName} ${counter}`;
+    counter++;
+  }
+
+  return currentName;
+}