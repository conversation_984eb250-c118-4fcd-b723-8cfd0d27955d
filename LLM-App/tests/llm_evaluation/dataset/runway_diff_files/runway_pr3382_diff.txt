PR #3382: RW-14354 Add suspension logging to db and show logs in admin
================================================================================

diff --git a/admin/paperwhite/client/pages/user/userDetails/SectionDetails.tsx b/admin/paperwhite/client/pages/user/userDetails/SectionDetails.tsx
--- a/admin/paperwhite/client/pages/user/userDetails/SectionDetails.tsx
+++ b/admin/paperwhite/client/pages/user/userDetails/SectionDetails.tsx
@@ -23,6 +23,7 @@ import { Link, useParams } from 'react-router-dom';
 import { trpc } from '../../../trpc';
 import { TeamMembershipsList } from './TeamMembershipsList';
 import { UserMembershipsList } from './UserMembershipsList';
+import { UserSuspensionsList } from './UserSuspensionsList';
 import moment = require('moment');
 import { DeleteOutlined, LoadingOutlined } from '@ant-design/icons';

@@ -37,6 +38,7 @@ export const SectionDetails = () => {
   const [messageApi, messageContext] = message.useMessage();

   const user = trpc.user.useQuery(Number(id));
+  const suspensions = trpc.userSuspensionRecords.useQuery(Number(id));
   const unsuspendUser = trpc.unsuspendUser.useMutation();
   const suspendUser = trpc.suspendUser.useMutation();
   const deleteUser = trpc.deleteUser.useMutation();
@@ -146,9 +148,17 @@ export const SectionDetails = () => {
           action={
             <Popconfirm
               onConfirm={async () => {
-                await unsuspendUser.mutateAsync(Number(id));
+                const reason = prompt('What is the reason for unsuspension?');
+                if (!reason) {
+                  return;
+                }
+                await unsuspendUser.mutateAsync({
+                  id: Number(id),
+                  reason,
+                });
                 await messageApi.success('User unsuspended');
                 void user.refetch();
+                void suspensions.refetch();
               }}
               title="Are you sure?"
               description="This will unsuspend the user and they will be able to sin again. Are you sure?"
@@ -176,13 +186,14 @@ export const SectionDetails = () => {
           {user.data && !user.data?.suspension && (
             <Popconfirm
               onConfirm={async () => {
-                const reason = prompt('What is the reason?');
+                const reason = prompt('What is the reason for suspension?');
                 if (!reason) {
                   return;
                 }
                 await suspendUser.mutateAsync({ id: Number(id), reason });
                 await messageApi.success('User suspended');
                 void user.refetch();
+                void suspensions.refetch();
               }}
               title="Are you sure?"
               description="This will suspend the user and they will not be able to use the app. Are you sure?"
@@ -438,6 +449,11 @@ export const SectionDetails = () => {
           },
           {
             key: '4',
+            label: 'Suspensions',
+            children: <UserSuspensionsList userId={Number(id)} />,
+          },
+          {
+            key: '5',
             label: 'Workspace config',
             children: user.isLoading ? (
               <div style={{ width: '100%', height: '400px' }}>

diff --git a/admin/paperwhite/client/pages/user/userDetails/UserSuspensionsList.tsx b/admin/paperwhite/client/pages/user/userDetails/UserSuspensionsList.tsx
--- a/admin/paperwhite/client/pages/user/userDetails/UserSuspensionsList.tsx
+++ b/admin/paperwhite/client/pages/user/userDetails/UserSuspensionsList.tsx
@@ -0,0 +1,66 @@
+import { Space, Table, Tag } from 'antd';
+import moment = require('moment');
+import * as React from 'react';
+
+import { trpc } from '../../../trpc';
+
+export const UserSuspensionsList = ({ userId }: { userId: number }) => {
+  const suspensions = trpc.userSuspensionRecords.useQuery(userId);
+
+  const sortedData = React.useMemo(() => {
+    if (!suspensions.data) return [];
+    return [...suspensions.data].sort(
+      (a, b) => moment(b.createdAt).valueOf() - moment(a.createdAt).valueOf()
+    );
+  }, [suspensions.data]);
+
+  return (
+    <Space direction="vertical" style={{ width: '100%' }}>
+      <Table
+        style={{ display: 'flex' }}
+        loading={suspensions.isLoading}
+        rowKey="id"
+        columns={[
+          {
+            title: 'Type',
+            dataIndex: 'type',
+            width: 100,
+            render: value => (
+              <Tag color={value === 'unsuspend' ? 'green' : 'red'}>{value}</Tag>
+            ),
+          },
+          {
+            title: 'Reason',
+            dataIndex: 'reason',
+            ellipsis: {
+              showTitle: false,
+            },
+            render: value => (
+              <div
+                style={{
+                  whiteSpace: 'pre-wrap',
+                  wordWrap: 'break-word',
+                }}
+              >
+                {value ?? '-'}
+              </div>
+            ),
+          },
+          {
+            title: 'Created By',
+            dataIndex: 'createdBy',
+            width: 120,
+            render: value => value || 'System',
+          },
+          {
+            title: 'Created At',
+            dataIndex: 'createdAt',
+            width: 180,
+            render: value => moment(value).format('lll'),
+          },
+        ]}
+        dataSource={sortedData}
+      />
+    </Space>
+  );
+};

diff --git a/admin/paperwhite/server/api/index.ts b/admin/paperwhite/server/api/index.ts
--- a/admin/paperwhite/server/api/index.ts
+++ b/admin/paperwhite/server/api/index.ts
@@ -21,6 +21,7 @@ import tasks from './tasks';
 import userMemberships from './userMemberships';
 import users from './users';
 import userSearch from './userSearch';
+import userSuspensionRecords from './userSuspensionRecords';
 import videoCompositions from './videoCompositions';

 export const AppRouter = router({
@@ -64,6 +65,7 @@ export const AppRouter = router({
   ...userMemberships,
   ...users,
   ...userSearch,
+  ...userSuspensionRecords,
   ...videoCompositions,
 });


diff --git a/admin/paperwhite/server/api/userSuspensionRecords.ts b/admin/paperwhite/server/api/userSuspensionRecords.ts
--- a/admin/paperwhite/server/api/userSuspensionRecords.ts
+++ b/admin/paperwhite/server/api/userSuspensionRecords.ts
@@ -0,0 +1,23 @@
+import { z } from 'zod';
+
+import { UserSuspensionRecord } from '../../../../entity/users/UserSuspensionRecord';
+import { publicProcedure } from '../../trpc';
+import {
+  formatUserSuspensionRecord,
+  zodUserSuspensionRecord,
+} from '../objects/UserSuspensionRecordObject';
+
+export default {
+  userSuspensionRecords: publicProcedure
+    .input(z.number())
+    .output(z.array(zodUserSuspensionRecord))
+    .query(async ({ input }) => {
+      return formatUserSuspensionRecord(
+        await UserSuspensionRecord.find({
+          where: {
+            userId: input,
+          },
+        })
+      );
+    }),
+} as const;

diff --git a/admin/paperwhite/server/api/users.ts b/admin/paperwhite/server/api/users.ts
--- a/admin/paperwhite/server/api/users.ts
+++ b/admin/paperwhite/server/api/users.ts
@@ -9,6 +9,10 @@ import {
   UserMembership,
 } from '../../../../entity/users/UserMembership';
 import { UserSocialProfile } from '../../../../entity/users/UserSocialProfile';
+import {
+  SuspensionType,
+  UserSuspensionRecord,
+} from '../../../../entity/users/UserSuspensionRecord';
 import { allAllowances } from '../../../../lib/allowances';
 import { allCapabilities } from '../../../../lib/capabilities';
 import { sendEmail } from '../../../../lib/email';
@@ -53,18 +57,35 @@ export default {
         await user.suspend({
           suspensionReason: `${req.input.reason}\nvia ${req.ctx.user()}`,
         });
+
+        // Log this in the user_suspension_records db table
+        await UserSuspensionRecord.Create({
+          userId: user.id,
+          createdBy: req.ctx.userId(),
+          type: SuspensionType.suspend,
+          reason: req.input.reason,
+        });
       }
       return await formatUser(user);
     }),
   unsuspendUser: publicProcedure
-    .input(z.number())
+    .input(z.object({ id: z.number(), reason: z.string() }))
     .output(zodUser)
     .mutation(async req => {
-      const user = await User.getOneBy({ id: req.input });
+      const user = await User.getOneBy({ id: req.input.id });
       if (!user.suspended) {
         return await formatUser(user);
       }
       user.suspended = false;
+
+      // Log this in the user_suspension_records db table
+      await UserSuspensionRecord.Create({
+        userId: user.id,
+        createdBy: req.ctx.userId(),
+        type: SuspensionType.unsuspend,
+        reason: req.input.reason,
+      });
+
       await user.save();
       return await formatUser(user);
     }),

diff --git a/admin/paperwhite/server/objects/UserSuspensionRecordObject.ts b/admin/paperwhite/server/objects/UserSuspensionRecordObject.ts
--- a/admin/paperwhite/server/objects/UserSuspensionRecordObject.ts
+++ b/admin/paperwhite/server/objects/UserSuspensionRecordObject.ts
@@ -0,0 +1,25 @@
+import { z } from 'zod';
+
+import { UserSuspensionRecord } from '../../../../entity/users/UserSuspensionRecord';
+
+export const zodUserSuspensionRecord = z.object({
+  id: z.number(),
+  userId: z.number(),
+  createdAt: z.string(),
+  createdBy: z.nullable(z.number()),
+  type: z.string(),
+  reason: z.nullable(z.string()),
+});
+
+export async function formatUserSuspensionRecord(
+  suspensions: UserSuspensionRecord[]
+): Promise<z.infer<typeof zodUserSuspensionRecord>[]> {
+  return suspensions.map(suspension => ({
+    ...suspension,
+    createdAt: suspension.createdAt.toISOString(),
+  }));
+}
+
+export type UserSuspensionRecordObject = z.infer<
+  typeof zodUserSuspensionRecord
+>;

diff --git a/admin/paperwhite/trpc.ts b/admin/paperwhite/trpc.ts
--- a/admin/paperwhite/trpc.ts
+++ b/admin/paperwhite/trpc.ts
@@ -14,6 +14,7 @@ export const createContext = async ({
     req,
     res,
     user: () => req[UserSymbol].username as string,
+    userId: () => req[UserSymbol].id as number,
     isPrivileged: () =>
       isStageOrDev() ||
       process.env.LOCAL_DEV === 'true' ||

diff --git a/entity/users/UserSuspensionRecord.ts b/entity/users/UserSuspensionRecord.ts
--- a/entity/users/UserSuspensionRecord.ts
+++ b/entity/users/UserSuspensionRecord.ts
@@ -0,0 +1,74 @@
+import {
+  Column,
+  CreateDateColumn,
+  Entity,
+  PrimaryGeneratedColumn,
+} from 'typeorm';
+
+import {
+  ExportFieldToRedshift,
+  ExportTableToRedshift,
+  RunwayBaseEntity,
+} from '../../lib/RunwayBaseEntity';
+
+export interface UserSuspensionRecordConfig {
+  userId: number;
+  createdBy: number | null;
+  type: SuspensionType;
+  reason: string | null;
+}
+
+export enum SuspensionType {
+  suspend = 'suspend',
+  unsuspend = 'unsuspend',
+}
+
+@ExportTableToRedshift({ rowsAreImmutable: true })
+@Entity({ name: 'user_suspension_records' })
+export class UserSuspensionRecord extends RunwayBaseEntity {
+  @ExportFieldToRedshift()
+  @PrimaryGeneratedColumn()
+  id!: number;
+
+  @ExportFieldToRedshift()
+  @Column({ type: 'int4' })
+  userId!: number;
+
+  @ExportFieldToRedshift()
+  @CreateDateColumn()
+  createdAt!: Date;
+
+  @ExportFieldToRedshift()
+  @Column({
+    comment: 'Who suspended the user',
+    type: 'int4',
+    nullable: true,
+  })
+  createdBy!: number | null;
+
+  @ExportFieldToRedshift()
+  @Column({
+    type: 'varchar',
+    comment:
+      'Is this a suspension or an unsuspension? Either "suspend" or "unsuspend".',
+  })
+  type!: SuspensionType;
+
+  @ExportFieldToRedshift()
+  @Column({
+    comment: 'Why the user was suspended or unsuspended',
+    type: 'varchar',
+    nullable: true,
+  })
+  reason!: string | null;
+
+  static async Create(config: UserSuspensionRecordConfig) {
+    const suspension = new UserSuspensionRecord();
+    suspension.userId = config.userId;
+    suspension.createdBy = config.createdBy;
+    suspension.type = config.type;
+    suspension.reason = config.reason;
+    await suspension.save();
+    return suspension;
+  }
+}

diff --git a/lib/dbConnection.ts b/lib/dbConnection.ts
--- a/lib/dbConnection.ts
+++ b/lib/dbConnection.ts
@@ -81,6 +81,7 @@ import { UserCharge } from '../entity/users/UserCharge';
 import { UserImpersonationSession } from '../entity/users/UserImpersonationSession';
 import { UserMembership } from '../entity/users/UserMembership';
 import { UserSocialProfile } from '../entity/users/UserSocialProfile';
+import { UserSuspensionRecord } from '../entity/users/UserSuspensionRecord';
 import { VideoComposition } from '../entity/video/VideoComposition';
 import { VideoCompositionVersion } from '../entity/video/VideoCompositionVersion';
 import { VideoTranscription } from '../entity/video/VideoTranscription';
@@ -178,6 +179,7 @@ export function getEntities(): Array<
     Asset,
     PublicAPIProjectAuditLog,
     TaskStatusTransition,
+    UserSuspensionRecord,
   ];
 }


diff --git a/migrations/1730305255903-AddUserSuspensionRecord.ts b/migrations/1730305255903-AddUserSuspensionRecord.ts
--- a/migrations/1730305255903-AddUserSuspensionRecord.ts
+++ b/migrations/1730305255903-AddUserSuspensionRecord.ts
@@ -0,0 +1,27 @@
+import { MigrationInterface, QueryRunner } from 'typeorm';
+
+export class AddUserSuspensionRecord1730305255903
+  implements MigrationInterface
+{
+  public async up(queryRunner: QueryRunner): Promise<void> {
+    await queryRunner.query(`
+            CREATE TABLE "user_suspension_records" (
+                "id" SERIAL PRIMARY KEY,
+                "userId" integer NOT NULL,
+                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
+                "createdBy" integer NULL,
+                "type" varchar NOT NULL,
+                "reason" varchar DEFAULT NULL,
+                
+                CONSTRAINT "fk_user_suspension_records_users" 
+                FOREIGN KEY ("userId") 
+                REFERENCES "users"("id")
+            );
+            CREATE INDEX "idx_user_suspension_records_userId" ON "user_suspension_records"("userId");
+        `);
+  }
+
+  public async down(queryRunner: QueryRunner): Promise<void> {
+    await queryRunner.query(`DROP TABLE "user_suspension_records"`);
+  }
+}