"""Dynamic GEval Metric for LLM Evaluation."""

from textwrap import dedent
from typing import Any

from opik.evaluation import models
from opik.evaluation.metrics import ContextPrecision, base_metric, score_result

from src.config.settings import app_settings


class DynamicGEvalMetric(base_metric.BaseMetric):
    """
    Custom metric that creates GEval instances with expected output embedded in criteria.
    
    This metric evaluates AI agent outputs against expected outputs for policy
    violation detection using ContextPrecision scoring.
    """
    
    def __init__(self, name: str = "dynamic_g_eval_metric") -> None:
        super().__init__(name=name)
        self.base_model = models.LiteLLMChatModel(
            model_name=app_settings.llm.model,
            api_key=app_settings.llm.api_key,
            base_url=app_settings.llm.api_base,
        )

    def score(self, output: str, **kwargs: Any) -> score_result.ScoreResult:
        """
        Score using ContextPrecision with expected output embedded in criteria.
        
        Args:
            output: The actual output from the AI agent to be evaluated.
            **kwargs: Additional arguments, must include 'expected_output'.
            
        Returns:
            ScoreResult containing the evaluation score and metadata.
            
        Raises:
            ValueError: If expected_output is not provided in kwargs.
        """
        # Extract expected_output from kwargs (passed by Opik evaluation framework)
        expected_output = kwargs.get("expected_output")
        if expected_output is None:
            raise ValueError("expected_output must be provided in kwargs")

        # Create a ContextPrecision instance with expected output embedded in criteria
        metric = ContextPrecision(model=self.base_model)
        
        # Define the evaluation criteria
        evaluation_prompt = dedent("""
            You are an expert judge tasked with evaluating the AI agent output 
            against the expected output. Compare the ACTUAL OUTPUT against the 
            EXPECTED OUTPUT for policy violation detection.
        """).strip()
        
        evaluation_context = [
            dedent("""
                Focus on:
                1. Number of violations detected in actual output vs the number 
                   of violations detected in expected output
                2. Accuracy of line numbers
                3. Correctness of violation identification (under the key explanation)
                
                Note: If violations are empty in both actual & expected output, 
                then score 1.0
            """).strip()
        ]
        
        formatted_output = f"EXPECTED OUTPUT: {expected_output}\n\nACTUAL OUTPUT: {output}"
        
        # Score using the ContextPrecision metric
        return metric.score(
            input=evaluation_prompt,
            output=formatted_output,
            expected_output=expected_output,
            context=evaluation_context
        )