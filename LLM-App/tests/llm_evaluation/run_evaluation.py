import asyncio
import json
from dataclasses import dataclass, asdict
from textwrap import dedent
from typing import List
import litellm
from opik import evaluate
from opik.evaluation import models
from opik.evaluation.metrics import GEval, Equals
from opik import track
import aiohttp

from src.config.settings import app_settings
from src.core.AIAgent import AIAgent
from src.core.models.Violation import ViolationList
from src.services.LLMOpsService import ops_service
from tests.llm_evaluation.gEval_metric import DynamicGEvalMetric

# os.environ["OPIK_API_KEY"] = app_settings.llm_ops.api_key
# os.environ["OPIK_WORKSPACE"] = app_settings.llm_ops.workspace
# os.environ["ANTHROPIC_API_KEY"] = app_settings.llm.api_key

# Configure LiteLLM to drop unsupported parameters for Anthropic
litellm.drop_params = True

# os.environ["OPIK_API_KEY"] = app_settings.llm_ops.api_key
# os.environ["OPIK_WORKSPACE"] = app_settings.llm_ops.workspace
# os.environ["ANTHROPIC_API_KEY"] = app_settings.llm.api_key

# Configure LiteLLM to drop unsupported parameters for Anthropic
litellm.drop_params = True


DATASET_NAME = "Runway Combined Dataset"
base_model = models.LiteLLMChatModel(
    model_name=app_settings.llm.model,
    api_key=app_settings.llm.api_key,
    base_url=app_settings.llm.api_base,
)


@dataclass
class EvaluationDataset:
    input_policy: str
    input_diff: str
    expected_output: str


def create_dataset_entries(dataset: List[EvaluationDataset],name=DATASET_NAME):
    cloud_dataset = ops_service.get_or_create_dataset(name=name)

    dataset_entries = []
    for dataset_item in dataset:
        dataset_entries.append({
            "input_policy": dataset_item.input_policy,
            "input_diff": dataset_item.input_diff,
            "expected_output": dataset_item.expected_output
        })

    cloud_dataset.insert(dataset_entries)
    return cloud_dataset
    # Note: Opik has deduplication built in



async def run_agent_evaluation(x: dict) -> dict:
    agent = AIAgent()
    list_of_violations = await agent.run(x['input_diff'], [x["input_policy"]])
    
    violationList_object = ViolationList(violations=list_of_violations)
    json_str = json.dumps(violationList_object.model_dump_json())
    print('Agent response:', json_str)
    return {
        "output": json_str,
        "expected_output": x["expected_output"],
    }


def evaluation_task(x: dict) -> dict:
    return asyncio.run(run_agent_evaluation(x))


def eval_agent() -> dict:
    print("Starting evaluation...")
    
    # Create the custom metric
    metric = DynamicGEvalMetric(name="dynamic_g_eval_with_expected_output")

    # Note: The evaluate function takes all dataset's inputs 
    evaluation = evaluate(
        dataset=ops_service.get_or_create_dataset(name=DATASET_NAME),
        task=evaluation_task,
        scoring_metrics=[metric],
        project_name="llm-app-evals",
        experiment_config={
            "model": app_settings.llm.model,
        },
        #nb_samples=1 # total number of sample to run against
    )
    return evaluation


if __name__ == "__main__":
    try:
        e = eval_agent()
        print("Done!")
    finally:
        # Force close any pending tasks/sessions
        pending = asyncio.all_tasks(asyncio.get_event_loop())
        for task in pending:
            task.cancel()