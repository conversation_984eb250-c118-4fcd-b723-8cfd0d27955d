#!/usr/bin/env python3
"""
Standalone script to upload diff files to S3 without using the settings system.
"""
import boto3
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def upload_all_diff_files():
    """Upload all runway_pr*_diff.txt files to S3"""
    
    # Get AWS credentials from environment
    access_key = os.getenv('AWS_ACCESS_KEY_ID')
    secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
    region = os.getenv('AWS_REGION', 'us-east-1')
    bucket_name = os.getenv('AWS_S3_BUCKET_NAME')
    
    if not all([access_key, secret_key, bucket_name]):
        print("ERROR: Missing AWS credentials or bucket name in .env file")
        return False
    
    # Create S3 client
    s3_client = boto3.client(
        's3',
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_key,
        region_name=region
    )
    
    # Find diff files
    project_root = Path(__file__).parent.parent
    diff_folder = project_root / "tests/llm_evaluation/dataset/runway_diff_files"
    
    if not diff_folder.exists():
        print(f"ERROR: Diff folder not found: {diff_folder}")
        return False
    
    print(f"Looking for diff files in: {diff_folder}")
    print(f"Target bucket: {bucket_name}")
    print()
    
    uploaded_count = 0
    total_count = 0
    diff_files_prefix = "runway_diffs/"
    
    for file_path in diff_folder.glob("runway_pr*_diff.txt"):
        total_count += 1
        filename = file_path.name
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # Upload to S3
            key = f"{diff_files_prefix}{filename}"
            s3_client.put_object(
                Bucket=bucket_name,
                Key=key,
                Body=content.encode('utf-8')
            )
            
            print(f"SUCCESS: Uploaded {filename} -> s3://{bucket_name}/{key}")
            uploaded_count += 1
            
        except Exception as e:
            print(f"ERROR: Failed to upload {filename}: {e}")
    
    print(f"\nSummary: {uploaded_count}/{total_count} files uploaded successfully")
    
    if uploaded_count > 0:
        print("\nFiles uploaded to S3:")
        print(f"  Bucket: {bucket_name}")
        print(f"  Prefix: {diff_files_prefix}")
        print("  Files ready for evaluation system to use")
    
    return uploaded_count > 0

if __name__ == "__main__":
    print("Starting diff files upload to S3...")
    success = upload_all_diff_files()
    exit(0 if success else 1)
