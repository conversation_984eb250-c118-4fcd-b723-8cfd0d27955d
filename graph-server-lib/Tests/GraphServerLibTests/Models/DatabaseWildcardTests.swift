//
//  DatabaseWildcardTests.swift
//  GraphServerLibTests
//
//  Created by Amp on 7/1/25.
//

@testable import GraphServerLib
import Foundation
import Testing

@Suite("Database Wildcard Pattern Tests")
struct DatabaseWildcardTests {
    let database: Database
    
    init() {
        self.database = Database.inMemory()
    }
    
    @Test func testSymbolOccurrencePatternMatching() async throws {
        let compileUnit = CompileUnit(rootDir: URL(fileURLWithPath: "/test/project"))
        await self.database.add(compileUnit: compileUnit)
        
        // Add test symbols with different typeorm versions
        let typeormSymbols = [
            SymbolInformation(
                id: "scip-typescript npm typeorm 0.3.17 repository/`BaseEntity.d.ts`/BaseEntity#findBy().",
                kind: .method,
                displayName: "findBy",
                occurrences: [
                    SymbolOccurrence(
                        compileUnit: compileUnit,
                        document: "src/entities/User.ts",
                        range: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        enclosingRange: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        roles: [.readAccess])
                ],
                typeString: nil
            ),
            SymbolInformation(
                id: "scip-typescript npm typeorm 0.3.20 repository/`BaseEntity.d.ts`/BaseEntity#findBy().",
                kind: .method,
                displayName: "findBy",
                occurrences: [
                    SymbolOccurrence(
                        compileUnit: compileUnit,
                        document: "src/entities/Product.ts",
                        range: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        enclosingRange: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        roles: [.readAccess])
                ],
                typeString: nil
            ),
            SymbolInformation(
                id: "scip-typescript npm typeorm 1.0.0 repository/`BaseEntity.d.ts`/BaseEntity#findBy().",
                kind: .method,
                displayName: "findBy",
                occurrences: [
                    SymbolOccurrence(
                        compileUnit: compileUnit,
                        document: "src/entities/Order.ts",
                        range: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        enclosingRange: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        roles: [.readAccess])
                ],
                typeString: nil
            ),
            // Add a symbol that should NOT match
            SymbolInformation(
                id: "scip-typescript npm typeorm-extended 1.0.0 repository/`BaseEntity.d.ts`/BaseEntity#findBy().",
                kind: .method,
                displayName: "findBy",
                occurrences: [
                    SymbolOccurrence(
                        compileUnit: compileUnit,
                        document: "src/entities/Extended.ts",
                        range: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        enclosingRange: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        roles: [.readAccess])
                ],
                typeString: nil
            )
        ]
        
        await self.database.add(symbols: typeormSymbols, definedInCompileUnit: compileUnit)
        
        // Test wildcard pattern matching
        let wildcardPattern = "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#findBy()."
        let wildcardOccurrences = await self.database.occurrences(forSymbolIDPattern: wildcardPattern)
        
        // Should match first 3 symbols but not typeorm-extended
        #expect(wildcardOccurrences.count == 3)
        let matchedDocuments = Set(wildcardOccurrences.map { $0.document })
        #expect(matchedDocuments.contains("src/entities/User.ts"))
        #expect(matchedDocuments.contains("src/entities/Product.ts"))
        #expect(matchedDocuments.contains("src/entities/Order.ts"))
        #expect(!matchedDocuments.contains("src/entities/Extended.ts"))
    }
    
    @Test func testWildcardSpecificity() async throws {
        let compileUnit = CompileUnit(rootDir: URL(fileURLWithPath: "/test/project"))
        await self.database.add(compileUnit: compileUnit)
        
        // Add symbols to test boundary conditions
        let testSymbols = [
            SymbolInformation(
                id: "scip-typescript npm typeorm 0.3.17 repository/`BaseEntity.d.ts`/BaseEntity#findBy().",
                kind: .method,
                displayName: "findBy",
                occurrences: [
                    SymbolOccurrence(
                        compileUnit: compileUnit,
                        document: "src/typeorm.ts",
                        range: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        enclosingRange: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        roles: [.readAccess])
                ],
                typeString: nil
            ),
            SymbolInformation(
                id: "scip-typescript npm typeorm-extended 1.0.0 repository/`BaseEntity.d.ts`/BaseEntity#findBy().",
                kind: .method,
                displayName: "findBy",
                occurrences: [
                    SymbolOccurrence(
                        compileUnit: compileUnit,
                        document: "src/extended.ts",
                        range: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        enclosingRange: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        roles: [.readAccess])
                ],
                typeString: nil
            ),
            SymbolInformation(
                id: "scip-typescript npm my-typeorm 1.0.0 repository/`BaseEntity.d.ts`/BaseEntity#findBy().",
                kind: .method,
                displayName: "findBy",
                occurrences: [
                    SymbolOccurrence(
                        compileUnit: compileUnit,
                        document: "src/my-typeorm.ts",
                        range: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        enclosingRange: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        roles: [.readAccess])
                ],
                typeString: nil
            )
        ]
        
        await self.database.add(symbols: testSymbols, definedInCompileUnit: compileUnit)
        
        // Test that "typeorm %" doesn't match "typeorm-extended" or "my-typeorm"
        let wildcardPattern = "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#findBy()."
        let occurrences = await self.database.occurrences(forSymbolIDPattern: wildcardPattern)
        
        #expect(occurrences.count == 1)
        #expect(occurrences.first?.document == "src/typeorm.ts")
    }
    
    @Test func testBackwardCompatibility() async throws {
        let compileUnit = CompileUnit(rootDir: URL(fileURLWithPath: "/test/project"))
        await self.database.add(compileUnit: compileUnit)
        
        let exactSymbol = SymbolInformation(
            id: "scip-typescript npm typeorm 0.3.17 repository/`BaseEntity.d.ts`/BaseEntity#findBy().",
            kind: .method,
            displayName: "findBy",
            occurrences: [
                SymbolOccurrence(
                    compileUnit: compileUnit,
                    document: "src/exact.ts",
                    range: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                    enclosingRange: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                    roles: [.readAccess])
            ],
            typeString: nil
        )
        
        await self.database.add(symbols: [exactSymbol], definedInCompileUnit: compileUnit)
        
        // Test that exact patterns still work with existing method
        let exactPattern = "scip-typescript npm typeorm 0.3.17 repository/`BaseEntity.d.ts`/BaseEntity#findBy()."
        let exactOccurrences = await self.database.occurrences(forSymbolID: exactPattern)
        
        #expect(exactOccurrences.count == 1)
        #expect(exactOccurrences.first?.document == "src/exact.ts")
    }
    
    @Test func testWildcardWithDocumentPrefixFilter() async throws {
        let compileUnit = CompileUnit(rootDir: URL(fileURLWithPath: "/test/project"))
        await self.database.add(compileUnit: compileUnit)
        
        let testSymbols = [
            SymbolInformation(
                id: "scip-typescript npm axios 1.6.8 `index.d.ts`/AxiosStatic#create().",
                kind: .method,
                displayName: "create",
                occurrences: [
                    SymbolOccurrence(
                        compileUnit: compileUnit,
                        document: "src/api/client.ts",
                        range: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        enclosingRange: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        roles: [.readAccess])
                ],
                typeString: nil
            ),
            SymbolInformation(
                id: "scip-typescript npm axios 1.7.7 `index.d.ts`/AxiosStatic#create().",
                kind: .method,
                displayName: "create",
                occurrences: [
                    SymbolOccurrence(
                        compileUnit: compileUnit,
                        document: "test/unit/axios.test.ts",
                        range: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        enclosingRange: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        roles: [.readAccess])
                ],
                typeString: nil
            )
        ]
        
        await self.database.add(symbols: testSymbols, definedInCompileUnit: compileUnit)
        
        // Test wildcard with document prefix filter
        let wildcardPattern = "scip-typescript npm axios % `index.d.ts`/AxiosStatic#create()."
        let filteredOccurrences = await self.database.occurrences(
            forSymbolIDPattern: wildcardPattern,
            filterDocumentPrefix: "src/"
        )
        
        // Should only match the src/ document, not the test/ document
        #expect(filteredOccurrences.count == 1)
        #expect(filteredOccurrences.first?.document == "src/api/client.ts")
    }
    
    @Test func testWildcardWithExcludeRoleFilter() async throws {
        let compileUnit = CompileUnit(rootDir: URL(fileURLWithPath: "/test/project"))
        await self.database.add(compileUnit: compileUnit)
        
        let testSymbol = SymbolInformation(
            id: "scip-typescript npm express 4.19.6 `index.d.ts`/IRouter#get().",
            kind: .method,
            displayName: "get",
            occurrences: [
                SymbolOccurrence(
                    compileUnit: compileUnit,
                    document: "src/routes.ts",
                    range: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                    enclosingRange: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                    roles: [.readAccess]),
                SymbolOccurrence(
                    compileUnit: compileUnit,
                    document: "types/express.d.ts",
                    range: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                    enclosingRange: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                    roles: [.definition])
            ],
            typeString: nil
        )
        
        await self.database.add(symbols: [testSymbol], definedInCompileUnit: compileUnit)
        
        // Test wildcard with exclude role filter
        let wildcardPattern = "scip-typescript npm express % `index.d.ts`/IRouter#get()."
        let filteredOccurrences = await self.database.occurrences(
            forSymbolIDPattern: wildcardPattern,
            filterExcludeRole: .definition
        )
        
        // Should exclude the definition occurrence, only return readAccess
        #expect(filteredOccurrences.count == 1)
        #expect(filteredOccurrences.first?.document == "src/routes.ts")
        #expect(filteredOccurrences.first?.roles.contains(.readAccess) == true)
        #expect(filteredOccurrences.first?.roles.contains(.definition) == false)
    }
    
    @Test func testMultipleWildcardCharacters() async throws {
        let compileUnit = CompileUnit(rootDir: URL(fileURLWithPath: "/test/project"))
        await self.database.add(compileUnit: compileUnit)
        
        let testSymbols = [
            SymbolInformation(
                id: "scip-typescript npm @types/express-serve-static-core 4.17.33 `index.d.ts`/IRouter#get.",
                kind: .method,
                displayName: "get",
                occurrences: [
                    SymbolOccurrence(
                        compileUnit: compileUnit,
                        document: "src/express-old.ts",
                        range: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        enclosingRange: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        roles: [.readAccess])
                ],
                typeString: nil
            ),
            SymbolInformation(
                id: "scip-typescript npm @types/express-serve-static-core 4.19.6 `index.d.ts`/IRouter#get.",
                kind: .method,
                displayName: "get",
                occurrences: [
                    SymbolOccurrence(
                        compileUnit: compileUnit,
                        document: "src/express-new.ts",
                        range: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        enclosingRange: PointUTF16Z.zero() ..< PointUTF16Z.zero(),
                        roles: [.readAccess])
                ],
                typeString: nil
            )
        ]
        
        await self.database.add(symbols: testSymbols, definedInCompileUnit: compileUnit)
        
        // Test pattern with multiple wildcards
        let multiWildcardPattern = "scip-typescript npm @types/express-serve-static-core % `index.d.ts`/IRouter#%."
        let occurrences = await self.database.occurrences(forSymbolIDPattern: multiWildcardPattern)
        
        #expect(occurrences.count == 2)
        let matchedDocuments = Set(occurrences.map { $0.document })
        #expect(matchedDocuments.contains("src/express-old.ts"))
        #expect(matchedDocuments.contains("src/express-new.ts"))
    }
}

extension DatabaseWildcardTests: SCIPFixtureTests {}
