//
//  ScipIDTests.swift
//  GraphServerLibTests
//
//  Created by Amp on 7/1/25.
//

@testable import GraphServerLib
import Foundation
import Testing

@Suite("ScipID Wildcard Tests")
struct ScipIDTests {
    
    @Test func testWildcardDetection() async throws {
        let exactPattern: ScipID = "scip-typescript npm typeorm 0.3.17 repository/`BaseEntity.d.ts`/BaseEntity#findBy()."
        let wildcardPattern: ScipID = "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#findBy()."
        let underscoreWildcardPattern: ScipID = "scip-typescript npm typeorm 0.3._ repository/`BaseEntity.d.ts`/BaseEntity#findBy()."
        let multipleWildcardPattern: ScipID = "scip-typescript npm % % repository/`BaseEntity.d.ts`/BaseEntity#%()."
        
        #expect(!exactPattern.hasWildcards)
        #expect(wildcardPattern.hasWildcards)
        #expect(underscoreWildcardPattern.hasWildcards)
        #expect(multipleWildcardPattern.hasWildcards)
    }
    
    @Test func testVersionWildcardFactory() async throws {
        let pattern = ScipID.withVersionWildcard("scip-typescript npm axios % `index.d.ts`/AxiosStatic#create().")
        
        #expect(pattern.hasWildcards)
        #expect(pattern.rawValue.contains("%"))
        #expect(pattern.rawValue == "scip-typescript npm axios % `index.d.ts`/AxiosStatic#create().")
    }
    
    @Test func testWildcardFactoryWithDifferentPatterns() async throws {
        // Test various wildcard patterns
        let typeormPattern = ScipID.withVersionWildcard("scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#findBy().")
        let axiosPattern = ScipID.withVersionWildcard("scip-typescript npm axios % `index.d.ts`/AxiosStatic#create().")
        let expressPattern = ScipID.withVersionWildcard("scip-typescript npm @types/express-serve-static-core % `index.d.ts`/IRouter#get.")
        
        #expect(typeormPattern.hasWildcards)
        #expect(axiosPattern.hasWildcards)
        #expect(expressPattern.hasWildcards)
        
        #expect(typeormPattern.rawValue.contains("typeorm %"))
        #expect(axiosPattern.rawValue.contains("axios %"))
        #expect(expressPattern.rawValue.contains("@types/express-serve-static-core %"))
    }
    
    @Test func testWildcardDetectionWithUnderscores() async throws {
        // Test that underscore wildcards are also detected
        let underscorePattern: ScipID = "scip-typescript npm typeorm 0.3._ repository/`BaseEntity.d.ts`/BaseEntity#findBy()."
        let mixedPattern: ScipID = "scip-typescript npm % 0.3._ repository/`BaseEntity.d.ts`/BaseEntity#%()."
        
        #expect(underscorePattern.hasWildcards)
        #expect(mixedPattern.hasWildcards)
    }
    
    @Test func testNonWildcardPatterns() async throws {
        // Ensure normal ScipIDs without wildcards are not detected as wildcards
        let normalPatterns = [
            "scip-typescript npm typeorm 0.3.17 repository/`BaseEntity.d.ts`/BaseEntity#findBy().",
            "scip-typescript npm axios 1.7.7 `index.d.ts`/AxiosStatic#create().",
            "scip-typescript npm @types/express-serve-static-core 4.19.6 `index.d.ts`/IRouter#get.",
            "semanticdb maven . . example/HelloService.MethodPerEndpoint#sayHello()."
        ]
        
        for patternString in normalPatterns {
            if let pattern = ScipID(patternString) {
                #expect(!pattern.hasWildcards, "Pattern '\(patternString)' should not be detected as wildcard")
            }
        }
    }
    
    @Test func testEdgeCasesForWildcards() async throws {
        // Test edge cases where % or _ might appear in legitimate contexts
        let legitimatePercent: ScipID = "scip-typescript npm my-package 1.0.0 src/`file%20with%20encoding.ts`/Function#method()."
        let legitimateUnderscore: ScipID = "scip-typescript npm my_package 1.0.0 src/`file_name.ts`/function_name#method()."
        
        // These should still be detected as wildcards because the implementation
        // checks for % and _ anywhere in the string, which is the safest approach
        // for SQLite LIKE queries
        #expect(legitimatePercent.hasWildcards)
        #expect(legitimateUnderscore.hasWildcards)
    }
    
    @Test func testFactoryMethodWithComplexPatterns() async throws {
        // Test factory method with more complex patterns
        let complexPattern = ScipID.withVersionWildcard("scip-typescript npm @types/node % src/`fs.d.ts`/promises/FileHandle#readFile().")
        
        #expect(complexPattern.hasWildcards)
        #expect(complexPattern.rawValue.contains("@types/node %"))
        
        // Test that the factory method works with already wildcarded strings
        let alreadyWildcard = ScipID.withVersionWildcard("scip-typescript npm axios % `index.d.ts`/AxiosStatic#%().")
        #expect(alreadyWildcard.hasWildcards)
        #expect(alreadyWildcard.rawValue == "scip-typescript npm axios % `index.d.ts`/AxiosStatic#%().")
    }
}
