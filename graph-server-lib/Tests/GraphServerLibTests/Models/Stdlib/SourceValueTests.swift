//
//  SourceValueTests.swift
//  graph-server-lib
//
//  Created by <PERSON><PERSON><PERSON> on 5/28/25.
//

@testable import GraphServerLib
import Foundation
import SwiftTreeSitter
import Testing

@Suite struct SourceValueTests: FixtureTests {
    private func node(fromParser parser: TypescriptParser, assignedToVariableWithName varName: String, fromRoot rootNode: SwiftTreeSitter.Node) -> SwiftTreeSitter.Node? {
        var maybeNode: SwiftTreeSitter.Node? = nil
        rootNode.enumerateChildren { n1 in
            guard maybeNode == nil else { return }
            guard n1.nodeType == "lexical_declaration" else { return }
            n1.enumerateChildren { n2 in
                guard maybeNode == nil else { return }
                guard n2.nodeType == "variable_declarator" else { return }
                guard n2.child(byFieldName: "name")?.sourceText(from: parser.sourceNSString) == varName else { return }
                maybeNode = n2.child(byFieldName: "value")
            }
        }
        return maybeNode
    }
    
    @Test func testKeypathFunctionality() throws {
        let compileUnit = CompileUnit(rootDir: URL(filePath: "/tmp/SourceValueTest"))
        let objectsFile = fixtureURL(directory: "SourceValueTests", fileName: "objects.ts")
        let parser = try TypescriptParser(file: objectsFile, compileUnit: compileUnit)
        let root = (try! parser.parse())!
        
        let sourceValueA = SourceValue(
            kind: .objectLiteral,
            node: self.node(fromParser: parser, assignedToVariableWithName: "a", fromRoot: root)!,
            parser: parser
        )
        #expect(!sourceValueA.isObject(withKeyPath: "key"))

        let sourceValueB = SourceValue(
            kind: .objectLiteral,
            node: self.node(fromParser: parser, assignedToVariableWithName: "b", fromRoot: root)!,
            parser: parser
        )
        #expect(sourceValueB.isObject(withKeyPath: "key"))
        let sourceValueBKey = sourceValueB.get(keyPath: "key")!
        #expect(sourceValueBKey.kind == .stringLiteral)
        #expect(sourceValueBKey.sourceStringValue() == "\"value\"")
        
        let sourceValueC = SourceValue(
            kind: .objectLiteral,
            node: self.node(fromParser: parser, assignedToVariableWithName: "c", fromRoot: root)!,
            parser: parser
        )
        #expect(sourceValueC.isObject(withKeyPath: "nested"))
        #expect(sourceValueC.isObject(withKeyPath: "nested.child"))
        #expect(sourceValueC.isObject(withKeyPath: "nested.child.x"))
        #expect(sourceValueC.isObject(withKeyPath: "nonexistent") == false)
        #expect(sourceValueC.isObject(withKeyPath: "nested.nonexistent") == false)
        let sourceValueCX = sourceValueC.get(keyPath: "nested.child.x")!
        #expect(sourceValueCX.kind == .stringLiteral)
        #expect(sourceValueCX.sourceStringValue() == "\"x\"")
        
        let sourceValueD = SourceValue(
            kind: .objectLiteral,
            node: self.node(fromParser: parser, assignedToVariableWithName: "d", fromRoot: root)!,
            parser: parser
        )
        #expect(sourceValueD.isObject(withKeyPath: "shorthandValue"))
        let sourceValueDShorthand = sourceValueD.get(keyPath: "shorthandValue")!
        #expect(sourceValueDShorthand.kind == .identifier)
        #expect(sourceValueDShorthand.sourceStringValue() == "shorthandValue")
    }
}
