@testable import GraphServerLib
import Foundation
import Testing
import SwiftTreeSitter


@Suite("AxiosConfigRule Tests")
struct AxiosConfigRuleTests {
    let database: Database
    let tempDir: URL
    
    init() {
        self.database = Database.inMemory()
        self.tempDir = URL(fileURLWithPath: NSTemporaryDirectory()).appendingPathComponent("testDir")
        do {
            try FileManager.default.createDirectory(at: self.tempDir, withIntermediateDirectories: true)
        } catch {
            fatalError("Failed to create temporary directory: \(error)")
        }
    }
    
    private func setupTestEnvironment(tsContent: String, fileName: String = "client.ts") async throws -> (tempDir: URL, fileURL: URL) {
        
        let fileURL = self.tempDir.appendingPathComponent("src/api/\(fileName)").resolvingSymlinksInPath()
        try FileManager.default.createDirectory(at: fileURL.deletingLastPathComponent(), withIntermediateDirectories: true)
        try tsContent.write(to: fileURL, atomically: true, encoding: .utf8)
        
        return (tempDir, fileURL)
    }
    
    @Test func detectsMissingTimeout() async throws {
        // Arrange: Create source file
        let tsContent = """
        const client = axios.create({
            baseURL: 'https://api.example.com',
            headers: {
                'Authorization': 'Bearer token',
                'User-Agent': 'MyApp/1.0'
            }
        });
        """
        let fileName = "detectsMissingTimeout"
        let (tempDir, fileURL) = try await setupTestEnvironment(tsContent: tsContent, fileName: fileName + ".ts")
        defer { try? FileManager.default.removeItem(at: tempDir) }

        let compileUnit = CompileUnit(rootDir: tempDir)
        await self.database.add(compileUnit: compileUnit)

        // Add axios.create method call symbol
        let axiosCreateSymbol = SymbolInformation(
            id: "scip-typescript npm axios 1.7.7 `index.d.ts`/AxiosStatic#create().",
            kind: .method,
            displayName: "create",
            occurrences: [
                try! SymbolOccurrence(
                    compileUnit: compileUnit,
                    document: "src/api/detectsMissingTimeout.ts",
                    range: [0,19,25], // axios.create
                    enclosingRange: [0,0,6,3],
                    roles: [.readAccess] // This is key - it's a read access (method call), not a definition
                )
            ],
            typeString: nil
        )

        await self.database.add(symbols: [axiosCreateSymbol], definedInCompileUnit: compileUnit)

        // Arrange: Create diff
        let diff = """
        diff --git a/src/api/\(fileName).ts b/src/api/\(fileName).ts
        index 123456..789012 100644
        --- a/src/api/\(fileName).ts
        +++ b/src/api/\(fileName).ts
        @@ -1,1 +1,7 @@
        +\(tsContent.components(separatedBy: .newlines).map { "+\($0)" }.joined(separator: "\n"))
        """

        let diffFile = tempDir.appendingPathComponent("changes.txt")
        try diff.write(to: diffFile, atomically: true, encoding: .utf8)

        // Act
        let context = try RuleContext(compileUnit: compileUnit, minimalDiffFileURL: diffFile, database: self.database)
        let result = try await AxiosConfigRule(database: self.database, context: context).findMatchingLines()

        // Assert
        #expect(result.count == 1)
    }
    
    @Test func detectsMissingHeaders() async throws {
        // Arrange: Create source file
        let tsContent = """
        const client = axios.create({
            baseURL: 'https://api.example.com',
            timeout: 5000,
            headers: {
                'Content-Type': 'application/json'
            }
        });
        """

        let (tempDir, fileURL) = try await setupTestEnvironment(tsContent: tsContent, fileName: "detectsMissingHeaders.ts")
        defer { try? FileManager.default.removeItem(at: tempDir) }

        let compileUnit = CompileUnit(rootDir: tempDir)
        await self.database.add(compileUnit: compileUnit)

        // Add axios.create method call symbol
        let axiosCreateSymbol = SymbolInformation(
            id: "scip-typescript npm axios 1.7.7 `index.d.ts`/AxiosStatic#create().",
            kind: .method,
            displayName: "create",
            occurrences: [
                try! SymbolOccurrence(
                    compileUnit: compileUnit,
                    document: "src/api/detectsMissingHeaders.ts",
                    range: [0,19,25], // axios.create
                    enclosingRange: [0,0,6,3],
                    roles: []
                )
            ],
            typeString: nil
        )

        await self.database.add(symbols: [axiosCreateSymbol], definedInCompileUnit: compileUnit)

        // Arrange: Create diff
        let diff = """
        diff --git a/src/api/detectsMissingHeaders.ts b/src/api/detectsMissingHeaders.ts
        index 123456..789012 100644
        --- a/src/api/detectsMissingHeaders.ts
        +++ b/src/api/detectsMissingHeaders.ts
        @@ -1,1 +1,7 @@
        +\(tsContent.components(separatedBy: .newlines).map { "+\($0)" }.joined(separator: "\n"))
        """

        let diffFile = tempDir.appendingPathComponent("detectsMissingHeaders.diff")
        try diff.write(to: diffFile, atomically: true, encoding: .utf8)

        // Act
        let context = try RuleContext(compileUnit: compileUnit, minimalDiffFileURL: diffFile, database: self.database)
        let result = try await AxiosConfigRule(database: self.database, context: context).findMatchingLines()

        // Assert
        #expect(result.count == 1)
    }
    
    @Test func acceptsValidConfig() async throws {
        // Arrange: Create source file
        let tsContent = """
        const client = axios.create({
            baseURL: 'https://api.example.com',
            timeout: 5000,
            headers: {
                'Authorization': 'Bearer token',
                'User-Agent': 'MyApp/1.0',
                'Content-Type': 'application/json'
            }
        });
        """

        let (tempDir, fileURL) = try await setupTestEnvironment(tsContent: tsContent, fileName: "acceptsValidConfig.ts")
        defer { try? FileManager.default.removeItem(at: tempDir) }

        let compileUnit = CompileUnit(rootDir: tempDir)
        await self.database.add(compileUnit: compileUnit)

        // Add axios.create method call symbol
        let axiosCreateSymbol = SymbolInformation(
            id: "scip-typescript npm axios 1.7.7 `index.d.ts`/AxiosStatic#create().",
            kind: .method,
            displayName: "create",
            occurrences: [
                try! SymbolOccurrence(
                    compileUnit: compileUnit,
                    document: "src/api/acceptsValidConfig.ts",
                    range: [0,19,25], // axios.create
                    enclosingRange: [0,0,8,3],
                    roles: []
                )
            ],
            typeString: nil
        )

        await self.database.add(symbols: [axiosCreateSymbol], definedInCompileUnit: compileUnit)

        // Arrange: Create diff
        let diff = """
        diff --git a/src/api/acceptsValidConfig.ts b/src/api/acceptsValidConfig.ts
        index 123456..789012 100644
        --- a/src/api/acceptsValidConfig.ts
        +++ b/src/api/acceptsValidConfig.ts
        @@ -1,1 +1,9 @@
        +\(tsContent.components(separatedBy: .newlines).map { "+\($0)" }.joined(separator: "\n"))
        """

        let diffFile = tempDir.appendingPathComponent("changes.diff")
        try diff.write(to: diffFile, atomically: true, encoding: .utf8)

        // Act
        let context = try RuleContext(compileUnit: compileUnit, minimalDiffFileURL: diffFile, database: self.database)
        let result = try await AxiosConfigRule(database: self.database, context: context).findMatchingLines()

        // Assert
        #expect(result.isEmpty)
    }
    
    @Test func handlesVariableHeaders() async throws {
        // Arrange: Create source file
        let tsContent = """
        const API_URL = "http://base"
        const headers = {
            'Authorization': getAuthToken(),
            'User-Agent': `${APP_NAME}/${VERSION}`
        };
        const client = axios.create({
            baseURL: API_URL,
            timeout: 5000,
            headers
        });
        """
        let fileName = "handlesVariableHeaders.ts"

        let (tempDir, fileURL) = try await setupTestEnvironment(tsContent: tsContent, fileName: fileName)
        defer { try? FileManager.default.removeItem(at: tempDir) }

        let compileUnit = CompileUnit(rootDir: tempDir)
        await self.database.add(compileUnit: compileUnit)

        // Add symbol for API_URL
        let apiUrlSymbol = SymbolInformation(
            id: "scip-typescript npm axios 1.7.7 . . API_URL#",
            kind: .variable,
            displayName: "API_URL",
            occurrences: [
                // Definition
                try! SymbolOccurrence(
                    compileUnit: compileUnit,
                    document: "src/api/\(fileName)",
                    range: [0,6,13],
                    enclosingRange: [0,0,0,28],
                    roles: [.definition]
                ),
                // Reference in axios.create config
                try! SymbolOccurrence(
                    compileUnit: compileUnit,
                    document: "src/api/\(fileName)",
                    range: [6,13,20],
                    enclosingRange: [5,0,9,2], // Fixed: Enclosing range should cover the entire axios.create call
                    roles: []
                )
            ],
            typeString: nil
        )

        // Add symbol for headers
        let headersSymbol = SymbolInformation(
            id: "scip-typescript npm axios 1.7.7 . . headers#",
            kind: .variable,
            displayName: "headers",
            occurrences: [
                try! SymbolOccurrence(
                    compileUnit: compileUnit,
                    document: "src/api/\(fileName)",
                    range: [1,6,13],
                    enclosingRange: [1,0,4,2],
                    roles: [.definition]
                ),
                // Added: Reference occurrence in axios.create
                try! SymbolOccurrence(
                    compileUnit: compileUnit,
                    document: "src/api/\(fileName)",
                    range: [8,4,11],
                    enclosingRange: [5,0,9,2], // Same enclosing range as the axios config object
                    roles: [.readAccess]
                )
            ],
            typeString: nil
        )

        // Add axios.create method call symbol
        let axiosCreateSymbol = SymbolInformation(
            id: "scip-typescript npm axios 1.7.7 `index.d.ts`/AxiosStatic#create().",
            kind: .method,
            displayName: "create",
            occurrences: [
                try! SymbolOccurrence(
                    compileUnit: compileUnit,
                    document: "src/api/\(fileName)",
                    range: [5,19,25], // axios.create
                    enclosingRange: [5,0,8,2], // Fixed: End at line 8 instead of 9 to fit within diff location
                    roles: [.readAccess] // Add readAccess role to match the working test
                )
            ],
            typeString: nil
        )

        await self.database.add(symbols: [apiUrlSymbol, headersSymbol, axiosCreateSymbol], definedInCompileUnit: compileUnit)

        // Arrange: Create diff
        let diff = """
        diff --git a/src/api/\(fileName) b/src/api/\(fileName)
        index 123456..789012 100644
        --- a/src/api/\(fileName)
        +++ b/src/api/\(fileName)
        @@ -1,1 +1,9 @@
        +\(tsContent.components(separatedBy: .newlines).map { "+\($0)" }.joined(separator: "\n"))
        """

        let diffFile = tempDir.appendingPathComponent("changes.diff")
        try diff.write(to: diffFile, atomically: true, encoding: .utf8)

        let context = try RuleContext(compileUnit: compileUnit, minimalDiffFileURL: diffFile, database: self.database)
        let result = try await AxiosConfigRule(database: self.database, context: context).findMatchingLines()

        // Assert: The rule should NOT flag this as a violation because:
        // 1. timeout is present (5000)
        // 2. headers property exists (even though it's a variable reference)
        #expect(result.count == 0)
    }


    @Test func resolvesHeadersAndDetectsViolations() async throws {
        // Arrange: Create source file
        let tsContent = """
        const API_URL = "http://base"
        const headers = {
            'Authorization': getAuthToken()

        };
        const client = axios.create({
            baseURL: API_URL,
            timeout: 5000,
            headers
        });
        """
        let fileName = "resolvesHeadersAndDetectsViolations.ts"

        let (tempDir, fileURL) = try await setupTestEnvironment(tsContent: tsContent, fileName: fileName)
        defer { try? FileManager.default.removeItem(at: tempDir) }

        let compileUnit = CompileUnit(rootDir: tempDir)
        await self.database.add(compileUnit: compileUnit)

        // Add symbol for API_URL
        let apiUrlSymbol = SymbolInformation(
            id: "scip-typescript npm axios 1.7.7 . . API_URL#",
            kind: .variable,
            displayName: "API_URL",
            occurrences: [
                // Definition
                try! SymbolOccurrence(
                    compileUnit: compileUnit,
                    document: "src/api/\(fileName)",
                    range: [0,6,13],
                    enclosingRange: [0,0,0,28],
                    roles: [.definition]
                ),
                // Reference in axios.create config
                try! SymbolOccurrence(
                    compileUnit: compileUnit,
                    document: "src/api/\(fileName)",
                    range: [6,13,20],
                    enclosingRange: [5,0,9,2], // Fixed: Enclosing range should cover the entire axios.create call
                    roles: []
                )
            ],
            typeString: nil
        )

        // Add symbol for headers
        let headersSymbol = SymbolInformation(
            id: "scip-typescript npm axios 1.7.7 . . headers#",
            kind: .variable,
            displayName: "headers",
            occurrences: [
                try! SymbolOccurrence(
                    compileUnit: compileUnit,
                    document: "src/api/\(fileName)",
                    range: [1,6,13],
                    enclosingRange: [1,0,4,2],
                    roles: [.definition]
                ),
                // Added: Reference occurrence in axios.create
                try! SymbolOccurrence(
                    compileUnit: compileUnit,
                    document: "src/api/\(fileName)",
                    range: [8,4,11],
                    enclosingRange: [5,0,9,2], // Same enclosing range as the axios config object
                    roles: [.readAccess]
                )
            ],
            typeString: nil
        )

        // Add axios.create method call symbol
        let axiosCreateSymbol = SymbolInformation(
            id: "scip-typescript npm axios 1.7.7 `index.d.ts`/AxiosStatic#create().",
            kind: .method,
            displayName: "create",
            occurrences: [
                try! SymbolOccurrence(
                    compileUnit: compileUnit,
                    document: "src/api/\(fileName)",
                    range: [5,19,25], // axios.create
                    enclosingRange: [5,0,8,2], // Fixed: End at line 8 instead of 9 to fit within diff location
                    roles: [.readAccess] // Add readAccess role to match the working test
                )
            ],
            typeString: nil
        )

        await self.database.add(symbols: [apiUrlSymbol, headersSymbol, axiosCreateSymbol], definedInCompileUnit: compileUnit)

        // Arrange: Create diff
        let diff = """
        diff --git a/src/api/\(fileName) b/src/api/\(fileName)
        index 123456..789012 100644
        --- a/src/api/\(fileName)
        +++ b/src/api/\(fileName)
        @@ -1,1 +1,9 @@
        +\(tsContent.components(separatedBy: .newlines).map { "+\($0)" }.joined(separator: "\n"))
        """

        let diffFile = tempDir.appendingPathComponent("changes.diff")
        try diff.write(to: diffFile, atomically: true, encoding: .utf8)

        let context = try RuleContext(compileUnit: compileUnit, minimalDiffFileURL: diffFile, database: self.database)
        let result = try await AxiosConfigRule(database: self.database, context: context).findMatchingLines()

        // Assert: The rule should flag this as a violation because:
        #expect(result.count == 1)
    }

    @Test func testAxiosWildcardVersionMatching() async throws {
        // Create test data with multiple axios versions
        let tsContent = """
        const client1 = axios.create({
            baseURL: 'https://api.example.com',
            headers: {
                'Authorization': 'Bearer token'
            }
        });
        
        const client2 = axios.create({
            timeout: 5000
        });
        """
        
        let (tempDir, fileURL) = try await setupTestEnvironment(tsContent: tsContent, fileName: "wildcardTest.ts")
        defer { try? FileManager.default.removeItem(at: tempDir) }

        let compileUnit = CompileUnit(rootDir: tempDir)
        await self.database.add(compileUnit: compileUnit)

        // Add axios.create symbols with different versions
        let axiosCreateSymbols = [
            SymbolInformation(
                id: "scip-typescript npm axios 1.6.8 `index.d.ts`/AxiosStatic#create().",
                kind: .method,
                displayName: "create",
                occurrences: [
                    try! SymbolOccurrence(
                        compileUnit: compileUnit,
                        document: "src/api/wildcardTest.ts",
                        range: [0,19,25], // First axios.create
                        enclosingRange: [0,0,5,3],
                        roles: [.readAccess]
                    )
                ],
                typeString: nil
            ),
            SymbolInformation(
                id: "scip-typescript npm axios 1.7.7 `index.d.ts`/AxiosStatic#create().",
                kind: .method,
                displayName: "create",
                occurrences: [
                    try! SymbolOccurrence(
                        compileUnit: compileUnit,
                        document: "src/api/wildcardTest.ts",
                        range: [7,19,25], // Second axios.create
                        enclosingRange: [7,0,9,3],
                        roles: [.readAccess]
                    )
                ],
                typeString: nil
            )
        ]

        await self.database.add(symbols: axiosCreateSymbols, definedInCompileUnit: compileUnit)

        // Create diff
        let diff = """
        diff --git a/src/api/wildcardTest.ts b/src/api/wildcardTest.ts
        index 123456..789012 100644
        --- a/src/api/wildcardTest.ts
        +++ b/src/api/wildcardTest.ts
        @@ -1,1 +1,11 @@
        +\(tsContent.components(separatedBy: .newlines).map { "+\($0)" }.joined(separator: "\n"))
        """

        let diffFile = tempDir.appendingPathComponent("wildcard.diff")
        try diff.write(to: diffFile, atomically: true, encoding: .utf8)

        // Test wildcard config
        let wildcardConfig = AxiosConfigRule.Config(
            axiosCreateMethodIDs: [
                "scip-typescript npm axios % `index.d.ts`/AxiosStatic#create()."
            ],
            axiosGetMethodIDs: [
                "scip-typescript npm axios % `index.d.ts`/Axios#get()."
            ],
            axiosPostMethodIDs: [
                "scip-typescript npm axios % `index.d.ts`/Axios#post()."
            ],
            requiredConfigKeys: ["timeout"],
            requiredHeaders: ["User-Agent"]
        )

        let context = try RuleContext(compileUnit: compileUnit, minimalDiffFileURL: diffFile, database: self.database)
        let wildcardRule = AxiosConfigRule(database: self.database, context: context, config: wildcardConfig)
        let result = try await wildcardRule.findMatchingLines()

        // Should detect violations in both axios.create calls
        // First call is missing timeout and User-Agent
        // Second call is missing User-Agent
        #expect(result.count == 2, "Should find violations in both axios.create calls")
    }
// 
}
