//
//  RunwayPrivateInTeamRuleTests.swift
//  graph-server-lib
//
//  Created by AI Assistant on 6/30/25.
//

@testable import GraphServerLib
import Foundation
import Testing

@Suite("RunwayPrivateInTeamRuleTests")
struct RunwayPrivateInTeamRuleTests: DiffTests, SCIPFixtureTests {
    let database: Database
    
    init() {
        self.database = Database.inMemory()
    }

    @Test func testFindsExpectedViolations() async throws {
        let cloneDir = try await cloneAndOpenSCIPIndex(fromFixtureDirectory: "RunwayPrivateInTeamRuleTests", subdirectory: "example-api", database: self.database)
        await OpenCompileUnits(database: self.database).run(compileUnitURLs: [cloneDir], ignoringLocals: true)
        
        let compileUnit = CompileUnit(rootDir: cloneDir)
        let diffURL = writeDiffToTempDirectory(forEntireFile: cloneDir.appendingPathComponent("main.ts"))
        let context = try RuleContext(compileUnit: compileUnit, minimalDiffFileURL: diffURL, database: self.database)
        let rule = RunwayPrivateInTeamRule(database: self.database, context: context, config: .testDefault())
        
        let matchingLines = try await rule.findMatchingLines()
        let locations = matchingLines.map { $0.location }
        
        // Should find at least some violations (includes all patterns)
        let violations = locations.filter { location in
            location.documentPath == "main.ts"
        }

        #expect(violations.count == 6, "Should find 6 violations")
        // Remember, these line numbers are zero-indexed
        #expect(violations.map { $0.locationInFile.getFirstPoint().row }.sorted() == [13, 16, 32, 35, 63, 100], "Violations should match these lines")
    }
    
    @Test func testTypeormWildcardVersionMatching() async throws {
        let cloneDir = try await cloneAndOpenSCIPIndex(fromFixtureDirectory: "RunwayPrivateInTeamRuleTests", subdirectory: "example-api", database: self.database)
        await OpenCompileUnits(database: self.database).run(compileUnitURLs: [cloneDir], ignoringLocals: true)
        
        let compileUnit = CompileUnit(rootDir: cloneDir)
        let diffURL = writeDiffToTempDirectory(forEntireFile: cloneDir.appendingPathComponent("main.ts"))
        let context = try RuleContext(compileUnit: compileUnit, minimalDiffFileURL: diffURL, database: self.database)
        
        // Test wildcard config that should match multiple typeorm versions
        let wildcardConfig = RunwayPrivateInTeamRule.Config(
            findByMethodIDs: [
                "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#findBy().",
                "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#findOneBy().",
                "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#countBy().",
                "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#existsBy()."
            ],
            findMethodIDs: [
                "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#find().",
                "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#findOne()."
            ],
            queryBuilderMethodIDs: [
                "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#createQueryBuilder()."
            ],
            receiverIDs: [
                "scip-typescript npm example-api 1.0.0 `TaskArtifact.ts`/TaskArtifact#"
            ],
            whereMethodIDs: [
                "scip-typescript npm typeorm % query-builder/`SelectQueryBuilder.d.ts`/SelectQueryBuilder#where().",
                "scip-typescript npm typeorm % query-builder/`SelectQueryBuilder.d.ts`/SelectQueryBuilder#andWhere()."
            ]
        )
        
        let wildcardRule = RunwayPrivateInTeamRule(database: self.database, context: context, config: wildcardConfig)
        let wildcardResults = try await wildcardRule.findMatchingLines()
        
        // Compare with the old explicit version config
        let explicitRule = RunwayPrivateInTeamRule(database: self.database, context: context, config: .testDefault())
        let explicitResults = try await explicitRule.findMatchingLines()
        
        // Wildcard should match at least as many violations as explicit versions
        #expect(wildcardResults.count >= explicitResults.count, "Wildcard config should match at least as many violations as explicit versions")
        
        // Verify specific violations are still found
        let wildcardLocations = wildcardResults.map { $0.location }
        let wildcardViolations = wildcardLocations.filter { $0.documentPath == "main.ts" }
        #expect(wildcardViolations.count >= 6, "Wildcard config should find at least 6 violations")
    }

    @Test func testMultipleReceiverTypes() async throws {
        let cloneDir = try await cloneAndOpenSCIPIndex(fromFixtureDirectory: "RunwayPrivateInTeamRuleTests", subdirectory: "example-api", database: self.database)
        await OpenCompileUnits(database: self.database).run(compileUnitURLs: [cloneDir], ignoringLocals: true)
        
        let compileUnit = CompileUnit(rootDir: cloneDir)
        let diffURL = writeDiffToTempDirectory(forEntireFile: cloneDir.appendingPathComponent("main.ts"))
        let context = try RuleContext(compileUnit: compileUnit, minimalDiffFileURL: diffURL, database: self.database)
        
        // Create a config with multiple receiver types
        let multiReceiverConfig = RunwayPrivateInTeamRule.Config(
            findByMethodIDs: [
                "scip-typescript npm typeorm 0.3.20 repository/`BaseEntity.d.ts`/BaseEntity#findBy().",
                "scip-typescript npm typeorm 0.3.20 repository/`BaseEntity.d.ts`/BaseEntity#findOneBy().",
                "scip-typescript npm typeorm 0.3.20 repository/`BaseEntity.d.ts`/BaseEntity#countBy().",
                "scip-typescript npm typeorm 0.3.20 repository/`BaseEntity.d.ts`/BaseEntity#existsBy()."
            ],
            findMethodIDs: [
                "scip-typescript npm typeorm 0.3.20 repository/`BaseEntity.d.ts`/BaseEntity#find().",
                "scip-typescript npm typeorm 0.3.20 repository/`BaseEntity.d.ts`/BaseEntity#findOne()."
            ],
            queryBuilderMethodIDs: [
                "scip-typescript npm typeorm 0.3.20 repository/`BaseEntity.d.ts`/BaseEntity#createQueryBuilder()."
            ],
            receiverIDs: [
                "scip-typescript npm example-api 1.0.0 `TaskArtifact.ts`/TaskArtifact#",
                "scip-typescript npm example-api 1.0.0 `Task.ts`/Task#" // Add Task as second receiver type
            ],
            whereMethodIDs: [
                "scip-typescript npm typeorm 0.3.20 query-builder/`SelectQueryBuilder.d.ts`/SelectQueryBuilder#where().",
                "scip-typescript npm typeorm 0.3.20 query-builder/`SelectQueryBuilder.d.ts`/SelectQueryBuilder#andWhere()."
            ]
        )
        
        let rule = RunwayPrivateInTeamRule(database: self.database, context: context, config: multiReceiverConfig)
        
        let matchingLines = try await rule.findMatchingLines()
        let locations = matchingLines.map { $0.location }
        
        let violations = locations.filter { location in
            location.documentPath == "main.ts"
        }
        
        // Should find 6 violations: 6 from TaskArtifact + 2 from Task entity
        #expect(violations.count == 8, "Should find 8 violations with multiple receiver types")
    }
}
