//
//  RunwayApiInterfaceRuleTests.swift
//  graph-server-lib
//
//  Created by <PERSON><PERSON><PERSON> on 6/12/25.
//

@testable import GraphServerLib
import Foundation
import Testing

@Suite("RunwayApiInterfaceRuleTests")
struct RunwayApiInterfaceRuleTests: DiffTests, FixtureTests {
    let database: Database
    
    init() {
        self.database = Database.inMemory()
    }

    @Test func testItWorksOnExpressExampleFixture() async throws {
        let fixtureURL = fixtureURL(directory: "RunwayApiInterfaceRuleTests", subdirectory: "express-example")
        await OpenCompileUnits(database: self.database).run(compileUnitURLs: [fixtureURL], ignoringLocals: true)
        
        let compileUnit = CompileUnit(rootDir: fixtureURL)
        let diffURL = writeDiffToTempDirectory(forEntireFile: fixtureURL.appendingPathComponent("index.ts"))
        let context = try RuleContext(compileUnit: compileUnit, minimalDiffFileURL: diffURL, database: self.database)
        let locations = try await RunwayApiInterfaceRule(database: self.database, context: context, config: .testDefault()).run(inLocations: [])
        // Should only find the 4 Express router method calls, not the custom API interface methods
        #expect(locations.count == 4)
        #expect(locations[0].documentPath == "index.ts")
        #expect(locations[0].locationInFile == .range(try! .fromUTF16SCIPRange([6, 0, 8, 2]))) // router.get
        #expect(locations[1].documentPath == "index.ts")
        #expect(locations[1].locationInFile == .range(try! .fromUTF16SCIPRange([10, 0, 12, 2]))) // router.post
        #expect(locations[2].documentPath == "index.ts")
        #expect(locations[2].locationInFile == .range(try! .fromUTF16SCIPRange([14, 0, 16, 2]))) // router.put
        #expect(locations[3].documentPath == "index.ts")
        #expect(locations[3].locationInFile == .range(try! .fromUTF16SCIPRange([18, 0, 20, 2]))) // router.delete
    }

    @Test func testFindMatchingLines() async throws {
        let fixtureURL = fixtureURL(directory: "RunwayApiInterfaceRuleTests", subdirectory: "express-example")
        await OpenCompileUnits(database: self.database).run(compileUnitURLs: [fixtureURL], ignoringLocals: true)
        
        let compileUnit = CompileUnit(rootDir: fixtureURL)
        let diffURL = writeDiffToTempDirectory(forEntireFile: fixtureURL.appendingPathComponent("index.ts"))
        let context = try RuleContext(compileUnit: compileUnit, minimalDiffFileURL: diffURL, database: self.database)
        let rule = RunwayApiInterfaceRule(database: self.database, context: context, config: .testDefault())
        let diffLines = try await rule.findMatchingLines()
        
        // Should only find the 4 Express router method calls, not the custom API interface methods
        #expect(diffLines.count == 4)
        #expect(diffLines[0].side == .new)
        #expect(diffLines[0].location.documentPath == "index.ts")
        #expect(diffLines[0].location.locationInFile == .range(try! .fromUTF16SCIPRange([6, 0, 8, 2]))) // router.get
        #expect(diffLines[1].side == .new)
        #expect(diffLines[1].location.documentPath == "index.ts")
        #expect(diffLines[1].location.locationInFile == .range(try! .fromUTF16SCIPRange([10, 0, 12, 2]))) // router.post
        #expect(diffLines[2].side == .new)
        #expect(diffLines[2].location.documentPath == "index.ts")
        #expect(diffLines[2].location.locationInFile == .range(try! .fromUTF16SCIPRange([14, 0, 16, 2]))) // router.put
        #expect(diffLines[3].side == .new)
        #expect(diffLines[3].location.documentPath == "index.ts")
        #expect(diffLines[3].location.locationInFile == .range(try! .fromUTF16SCIPRange([18, 0, 20, 2]))) // router.delete
    }

    @Test func testExpressWildcardVersionMatching() async throws {
        let fixtureURL = fixtureURL(directory: "RunwayApiInterfaceRuleTests", subdirectory: "express-example")
        await OpenCompileUnits(database: self.database).run(compileUnitURLs: [fixtureURL], ignoringLocals: true)
        
        let compileUnit = CompileUnit(rootDir: fixtureURL)
        let diffURL = writeDiffToTempDirectory(forEntireFile: fixtureURL.appendingPathComponent("index.ts"))
        let context = try RuleContext(compileUnit: compileUnit, minimalDiffFileURL: diffURL, database: self.database)
        
        // Test wildcard config that should match all Express router versions
        let wildcardConfig = RunwayApiInterfaceRule.Config(
            expressRouterMethodIDs: [
                "scip-typescript npm @types/express-serve-static-core % `index.d.ts`/IRouter#get.",
                "scip-typescript npm @types/express-serve-static-core % `index.d.ts`/IRouter#post.",
                "scip-typescript npm @types/express-serve-static-core % `index.d.ts`/IRouter#put.",
                "scip-typescript npm @types/express-serve-static-core % `index.d.ts`/IRouter#delete."
            ]
        )
        
        let wildcardRule = RunwayApiInterfaceRule(database: self.database, context: context, config: wildcardConfig)
        let wildcardLocations = try await wildcardRule.run(inLocations: [])
        
        // Compare with the programmatically generated config from .testDefault()
        let explicitRule = RunwayApiInterfaceRule(database: self.database, context: context, config: .testDefault())
        let explicitLocations = try await explicitRule.run(inLocations: [])
        
        // Wildcard config should produce the same results as programmatic version
        #expect(wildcardLocations.count == explicitLocations.count, "Wildcard config should match same number of locations as explicit config")
        #expect(wildcardLocations.count == 4, "Should find 4 Express router method calls")
        
        // Verify that all wildcard locations exist in explicit locations
        for wildcardLocation in wildcardLocations {
            let found = explicitLocations.contains { explicit in
                explicit.documentPath == wildcardLocation.documentPath && 
                explicit.locationInFile == wildcardLocation.locationInFile
            }
            #expect(found, "Wildcard location should be found in explicit locations")
        }
        
        // Test that findMatchingLines also works with wildcard config
        let wildcardDiffLines = try await wildcardRule.findMatchingLines()
        let explicitDiffLines = try await explicitRule.findMatchingLines()
        
        #expect(wildcardDiffLines.count == explicitDiffLines.count, "Wildcard findMatchingLines should match explicit results")
        #expect(wildcardDiffLines.count == 4, "Should find 4 matching diff lines")
    }


}
