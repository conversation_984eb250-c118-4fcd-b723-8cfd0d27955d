import { TaskArtifact } from './TaskArtifact';
import { Task } from './Task';

export function testCases() {
  const userId = "user123";
  const privateInTeam = true;

  // Pattern 1: findBy/findOneBy/countBy pattern - VALID (no userId, so we don't care)
  TaskArtifact.findBy({ content: "test" }); // Should NOT be flagged - no userId specified
  TaskArtifact.findOneBy({ id: "123" }); // Should NOT be flagged - no userId specified
  TaskArtifact.countBy({ }); // Should NOT be flagged - no userId specified

  // Pattern 1: findBy/findOneBy/countBy pattern - VIOLATIONS (userId without privateInTeam)
  TaskArtifact.findBy({ userId: "user123" }); // Should be flagged - has userId but missing privateInTeam
  TaskArtifact.findOneBy({ privateInTeam: true }); // Should NOT be flagged - no userId specified
  // Should be flagged — not adding `privateInTeam`
  TaskArtifact.existsBy({
    userId: "user123",
    ...(privateInTeam ? { createdBy: "user123" } : {})
  })

  // Pattern 1: findBy/findOneBy/countBy pattern - VALID (both fields present)
  TaskArtifact.findBy({ userId: "user123", privateInTeam: true }); // Should NOT be flagged
  TaskArtifact.findOneBy({ userId: "user123", privateInTeam: false, content: "test" }); // Should NOT be flagged
  TaskArtifact.countBy({ userId: "user123", privateInTeam: true }); // Should NOT be flagged

  // Pattern 2: find/findOne pattern - VALID (no where.userId, so we don't care)
  TaskArtifact.find({ select: ["id", "content"] }); // Should NOT be flagged - no where.userId
  TaskArtifact.findOne({ order: { id: "ASC" } }); // Should NOT be flagged - no where.userId

  // Pattern 2: find/findOne pattern - VIOLATIONS (where.userId without where.privateInTeam)
  TaskArtifact.find({ where: { content: "test" } }); // Should NOT be flagged - no where.userId
  TaskArtifact.findOne({ where: { userId: "user123" } }); // Should be flagged - has where.userId but missing where.privateInTeam
  TaskArtifact.find({ where: { privateInTeam: true } }); // Should NOT be flagged - no where.userId
  // Should be flagged — first where clause has userId but missing privateInTeam
  TaskArtifact.findOne({
    where: [
      {
        userId: "user123",
        id: "id123",
        createdBy: "user123",
      },
      {
        userId: "user123",
        id: "id123",
        privateInTeam: false,
      }
    ]
  })


  // Pattern 2: find/findOne pattern - VALID (both where conditions present)
  TaskArtifact.find({ where: { userId: "user123", privateInTeam: true } }); // Should NOT be flagged
  TaskArtifact.findOne({ where: { userId: "user123", privateInTeam: false, content: "test" } }); // Should NOT be flagged

  // Pattern 3: queryBuilder pattern - VALID (no userId where clause, so we don't care)
  TaskArtifact.createQueryBuilder()
    .getMany(); // Should NOT be flagged - no where clauses at all

  TaskArtifact.createQueryBuilder()
    .where("content = :content", { content: "test" })
    .getMany(); // Should NOT be flagged - no userId where clause

  TaskArtifact.createQueryBuilder()
    .where("userId = :userId", { userId: "user123" })
    .getOne(); // Should be flagged - has userId where clause but missing privateInTeam where clause

  TaskArtifact.createQueryBuilder()
    .where("privateInTeam = :privateInTeam", { privateInTeam: true })
    .getMany(); // Should NOT be flagged - no userId where clause

  // Pattern 3: queryBuilder pattern - VALID (both where conditions present)
  TaskArtifact.createQueryBuilder()
    .where("userId = :userId", { userId: "user123" })
    .andWhere("privateInTeam = :privateInTeam", { privateInTeam: true })
    .getMany(); // Should NOT be flagged

  TaskArtifact.createQueryBuilder()
    .where("userId = :userId AND privateInTeam = :privateInTeam", { userId: "user123", privateInTeam: false })
    .getOne(); // Should NOT be flagged

  TaskArtifact.createQueryBuilder()
    .andWhere("userId = :userId", { userId: "user123" })
    .where("privateInTeam = :privateInTeam", { privateInTeam: true })
    .andWhere("content LIKE :content", { content: "%test%" })
    .getMany(); // Should NOT be flagged

  // Pattern 3: queryBuilder pattern - VALID (complex join with both userId and privateInTeam)
  const qb = TaskArtifact.createQueryBuilder('ta')
    .innerJoin(Task, 't', 't.id = ta.taskId')
    .where('t.userId = :userId', { userId })
    .andWhere('t.deleted = :deleted', { deleted: false })
    .andWhere('t.status = :status', { status: 'SUCCEEDED' })
    .andWhere('t.internal = :internal', { internal: false })
    .andWhere('ta.deleted = :deleted', { deleted: false })
    .andWhere('ta.reported = :reported', { reported: false })
    .andWhere('ta.privateInTeam = :privateInTeam', { privateInTeam })
    .andWhere('ta.userId = :userId', { userId }); // Should NOT be flagged - has both userId and privateInTeam

  // Pattern 3: queryBuilder pattern - VIOLATION (complex join with userId but missing privateInTeam)
  const qb2 = TaskArtifact.createQueryBuilder('ta')
    .innerJoin(Task, 't', 't.id = ta.taskId')
    .where('t.userId = :userId', { userId: "user456" })
    .andWhere('t.deleted = :deleted', { deleted: false })
    .andWhere('t.status = :status', { status: 'SUCCEEDED' })
    .andWhere('t.internal = :internal', { internal: false })
    .andWhere('ta.deleted = :deleted', { deleted: false })
    .andWhere('ta.reported = :reported', { reported: false })
    .andWhere('ta.userId = :userId', { userId: "user456" }); // Should be flagged - has ta.userId but missing ta.privateInTeam
}

// Non-TaskArtifact calls should be ignored
class OtherEntity {
  static findBy(criteria: any): any[] { return []; }
  static find(options: any): any[] { return []; }
  static createQueryBuilder(): any { return {}; }
}

export function nonTaskArtifactCalls() {
  // These should all be ignored since they're not TaskArtifact calls
  OtherEntity.findBy({ content: "test" });
  OtherEntity.find({ where: { content: "test" } });
  OtherEntity.createQueryBuilder().where("content = :content").getMany();
}

export function taskEntityCalls() {
  // Test calls to Task entity - should be flagged when included in receiverIDs
  Task.findBy({ userId: "user789" }); // Should be flagged when Task is in receiverIDs
  Task.find({ where: { userId: "user790" } }); // Should be flagged when Task is in receiverIDs  
}
