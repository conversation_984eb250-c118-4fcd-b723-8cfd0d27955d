If you change the test files here, generate a new `index.scip` file:
1. Install dependencies: `npm install` (TypeORM 0.3.20 is required)
2. `cd` into the `scip-typescript` folder at the root of our monorepo
3. Run `npx scip-typescript index --cwd <this directory>`

E.g.:

```
cd /Users/<USER>/Developer/tanagram-monorepo/graph-server-lib/Tests/GraphServerLibTests/Fixtures/RunwayPrivateInTeamRuleTests/example-api
npm install
cd /Users/<USER>/Developer/tanagram-monorepo/scip-typescript
npx scip-typescript index --cwd /Users/<USER>/Developer/tanagram-monorepo/graph-server-lib/Tests/GraphServerLibTests/Fixtures/RunwayPrivateInTeamRuleTests/example-api
```

This test fixture uses the real TypeORM 0.3.20 package to ensure the SCIP symbol IDs match what would be found in actual production code. The TaskArtifact entity extends TypeORM's BaseEntity class and uses proper TypeORM decorators.

You can view the outputted file using `SCIPBrowser` to help you get the correct IDs and locations for your tests.
