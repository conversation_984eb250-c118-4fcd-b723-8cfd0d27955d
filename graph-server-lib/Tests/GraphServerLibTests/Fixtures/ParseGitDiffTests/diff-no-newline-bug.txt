diff --git a/app/types/transactionTypes.ts b/app/types/transactionTypes.ts
index 7898ec6..268a825 100644
--- a/app/types/transactionTypes.ts
+++ b/app/types/transactionTypes.ts
@@ -9,0 +10 @@ export enum TransactionType {
+  BALANCE_TRANSFER = 'BALANCE_TRANSFER'
@@ -10,0 +12,3 @@ export enum TransactionType {
+
+// Always update, even if deleted, to allow for other systems to consume.
+await Task.update({ id: this.id }, { status: 'CANCELLED' });
\ No newline at end of file
