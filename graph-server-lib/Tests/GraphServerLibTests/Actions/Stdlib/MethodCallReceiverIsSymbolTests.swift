//
//  MethodCallReceiverIsSymbolTests.swift
//  graph-server-lib
//
//  Created by AI Agent on 6/29/25.
//

@testable import GraphServerLib
import Foundation
import Testing

@Suite("MethodCallReceiverIsSymbolTests")
struct MethodCallReceiverIsSymbolTests: SCIPFixtureTests {
    let database: Database
    
    init() {
        self.database = Database.inMemory()
    }

    @Test func returnsTrueForStaticMethodCalls() async throws {
        let cloneDir = try await cloneAndOpenSCIPIndex(fromFixtureDirectory: "GetCallsToMethodTests", subdirectory: "scip-playground", database: self.database)
        let clientTSFullFile = CodeLocation(
            compileUnit: CompileUnit(rootDir: cloneDir),
            documentPath: "client.ts",
            locationInFile: .range(try! .fromUTF16SCIPRange([0, 0, 8, 0]))
        )
        
        // Get the axios static method call (axios.create in client.ts)
        let axiosCreateCalls = try await GetCallsToMethods(database: self.database)
            .run(
                inLocations: [clientTSFullFile],
                methodIDs: ["scip-typescript npm axios 1.9.0 `index.d.ts`/AxiosInstance#create()."]
            )
        #expect(axiosCreateCalls.count == 1)
        let axiosCreateCall = axiosCreateCalls[0]
        
        // Test that static method calls return true when receiver symbols match
        let axiosItself: ScipID = "scip-typescript npm axios 1.9.0 `index.d.ts`/axios."
        
        let checker = MethodCallReceiverIsSymbol(database: self.database)
        
        let result = try await checker.run(methodCall: axiosCreateCall, receiverSymbols: [axiosItself])
        #expect(result == true)
        
        // Test that method calls return false when receiver symbols don't match  
        let nonExistentSymbol: ScipID = "scip-typescript npm nonexistent 1.0.0 `index.d.ts`/SomeClass#"
        let result2 = try await checker.run(methodCall: axiosCreateCall, receiverSymbols: [nonExistentSymbol])
        #expect(result2 == false)
    }

    @Test func returnsFalseForInstanceMethodCalls() async throws {
        let cloneDir = try await cloneAndOpenSCIPIndex(fromFixtureDirectory: "GetCallsToMethodTests", subdirectory: "scip-playground", database: self.database)
        let indexTSFullFile = CodeLocation(
            compileUnit: CompileUnit(rootDir: cloneDir),
            documentPath: "index.ts",
            locationInFile: .range(try! .fromUTF16SCIPRange([0, 0, 13, 0]))
        )
        
        // Get instance method calls (client.get - this is an instance method call)
        let clientGetCalls = try await GetCallsToMethods(database: self.database)
            .run(
                inLocations: [indexTSFullFile],
                methodIDs: ["scip-typescript npm axios 1.9.0 `index.d.ts`/Axios#get()."]
            )
        #expect(clientGetCalls.count == 1)
        let clientGetCall = clientGetCalls[0]
        
        // Test that instance method calls return false even when checking for the axios symbol
        // The receiver here is the `client` variable, not the `axios` class
        let axiosSymbol: ScipID = "scip-typescript npm axios 1.9.0 `index.d.ts`/Axios#"
        let checker = MethodCallReceiverIsSymbol(database: self.database)
        
        // This should return false because client.get() is an instance method call
        let result = try await checker.run(methodCall: clientGetCall, receiverSymbols: [axiosSymbol])
        #expect(result == false)
    }

    @Test func returnsFalseForEmptyReceiverSymbols() async throws {
        let cloneDir = try await cloneAndOpenSCIPIndex(fromFixtureDirectory: "GetCallsToMethodTests", subdirectory: "scip-playground", database: self.database)
        let indexTSFullFile = CodeLocation(
            compileUnit: CompileUnit(rootDir: cloneDir),
            documentPath: "index.ts",
            locationInFile: .range(try! .fromUTF16SCIPRange([0, 0, 13, 0]))
        )
        
        let axiosGetCalls = try await GetCallsToMethods(database: self.database)
            .run(
                inLocations: [indexTSFullFile],
                methodIDs: ["scip-typescript npm axios 1.9.0 `index.d.ts`/Axios#get()."]
            )
        #expect(axiosGetCalls.count == 1)
        let getCall = axiosGetCalls[0]
        
        let checker = MethodCallReceiverIsSymbol(database: self.database)
        let result = try await checker.run(methodCall: getCall, receiverSymbols: [])
        #expect(result == false)
    }
    
    @Test func supportsMultipleReceiverIDs() async throws {
        let cloneDir = try await cloneAndOpenSCIPIndex(fromFixtureDirectory: "GetCallsToMethodTests", subdirectory: "scip-playground", database: self.database)
        let clientTSFullFile = CodeLocation(
            compileUnit: CompileUnit(rootDir: cloneDir),
            documentPath: "client.ts",
            locationInFile: .range(try! .fromUTF16SCIPRange([0, 0, 8, 0]))
        )
        
        // Get the axios static method call (axios.create in client.ts)
        let axiosCreateCalls = try await GetCallsToMethods(database: self.database)
            .run(
                inLocations: [clientTSFullFile],
                methodIDs: ["scip-typescript npm axios 1.9.0 `index.d.ts`/AxiosInstance#create()."]
            )
        #expect(axiosCreateCalls.count == 1)
        let axiosCreateCall = axiosCreateCalls[0]
        
        let axiosItself: ScipID = "scip-typescript npm axios 1.9.0 `index.d.ts`/axios."
        let nonExistentSymbol: ScipID = "scip-typescript npm nonexistent 1.0.0 `index.d.ts`/SomeClass#"
        
        let checker = MethodCallReceiverIsSymbol(database: self.database)
        
        let result = try await checker.run(methodCall: axiosCreateCall, receiverSymbols: [axiosItself, nonExistentSymbol])
        #expect(result == true)
    }
}
