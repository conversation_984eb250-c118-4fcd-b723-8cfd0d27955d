//
//  ParseGitDiffTests.swift
//  graph-server-lib
//
//  Created by <PERSON><PERSON><PERSON> on 2/26/25.
//

@testable import GraphServerLib
import Foundation
import Testing

@Suite("ParseGitDiff Tests")
struct ParseGitDiffTests {
    private func fixtureURL(for fileName: String) -> URL {
        Bundle.module.url(forResource: "Fixtures/ParseGitDiffTests/\(fileName)", withExtension: nil)!
    }

    @Test func worksForASimpleDiff() {
        let compileUnit = CompileUnit(rootDir: URL(filePath: "/tmp/"))
        let simpleDiffFileURL = fixtureURL(for: "diff1.txt")
        let result = try! ParseGitDiff(compileUnit: compileUnit, diffFileURL: simpleDiffFileURL).run()
        let diffLineCodeLocation = CodeLocation(
            compileUnit: compileUnit,
            documentPath: "airflow/www/app.py",
            locationInFile: .point(try! PointUTF16Z(row: 70, column: 0))
        )
        let newSideLine = ParseGitDiff.DiffSideLine(side: .new, location: diffLineCodeLocation, content: "    print(\"hello\")")
        let diffLine = ParseGitDiff.DiffLine(oldContent: nil, newContent: newSideLine)
        #expect(result.diffLines == [diffLine])
        
        // Test that hunk locations are generated for the new file addition
        #expect(result.hunkLocations.count == 1)
        let startPoint = try! PointUTF16Z(row: 70, column: 0)
        let endPoint = try! PointUTF16Z(row: 71, column: 0)
        let hunkCodeLocation = CodeLocation(
            compileUnit: compileUnit,
            documentPath: "airflow/www/app.py",
            locationInFile: .range(startPoint ..< endPoint)
        )
        let expectedHunkLocation = ParseGitDiff.HunkLocation(side: .new, location: hunkCodeLocation)
        #expect(result.hunkLocations.contains(expectedHunkLocation))
    }

    @Test func worksForAComplexAdditionDiff() {
        let compileUnit = CompileUnit(rootDir: URL(filePath: "/tmp/"))
        let complexDiffFileURL = fixtureURL(for: "diff2.txt")
        let result = try! ParseGitDiff(compileUnit: compileUnit, diffFileURL: complexDiffFileURL).run()
        let outputSet = Set(result.diffLines)

        // Expectations were determined by manually going through the diff file the first time I wrote this code and got it working.
        #expect(result.diffLines.count == 269)
        // Each of these blocks below was written by manually translating hunks in the diff fixture
        for n in 0..<136 {
            let codeLocation = CodeLocation(compileUnit: compileUnit, documentPath: "graph-server-lib/Sources/GraphServerLib/Actions/CallHierarchyForSymbol.swift", locationInFile: .point(try! PointUTF16Z(row: n, column: 0)))
            #expect(outputSet.contains { $0.newContent!.location == codeLocation })
        }
        for n in 7..<9 {
            let codeLocation = CodeLocation(compileUnit: compileUnit, documentPath: "graph-server-lib/Sources/GraphServerLib/Models/CodeLocation.swift", locationInFile: .point(try! PointUTF16Z(row: n, column: 0)))
            #expect(outputSet.contains { $0.newContent!.location == codeLocation })
        }
        for n in 44..<48 {
            let codeLocation = CodeLocation(compileUnit: compileUnit, documentPath: "graph-server-lib/Sources/GraphServerLib/Models/CodeLocation.swift", locationInFile: .point(try! PointUTF16Z(row: n, column: 0)))
            #expect(outputSet.contains { $0.newContent!.location == codeLocation })
        }
        for n in 379..<380 {
            let codeLocation = CodeLocation(compileUnit: compileUnit, documentPath: "graph-server-lib/Sources/GraphServerLib/Models/Database.swift", locationInFile: .point(try! PointUTF16Z(row: n, column: 0)))
            #expect(outputSet.contains { $0.newContent!.location == codeLocation })
        }
        for n in 388..<390 {
            let codeLocation = CodeLocation(compileUnit: compileUnit, documentPath: "graph-server-lib/Sources/GraphServerLib/Models/Database.swift", locationInFile: .point(try! PointUTF16Z(row: n, column: 0)))
            #expect(outputSet.contains { $0.newContent!.location == codeLocation })
        }
        for n in 392..<393 {
            let codeLocation = CodeLocation(compileUnit: compileUnit, documentPath: "graph-server-lib/Sources/GraphServerLib/Models/Database.swift", locationInFile: .point(try! PointUTF16Z(row: n, column: 0)))
            #expect(outputSet.contains { $0.newContent!.location == codeLocation })
        }
        for n in 395..<397 {
            let codeLocation = CodeLocation(compileUnit: compileUnit, documentPath: "graph-server-lib/Sources/GraphServerLib/Models/Database.swift", locationInFile: .point(try! PointUTF16Z(row: n, column: 0)))
            #expect(outputSet.contains { $0.newContent!.location == codeLocation })
        }
        for n in 398..<402 {
            let codeLocation = CodeLocation(compileUnit: compileUnit, documentPath: "graph-server-lib/Sources/GraphServerLib/Models/Database.swift", locationInFile: .point(try! PointUTF16Z(row: n, column: 0)))
            #expect(outputSet.contains { $0.newContent!.location == codeLocation })
        }
        for n in 46..<47 {
            let codeLocation = CodeLocation(compileUnit: compileUnit, documentPath: "graph-server-lib/Sources/GraphServerLib/Models/SymbolOccurrence.swift", locationInFile: .point(try! PointUTF16Z(row: n, column: 0)))
            #expect(outputSet.contains { $0.newContent!.location == codeLocation })
        }
        for n in 85..<89 {
            let codeLocation = CodeLocation(compileUnit: compileUnit, documentPath: "graph-server-lib/Sources/GraphServerLib/Models/SymbolOccurrence.swift", locationInFile: .point(try! PointUTF16Z(row: n, column: 0)))
            #expect(outputSet.contains { $0.newContent!.location == codeLocation })
        }
        for n in 0..<106 {
            let codeLocation = CodeLocation(compileUnit: compileUnit, documentPath: "graph-server-lib/Tests/GraphServerLibTests/Actions/CallHierarchyForSymbolTests.swift", locationInFile: .point(try! PointUTF16Z(row: n, column: 0)))
            #expect(outputSet.contains { $0.newContent!.location == codeLocation })
        }
        for n in 107..<113 {
            let codeLocation = CodeLocation(compileUnit: compileUnit, documentPath: "graph-server-macos/Tanagram/TanagramApp.swift", locationInFile: .point(try! PointUTF16Z(row: n, column: 0)))
            #expect(outputSet.contains { $0.newContent!.location == codeLocation })
        }
        
        // Test hunk locations are generated for each file with additions
        // Helper function to create range
        func makeHunkLocation(_ startRow: Int, _ endRow: Int, path: String, side: ParseGitDiff.DiffSide) -> ParseGitDiff.HunkLocation {
            let start = try! PointUTF16Z(row: startRow, column: 0)
            let end = try! PointUTF16Z(row: endRow, column: 0)
            let location = CodeLocation(compileUnit: compileUnit, documentPath: path, locationInFile: .range(start ..< end))
            return ParseGitDiff.HunkLocation(side: side, location: location)
        }
        
            // CallHierarchyForSymbol.swift - new file, lines 0-136
        #expect(result.hunkLocations.contains(makeHunkLocation(0, 136, path: "graph-server-lib/Sources/GraphServerLib/Actions/CallHierarchyForSymbol.swift", side: .new)))
        // CodeLocation.swift - lines 7-9 and 44-48
        #expect(result.hunkLocations.contains(makeHunkLocation(7, 9, path: "graph-server-lib/Sources/GraphServerLib/Models/CodeLocation.swift", side: .new)))
        #expect(result.hunkLocations.contains(makeHunkLocation(44, 48, path: "graph-server-lib/Sources/GraphServerLib/Models/CodeLocation.swift", side: .new)))
        // Database.swift - multiple hunks
        #expect(result.hunkLocations.contains(makeHunkLocation(379, 380, path: "graph-server-lib/Sources/GraphServerLib/Models/Database.swift", side: .new)))
        #expect(result.hunkLocations.contains(makeHunkLocation(388, 390, path: "graph-server-lib/Sources/GraphServerLib/Models/Database.swift", side: .new)))
        #expect(result.hunkLocations.contains(makeHunkLocation(392, 393, path: "graph-server-lib/Sources/GraphServerLib/Models/Database.swift", side: .new)))
        #expect(result.hunkLocations.contains(makeHunkLocation(395, 397, path: "graph-server-lib/Sources/GraphServerLib/Models/Database.swift", side: .new)))
        #expect(result.hunkLocations.contains(makeHunkLocation(398, 402, path: "graph-server-lib/Sources/GraphServerLib/Models/Database.swift", side: .new)))
        // SymbolOccurrence.swift
        #expect(result.hunkLocations.contains(makeHunkLocation(46, 47, path: "graph-server-lib/Sources/GraphServerLib/Models/SymbolOccurrence.swift", side: .new)))
        #expect(result.hunkLocations.contains(makeHunkLocation(85, 89, path: "graph-server-lib/Sources/GraphServerLib/Models/SymbolOccurrence.swift", side: .new)))
        // CallHierarchyForSymbolTests.swift - new file, lines 0-106
        #expect(result.hunkLocations.contains(makeHunkLocation(0, 106, path: "graph-server-lib/Tests/GraphServerLibTests/Actions/CallHierarchyForSymbolTests.swift", side: .new)))
        // TanagramApp.swift
        #expect(result.hunkLocations.contains(makeHunkLocation(107, 113, path: "graph-server-macos/Tanagram/TanagramApp.swift", side: .new)))
    }

    @Test func worksForADeletionDiff() {
        let compileUnit = CompileUnit(rootDir: URL(filePath: "/tmp/"))
        let deletionDiffFileURL = fixtureURL(for: "diff3.txt")
        let result = try! ParseGitDiff(compileUnit: compileUnit, diffFileURL: deletionDiffFileURL).run()
        let outputSet = Set(result.diffLines)

        // Expectations were determined by manually going through the diff file the first time I wrote this code and got it working.
        #expect(result.diffLines.count == 10)

        for n in 139..<141 {
            let codeLocation = CodeLocation(compileUnit: compileUnit, documentPath: "airflow/www/app.py", locationInFile: .point(try! PointUTF16Z(row: n, column: 0)))
            #expect(outputSet.contains { $0.oldContent!.location == codeLocation })
        }
        for n in 171..<179 {
            let codeLocation = CodeLocation(compileUnit: compileUnit, documentPath: "airflow/www/app.py", locationInFile: .point(try! PointUTF16Z(row: n, column: 0)))
            #expect(outputSet.contains { $0.oldContent!.location == codeLocation })
        }
        
        // Test hunk locations are generated for deletion hunks
        func makeHunkLocation(_ startRow: Int, _ endRow: Int, path: String, side: ParseGitDiff.DiffSide) -> ParseGitDiff.HunkLocation {
            let start = try! PointUTF16Z(row: startRow, column: 0)
            let end = try! PointUTF16Z(row: endRow, column: 0)
            let location = CodeLocation(compileUnit: compileUnit, documentPath: path, locationInFile: .range(start ..< end))
            return ParseGitDiff.HunkLocation(side: side, location: location)
        }
        
        // Two deletion hunks in airflow/www/app.py
        #expect(result.hunkLocations.contains(makeHunkLocation(139, 141, path: "airflow/www/app.py", side: .old)))  // Lines 139-140 deleted
        #expect(result.hunkLocations.contains(makeHunkLocation(171, 179, path: "airflow/www/app.py", side: .old)))   // Lines 171-178 deleted
    }

    @Test func worksForAMixedAdditionAndDeletionDiff() {
        let compileUnit = CompileUnit(rootDir: URL(filePath: "/tmp/"))
        let deletionDiffFileURL = fixtureURL(for: "diff4.txt")
        let result = try! ParseGitDiff(compileUnit: compileUnit, diffFileURL: deletionDiffFileURL).run()
        let outputSet = Set(result.diffLines)
        // Expectations were determined by manually going through the diff file the first time I wrote this code and got it working.
        #expect(result.diffLines.count == 4)
        let codeLocation72 = CodeLocation(compileUnit: compileUnit, documentPath: "graph-server-lib/Sources/GraphServerLib/Actions/ParseGitDiff.swift", locationInFile: .point(try! PointUTF16Z(row: 72, column: 0)))
        let oldSide72 = ParseGitDiff.DiffSideLine(
            side: .old,
            location: codeLocation72,
            content: "        let oldDocumentPath = maybeOldDocumentPath!"
        )
        let newSide72 = ParseGitDiff.DiffSideLine(
            side: .new,
            location: codeLocation72,
            content: "        let foobaroldDocumentPath = maybeOldDocumentPath!"
        )
        #expect(outputSet.contains(ParseGitDiff.DiffLine(oldContent: oldSide72, newContent: newSide72)))
        let codeLocation73 = CodeLocation(compileUnit: compileUnit, documentPath: "graph-server-lib/Sources/GraphServerLib/Actions/ParseGitDiff.swift", locationInFile: .point(try! PointUTF16Z(row: 73, column: 0)))
        let oldSide73 = ParseGitDiff.DiffSideLine(
            side: .old,
            location: codeLocation73,
            content: "        let newDocumentPath = maybeNewDocumentPath!"
        )
        let newSide73 = ParseGitDiff.DiffSideLine(
            side: .new,
            location: codeLocation73,
            content: "        x"
        )
        #expect(outputSet.contains(ParseGitDiff.DiffLine(oldContent: oldSide73, newContent: newSide73)))
        let codeLocation74 = CodeLocation(compileUnit: compileUnit, documentPath: "graph-server-lib/Sources/GraphServerLib/Actions/ParseGitDiff.swift", locationInFile: .point(try! PointUTF16Z(row: 74, column: 0)))
        let newSide74 = ParseGitDiff.DiffSideLine(
            side: .new,
            location: codeLocation74,
            content: "        let foobarnewDocumentPath = maybeNewDocumentPath!"
        )
        #expect(outputSet.contains(ParseGitDiff.DiffLine(oldContent: nil, newContent: newSide74)))
        let codeLocation76 = CodeLocation(compileUnit: compileUnit, documentPath: "graph-server-lib/Sources/GraphServerLib/Actions/ParseGitDiff.swift", locationInFile: .point(try! PointUTF16Z(row: 76, column: 0)))
        let newSide76 = ParseGitDiff.DiffSideLine(
            side: .new,
            location: codeLocation76,
            content: "        y"
        )
        #expect(outputSet.contains(ParseGitDiff.DiffLine(oldContent: nil, newContent: newSide76)))
        
        // Test hunk locations are generated for mixed addition/deletion hunk
        func makeHunkLocation(_ startRow: Int, _ endRow: Int, path: String, side: ParseGitDiff.DiffSide) -> ParseGitDiff.HunkLocation {
            let start = try! PointUTF16Z(row: startRow, column: 0)
            let end = try! PointUTF16Z(row: endRow, column: 0)
            let location = CodeLocation(compileUnit: compileUnit, documentPath: path, locationInFile: .range(start ..< end))
            return ParseGitDiff.HunkLocation(side: side, location: location)
        }
        
        // Mixed hunk generates multiple ranges based on the actual diff structure
        #expect(result.hunkLocations.contains(makeHunkLocation(72, 74, path: "graph-server-lib/Sources/GraphServerLib/Actions/ParseGitDiff.swift", side: .old)))  // old side
        #expect(result.hunkLocations.contains(makeHunkLocation(72, 75, path: "graph-server-lib/Sources/GraphServerLib/Actions/ParseGitDiff.swift", side: .new)))  // new side
        #expect(result.hunkLocations.contains(makeHunkLocation(76, 77, path: "graph-server-lib/Sources/GraphServerLib/Actions/ParseGitDiff.swift", side: .new)))  // additional new side
    }

    @Test func worksForAlternatingAdditionsAndDeletions() {
        let compileUnit = CompileUnit(rootDir: URL(filePath: "/tmp/"))
        let alternatingDiffFileURL = fixtureURL(for: "diff5.txt")
        let result = try! ParseGitDiff(compileUnit: compileUnit, diffFileURL: alternatingDiffFileURL).run()
        let outputSet = Set(result.diffLines)

        // Expectations based on the diff5.txt file with alternating additions and deletions
        // The parser produces 10 DiffLine objects, including one empty one
        #expect(result.diffLines.count == 10)

        // Check for the presence of specific line pairs
        // Line 1
        let codeLocation1 = CodeLocation(
            compileUnit: compileUnit,
            documentPath: "example/file.py",
            locationInFile: .point(try! PointUTF16Z(row: 9, column: 0))
        )
        let oldSide1 = ParseGitDiff.DiffSideLine(
            side: .old,
            location: codeLocation1,
            content: "    print(\"line 1\")"
        )
        let newSide1 = ParseGitDiff.DiffSideLine(
            side: .new,
            location: codeLocation1,
            content: "    print(\"new line 1\")"
        )
        #expect(outputSet.contains(ParseGitDiff.DiffLine(oldContent: oldSide1, newContent: newSide1)))

        // Line 2
        let codeLocation2 = CodeLocation(
            compileUnit: compileUnit,
            documentPath: "example/file.py",
            locationInFile: .point(try! PointUTF16Z(row: 10, column: 0))
        )
        let oldSide2 = ParseGitDiff.DiffSideLine(
            side: .old,
            location: codeLocation2,
            content: "    print(\"line 2\")"
        )
        let newSide2 = ParseGitDiff.DiffSideLine(
            side: .new,
            location: codeLocation2,
            content: "    print(\"new line 2\")"
        )
        #expect(outputSet.contains(ParseGitDiff.DiffLine(oldContent: oldSide2, newContent: newSide2)))

        // Line 3
        let codeLocation3 = CodeLocation(
            compileUnit: compileUnit,
            documentPath: "example/file.py",
            locationInFile: .point(try! PointUTF16Z(row: 11, column: 0))
        )
        let oldSide3 = ParseGitDiff.DiffSideLine(
            side: .old,
            location: codeLocation3,
            content: "    print(\"line 3\")"
        )
        let newSide3 = ParseGitDiff.DiffSideLine(
            side: .new,
            location: codeLocation3,
            content: "    print(\"new line 3\")"
        )
        #expect(outputSet.contains(ParseGitDiff.DiffLine(oldContent: oldSide3, newContent: newSide3)))

        // Line 4
        let codeLocation4 = CodeLocation(
            compileUnit: compileUnit,
            documentPath: "example/file.py",
            locationInFile: .point(try! PointUTF16Z(row: 12, column: 0))
        )
        let oldSide4 = ParseGitDiff.DiffSideLine(
            side: .old,
            location: codeLocation4,
            content: "    print(\"line 4\")"
        )
        let newSide4 = ParseGitDiff.DiffSideLine(
            side: .new,
            location: codeLocation4,
            content: "    print(\"new line 4\")"
        )
        #expect(outputSet.contains(ParseGitDiff.DiffLine(oldContent: oldSide4, newContent: newSide4)))

        // Line 5
        let codeLocation5 = CodeLocation(
            compileUnit: compileUnit,
            documentPath: "example/file.py",
            locationInFile: .point(try! PointUTF16Z(row: 13, column: 0))
        )
        let oldSide5 = ParseGitDiff.DiffSideLine(
            side: .old,
            location: codeLocation5,
            content: "    print(\"line 5\")"
        )
        let newSide5 = ParseGitDiff.DiffSideLine(
            side: .new,
            location: codeLocation5,
            content: "    print(\"new line 5\")"
        )
        #expect(outputSet.contains(ParseGitDiff.DiffLine(oldContent: oldSide5, newContent: newSide5)))

        // Also check for the empty DiffLine
        #expect(outputSet.contains(ParseGitDiff.DiffLine(oldContent: nil, newContent: nil)))
    }

    @Test func parsesDiffWithNoNewlineAtEndCorrectly() {
        let compileUnit = CompileUnit(rootDir: URL(filePath: "/tmp/"))
        let noNewlineDiffFileURL = fixtureURL(for: "diff-no-newline-bug.txt")
        let result = try! ParseGitDiff(compileUnit: compileUnit, diffFileURL: noNewlineDiffFileURL).run()
        let outputSet = Set(result.diffLines)

        // This test captures the bug where the `await` line at the end of the file is missing
        // The diff contains 4 additions but the parser should capture all of them
        #expect(result.diffLines.count == 4)

        // Check for the first addition: BALANCE_TRANSFER line
        let balanceTransferLocation = CodeLocation(
            compileUnit: compileUnit,
            documentPath: "app/types/transactionTypes.ts",
            locationInFile: .point(try! PointUTF16Z(row: 9, column: 0))
        )
        let balanceTransferLine = ParseGitDiff.DiffSideLine(
            side: .new,
            location: balanceTransferLocation,
            content: "  BALANCE_TRANSFER = 'BALANCE_TRANSFER'"
        )
        #expect(outputSet.contains(ParseGitDiff.DiffLine(oldContent: nil, newContent: balanceTransferLine)))

        // Check for the empty line addition
        let emptyLineLocation = CodeLocation(
            compileUnit: compileUnit,
            documentPath: "app/types/transactionTypes.ts",
            locationInFile: .point(try! PointUTF16Z(row: 11, column: 0))
        )
        let emptyLine = ParseGitDiff.DiffSideLine(
            side: .new,
            location: emptyLineLocation,
            content: ""
        )
        #expect(outputSet.contains(ParseGitDiff.DiffLine(oldContent: nil, newContent: emptyLine)))

        // Check for the comment line addition
        let commentLocation = CodeLocation(
            compileUnit: compileUnit,
            documentPath: "app/types/transactionTypes.ts",
            locationInFile: .point(try! PointUTF16Z(row: 12, column: 0))
        )
        let commentLine = ParseGitDiff.DiffSideLine(
            side: .new,
            location: commentLocation,
            content: "// Always update, even if deleted, to allow for other systems to consume."
        )
        #expect(outputSet.contains(ParseGitDiff.DiffLine(oldContent: nil, newContent: commentLine)))

        // Check for the await line addition (this is the line that's currently missing due to the bug)
        let awaitLocation = CodeLocation(
            compileUnit: compileUnit,
            documentPath: "app/types/transactionTypes.ts",
            locationInFile: .point(try! PointUTF16Z(row: 13, column: 0))
        )
        let awaitLine = ParseGitDiff.DiffSideLine(
            side: .new,
            location: awaitLocation,
            content: "await Task.update({ id: this.id }, { status: 'CANCELLED' });"
        )
        #expect(outputSet.contains(ParseGitDiff.DiffLine(oldContent: nil, newContent: awaitLine)))
    }
}
