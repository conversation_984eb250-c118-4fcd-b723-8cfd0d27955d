//
//  ScipID.swift
//  graph-server-lib
//
//  Created by <PERSON><PERSON><PERSON> on 5/28/25.
//

// Currently, this is just a wrapper around a String, which is a bit silly.
// Eventually, we'll want to do more with SCIP IDs. Manipulatability is a design goal;
// you should be able to determine if a symbol is lexically contained in another
// (e.g. whether a property is inside a class) by comparing SCIP IDs.
// We might want to add this functionality later, so we'll create a type for it now.
// ScipID is `ExpressibleByStringLiteral`, so you can just write a string literal anywhere you need this type.
public struct ScipID: Equatable, Hashable, ExpressibleByStringLiteral, LosslessStringConvertible, RawRepresentable {
    public typealias RawValue = String
    
    public let idString: String
    
    public init?(_ idAsString: String) {
        // TODO: Check if valid
        self.idString = idAsString
    }
    
    public init?(rawValue: Self.RawValue) {
        // TODO: Check if valid
        self.idString = rawValue
    }
    
    public init(stringLiteral value: String) {
        self.idString = value
    }
    
    public var description: String {
        return idString
    }
    
    public var rawValue: String {
        return idString
    }
}

extension ScipID {
    /// Returns true if this ScipID contains SQLite wildcard characters
    public var hasWildcards: Bool {
        return idString.contains("%") || idString.contains("_")
    }
    
    /// Creates a version-agnostic ScipID using SQLite wildcards
    /// Example: ScipID.withVersionWildcard("scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#findBy().")
    public static func withVersionWildcard(_ pattern: String) -> ScipID {
        return ScipID(stringLiteral: pattern)
    }
}
