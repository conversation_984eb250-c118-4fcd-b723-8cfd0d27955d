//
//  Database.swift
//  Tanagram
//
//  Created by <PERSON><PERSON><PERSON> on 10/23/24.
//

import Foundation
import OrderedCollections
import SQLite

public actor Database {
    public struct Relationships: Sendable {
        public let from: [SymbolRelationship]
        public let to: [SymbolRelationship]
    }

    // MARK: - SQLite plumbing

    private static var _shared: Database?
    private let connection: Connection

    public static func shared() -> Database {
        if _shared != nil { return _shared! }
        if isRunningInTestEnvironment() {
            let db = Database.inMemory()
            _shared = db
            return db
        } else {
            let db = Database.fromFile(self.databaseOnDiskURL())
            _shared = db
            return db
        }
    }

    public static func inMemory() -> Database {
        let connection = try! Connection()
        Migrations.runAll(db: connection)
        return Database(connection: connection)
    }

    public static func fromFile(_ fileURL: URL) -> Database {
        Database(connection: try! Connection(fileURL.path))
    }

    nonisolated public static func databaseOnDiskURL() -> URL {
        // Create the "Application Support/Database" directory if needed
        let fileManager = FileManager.default
        let appSupportURL = try! fileManager.url(
            for: .applicationSupportDirectory, in: .userDomainMask,
            appropriateFor: nil, create: true)
        let directoryURL = appSupportURL.appendingPathComponent("Tanagram", isDirectory: true)
        try! fileManager.createDirectory(at: directoryURL, withIntermediateDirectories: true)
        let databaseURL = directoryURL.appendingPathComponent("db.sqlite")
        return databaseURL
    }

    private static func isRunningInTestEnvironment() -> Bool {
        ProcessInfo.processInfo.environment["XCTestSessionIdentifier"] != nil
    }

    init(connection: Connection) {
        self.connection = connection
        Migrations.runAll(db: connection)
    }

    public func reset() {
//        try! self.dbWriter.write { db in
//            try! SymbolRelationship.deleteAll(db)
//            try! SymbolOccurrence.deleteAll(db)
//            try! SymbolInformation.deleteAll(db)
//            try! CompileUnit.deleteAll(db)
//        }
        // TODO: Implement
    }

    // MARK: - Inserting

    public func add(compileUnit: CompileUnit) {
        do {
            try self.connection.run(
                CompileUnit.dbTable().insert(compileUnit.setters())
            )
        } catch let e as SQLite.Result {
            if case let .error(message, _, _) = e {
                if message.hasPrefix("UNIQUE constraint failed") {
                    return
                } else {
                    print("add(compileUnit:) unexpected SQLite.Result error: \(e)")
                }
            }
        } catch {
            print("add(compileUnit:) error: \(error)")
        }
    }

    public func add(symbol: SymbolInformation, definedInCompileUnit compileUnit: CompileUnit) {
        self.add(compileUnit: compileUnit)
        let maybeExistingSymbol = try? SymbolInformation.find(connection, id: symbol.id)
        if maybeExistingSymbol != nil {
            if !symbol.occurrences().isEmpty {
                // If a symbol already exists, just union occurrences
                // (I'm not entirely sure "union" is correct; maybe we will eventually want to delete existing occurrences?)
                // If we do an `insertMany` here, it'll raise an exception if any of the occurrences already exist.
                // To implement union semantics, we'll have to iterate on each occurrence, try inserting it,
                // and no-op if we get a unique constraint error.
                // Also we should maybe try wrapping this whole thing in a transaction?
                // TODO: Dedup this unique constraint handling with add(compileUnit:)
                for occurrence in symbol.occurrences() {
                    do {
                        try self.connection.run(SymbolOccurrence.dbTable().insert(occurrence.setters()))
                    } catch let e as SQLite.Result {
                        if case let .error(message, _, _) = e {
                            if message.hasPrefix("UNIQUE constraint failed") {
                                continue // Skip duplicates
                            } else {
                                print("add(symbol:definedInCompileUnit:) unexpected SQLite.Result error: \(e)")
                            }
                        }
                    } catch {
                        print("add(symbol:definedInCompileUnit:) error: \(error)")
                    }
                }
            }
        } else {
            // If the symbol doesn't exist, insert it and its occurrences
            do {
                try self.connection.transaction {
                    try self.connection.run(SymbolInformation.dbTable().insert(symbol.setters()))
                    if !symbol.occurrences().isEmpty {
                        try self.connection.run(
                            SymbolOccurrence.dbTable().insertMany(
                                symbol.occurrences().map { $0.setters() }
                            )
                        )
                    }
                }
            } catch let e as SQLite.Result {
                if case let .error(message, _, _) = e {
                    if !message.hasPrefix("UNIQUE constraint failed") {
                        print("add(symbol:) unexpected error: \(e)")
                    }
                }
            } catch {
                print("add(symbol:) error: \(error)")
            }
        }
    }

    public func add(symbols: [SymbolInformation], definedInCompileUnit compileUnit: CompileUnit) {
        add(compileUnit: compileUnit)
        do {
            // Insert all symbols, and then all occurrences
            let symbolSetters = symbols.map { $0.setters() }
            // Do in chunks because if we try inserting the entire array at once,
            // we might run into a "Too many SQL variables" error from SQLite.
            try symbolSetters.doInChunks(ofSize: 20000) {
                try self.connection.run(SymbolInformation.dbTable().insertMany(or: .replace, $0))
            }
            let occurrenceSetters = symbols.flatMap { $0.occurrences().map { $0.setters() }}
            try occurrenceSetters.doInChunks(ofSize: 20000) {
                try self.connection.run(SymbolOccurrence.dbTable().insertMany(or: .replace, $0))
            }
        } catch let e as SQLite.Result {
            if case let .error(message, _, _) = e {
                if message.hasPrefix("UNIQUE constraint failed") {
                    // Do nothing
                } else {
                    TLog.default().info("add(symbols:definedInCompileUnit:) unexpected SQLite.Result error: \(e)")
                }
            }
        } catch {
            TLog.default().info("add(symbols:definedInCompileUnit:) unanticipated error: \(error)")
        }
    }
    
    public func add(occurrences: [SymbolOccurrence], definedInCompileUnit compileUnit: CompileUnit) {
        add(compileUnit: compileUnit)
        do {
            let occurrenceSetters = occurrences.map { $0.setters() }
            // Do in chunks because if we try inserting the entire array at once,
            // we might run into a "Too many SQL variables" error from SQLite.
            try occurrenceSetters.doInChunks(ofSize: 20000) {
                try self.connection.run(SymbolOccurrence.dbTable().insertMany(or: .replace, $0))
            }
        } catch let e as SQLite.Result {
            if case let .error(message, _, _) = e {
                if message.hasPrefix("UNIQUE constraint failed") {
                    // Do nothing
                } else {
                    TLog.default().info("add(occurrences:definedInCompileUnit:) unexpected SQLite.Result error: \(e)")
                }
            }
        } catch {
            TLog.default().info("add(occurrences:definedInCompileUnit:) unanticipated error: \(error)")
        }
    }
    
    public func add(relationships: [SymbolRelationship]) {
        if !relationships.isEmpty {
            do {
                try self.connection.run(
                    SymbolRelationship.dbTable().insertMany(
                        or: .ignore, relationships.map { $0.setters() }
                    )
                )
            } catch {
                print("add(relationships:) error: \(error)")
            }
        }
    }

    public func add(relationship: SymbolRelationship) {
        do {
            try self.connection.run(
                SymbolRelationship.dbTable().insert(or: .ignore, relationship.setters())
            )
        } catch {
            print("add(relationship:) error: \(error)")
        }
    }

    public func importSymbols(fromSCIPIndex index: SCIPIndex, forCompileUnit compileUnit: CompileUnit, ignoringLocals: Bool) {
        var stopwatch = Stopwatch(name: "Import Symbols")
        stopwatch.start()
        self.add(compileUnit: compileUnit)
        stopwatch.mark(label: "Add compile unit")

        var tempSymbols: [SymbolInformation.ID:SymbolInformation] = [:]
        var tempRelationships: [SymbolRelationship] = []
        var tempOccurrences: [SymbolOccurrence] = []

        for document in index.documents {
            for documentSymbol in document.symbols {
                if ignoringLocals && documentSymbol.symbol.starts(with: "local") { continue }
                var symbolType: String? = nil
                if let typeDocumentationString = documentSymbol.documentation.first(where: { $0.hasPrefix("~type:") }) {
                    symbolType = String(typeDocumentationString.trimmingPrefix("~type:"))
                }
                let newSymbol = SymbolInformation(
                    id: documentSymbol.symbol,
                    kind: documentSymbol.kind,
                    displayName: documentSymbol.displayName,
                    occurrences: [],
                    typeString: symbolType
                )
                tempSymbols[newSymbol.id] = newSymbol

                for relationship in documentSymbol.relationships {
                    let roles = SymbolRelationship.rolesFrom(relationship: relationship)
                    let relationship = SymbolRelationship(
                        from: documentSymbol.symbol,
                        to: relationship.symbol,
                        roles: roles,
                        referenceKind: relationship.isReference ? .unspecifiedKind : nil
                    )
                    tempRelationships.append(relationship)
                }
            }

            for documentOccurrence in document.occurrences {
                // TODO: This implicitly assumes the document is indexed using UTF-16 encoding. Make sure this is actually the case.
                let maybeRange = try? Range<PointUTF16Z>.fromUTF16SCIPRange(documentOccurrence.range)
                let maybeEnclosingRange = try? Range<PointUTF16Z>.fromUTF16SCIPRange(documentOccurrence.enclosingRange)
                if maybeRange == nil {
                    // Somehow the range specified in the SCIP index was invalid
                    // If so, ignore this occurrence
                    continue
                }

                let occurrence = SymbolOccurrence(
                    symbolInformationId: documentOccurrence.symbol,
                    compileUnit: compileUnit,
                    document: document.relativePath,
                    range: maybeRange!,
                    enclosingRange: maybeEnclosingRange,
                    roles: .init(rawValue: Int(documentOccurrence.symbolRoles))
                )
                tempOccurrences.append(occurrence)
            }
        }
        stopwatch.mark(label: "Loop over docs")

        do {
            try self.connection.transaction {
                self.add(symbols: Array<SymbolInformation>(tempSymbols.values), definedInCompileUnit: compileUnit)
                stopwatch.mark(label: "Add symbols")
                self.add(occurrences: tempOccurrences, definedInCompileUnit: compileUnit)
                stopwatch.mark(label: "Add occurrences")
                self.add(relationships: tempRelationships)
                stopwatch.mark(label: "Add relationships")
            }
        } catch {
            TLog.default().info("Uncaught error in trying a database connection to insert symbols, occurrences, and relationships: \(error)")
        }
        stopwatch.end()
        print(stopwatch.summary())
    }

    // MARK: - Updating

    // Returns whether any rows were updated
    public func updateEnclosingRangeArrayOnSymbolOccurrenceMatching(
        symbolInformationID: SymbolInformation.ID,
        compileUnit: CompileUnit,
        document: String,
        rangeArray: [Int],
        newEnclosingRangeArray: [Int]?
    ) -> Bool {
        let query = SymbolOccurrence.dbTable()
            .filter(SymbolOccurrence.symbolInformationIDColumn() == symbolInformationID)
            .filter(SymbolOccurrence.compileUnitRootDirColumn() == compileUnit.rootDir.absoluteString)
            .filter(SymbolOccurrence.documentColumn() == document)
            .filter(SymbolOccurrence.rangeArrayColumn() == SymbolOccurrence.encodeArrayJSON(rangeArray))
        let updatedCount = try! self.connection.run(
            query.update(
                SymbolOccurrence.enclosingRangeArrayColumn() <- SymbolOccurrence.encodeArrayJSON(newEnclosingRangeArray)
            )
        )
        return updatedCount > 0
    }

    // MARK: - Querying
    private func hydrateOccurrences(_ symbol: SymbolInformation?) -> SymbolInformation? {
        guard symbol != nil else { return nil }
        let nonNilSymbol = symbol!
        let occurences = self.occurrences(forSymbolID: nonNilSymbol.id)
        return SymbolInformation(
            id: nonNilSymbol.id,
            kind: nonNilSymbol.kind,
            displayName: nonNilSymbol.displayName,
            occurrences: occurences,
            typeString: nonNilSymbol.typeString
        )
    }

    private func hydrateOccurrences(_ symbols: [SymbolInformation]) -> [SymbolInformation] {
        symbols.map { hydrateOccurrences($0)! }
    }

    public func symbol(withID symbolID: SymbolInformation.ID) -> SymbolInformation? {
        hydrateOccurrences(
            try! SymbolInformation.find(self.connection, id: symbolID)
        )
    }

    public func symbols(withIDs symbolIDs: [SymbolInformation.ID]) -> [SymbolInformation] {
        hydrateOccurrences(
            try! SymbolInformation.findAll(self.connection, ids: symbolIDs)
        )
    }

    public func relationshipsInvolving(symbolID: String) -> Relationships {
        let fromQuery = SymbolRelationship.dbTable()
            .filter(SymbolRelationship.fromIDColumn() == symbolID)
        let from = try! self.connection
            .prepareRowIterator(fromQuery)
            .map { SymbolRelationship(fromDBRow: $0) }
        let toQuery = SymbolRelationship.dbTable()
            .filter(SymbolRelationship.toIDColumn() == symbolID)
        let to = try! self.connection
            .prepareRowIterator(toQuery)
            .map { SymbolRelationship(fromDBRow: $0) }
        return .init(from: from, to: to)
    }

    public func relationshipsFrom(symbolID: String) -> [SymbolRelationship] {
        let fromQuery = SymbolRelationship.dbTable()
            .filter(SymbolRelationship.fromIDColumn() == symbolID)
        let from = try! self.connection
            .prepareRowIterator(fromQuery)
            .map { SymbolRelationship(fromDBRow: $0) }
        return from
    }

    public func symbolsFrom(compileUnit: CompileUnit, filterIDPrefix idPrefix: String? = nil) -> [SymbolInformation] {
        var query = SymbolOccurrence.dbTable()
            .filter(SymbolOccurrence.compileUnitRootDirColumn() == compileUnit.id.absoluteString)
        if idPrefix != nil {
            query = query.filter(SymbolOccurrence.symbolInformationIDColumn().like("\(idPrefix!)%"))
        }
        let definedSymbols = try! self.connection
            .prepareRowIterator(query)
            .map { SymbolOccurrence(fromDBRow: $0) }
            // TODO: Filter in database?
            .filter { $0.roles.contains(.definition) }
            .map(\.symbolInformationId)
        let symbols = try! SymbolInformation.findAll(self.connection, ids: definedSymbols)
        return hydrateOccurrences(symbols)
    }

    public func anySymbolsExist(fromCompileUnit compileUnit: CompileUnit) -> Bool {
        let occurrenceQuery = SymbolOccurrence.dbTable()
            .filter(SymbolOccurrence.compileUnitRootDirColumn() == compileUnit.id.absoluteString)
        if let occurrence = try! self.connection.pluck(occurrenceQuery) {
            let symbolInformationID = occurrence[SymbolOccurrence.symbolInformationIDColumn()]
            let symbolQuery = SymbolInformation.dbTable()
                .filter(SymbolInformation.idColumn() == symbolInformationID)
            if let _ = try! self.connection.pluck(symbolQuery) {
                return true
            }
        }
        return false
    }

    public func symbolsPer(compileUnits: [CompileUnit]) -> [CompileUnit:[SymbolInformation]] {
        var collection: [CompileUnit:[SymbolInformation]] = [:]
        for compileUnit in compileUnits {
            collection[compileUnit] = self.symbolsFrom(compileUnit: compileUnit)
        }
        return collection
    }

    public func symbolsFrom(document: String, inCompileUnit compileUnit: CompileUnit) -> [SymbolInformation] {
        let symbolOccurrencesQuery = SymbolOccurrence.dbTable()
            .filter(SymbolOccurrence.compileUnitRootDirColumn() == compileUnit.id.absoluteString)
            .filter(SymbolOccurrence.documentColumn() == document)
        let symbolOccurrences = try! self.connection
            .prepareRowIterator(symbolOccurrencesQuery)
            .map { SymbolOccurrence(fromDBRow: $0) }
        let definedSymbols = symbolOccurrences
            // TODO: Filter in database?
            .filter { $0.roles.contains(.definition) }
            .map { $0.symbolInformationId }
        let symbols = try! SymbolInformation.findAll(self.connection, ids: definedSymbols)
        return hydrateOccurrences(symbols)
    }

    public func allCompileUnits() -> [CompileUnit] {
        return try! self.connection
            .prepareRowIterator(CompileUnit.dbTable())
            .map { CompileUnit(fromDBRow: $0) }
    }

    public func compileUnit(withRootDir rootDir: URL) -> CompileUnit? {
        return try! CompileUnit.find(self.connection, rootDir: rootDir)
    }

    public func compileUnit(fromAbsoluteFileURL fileURL: URL) -> CompileUnit? {
        self.allCompileUnits().first(where: { $0.contains(absoluteFileURL: fileURL) })
    }

    public func symbolsFrom(absoluteFileURL fileURL: URL) -> [SymbolInformation] {
        guard let matchingCompileUnit = self.compileUnit(fromAbsoluteFileURL: fileURL) else { return [] }
        let documentPath = matchingCompileUnit.documentPath(fromAbsoluteFileURL: fileURL)
        return self.symbolsFrom(document: documentPath, inCompileUnit: matchingCompileUnit)
    }

    // TODO: This is slow, especially when it gets to symbolsFrom(document: String, inCompileUnit compileUnit: CompileUnit)
    public func symbolsEnclosing(absoluteFileURL fileURL: URL, position: PointUTF16Z) -> [SymbolInformation] {
        guard let compileUnit = self.compileUnit(fromAbsoluteFileURL: fileURL) else { return [] }
        let symbolsInDocument = self.symbolsFrom(absoluteFileURL: fileURL)
        return symbolsInDocument.filter { symbol in
            symbol.definitionOccurrence(inCompileUnit: compileUnit)?.encloses(position: position) ?? false
        }
    }

    // Filter by `kinds` if specified; if not specified, then no filter by kind/all kinds are allowed in return value
    public func tightestSymbolEnclosing(occurrence: SymbolOccurrence, ofKinds kinds: [SCIPSymbolInformationKind]? = nil) -> SymbolInformation? {
        let absoluteFileURL = occurrence.absoluteFileURL()
        let position = occurrence.range.lowerBound
        return tightestSymbolEnclosing(absoluteFileURL: absoluteFileURL, position: position, ofKinds: kinds)
    }

    // Filter by `kinds` if specified; if not specified, then no filter by kind/all kinds are allowed in return value
    public func tightestSymbolEnclosing(absoluteFileURL fileURL: URL, position: PointUTF16Z, ofKinds kinds: [SCIPSymbolInformationKind]? = nil) -> SymbolInformation? {
        guard let compileUnit = self.compileUnit(fromAbsoluteFileURL: fileURL) else { return nil }
        var candidates = self.symbolsEnclosing(absoluteFileURL: fileURL, position: position)
        if let nonNilKinds = kinds, nonNilKinds.count > 0 {
            candidates = candidates.filter { nonNilKinds.contains($0.kind) }
        }
        return candidates.min { symbol1, symbol2 in
            let symbol1Range = symbol1.definitionOccurrence(inCompileUnit: compileUnit)!.enclosingRange ?? symbol1.definitionOccurrence(inCompileUnit: compileUnit)!.range
            let symbol2Range = symbol2.definitionOccurrence(inCompileUnit: compileUnit)!.enclosingRange ?? symbol2.definitionOccurrence(inCompileUnit: compileUnit)!.range
            // candidates from above are supposed to definitely enclose `position`
            return symbol1Range.looseness(around: position)! < symbol2Range.looseness(around: position)!
        }
    }

    public func occurrences(
        forSymbolID symbolID: SymbolInformation.ID,
        filterDocumentPrefix documentPrefix: String? = nil,
        filterExcludeRole excludeRole: SymbolOccurrence.Role? = nil
    ) -> [SymbolOccurrence] {
        var query = SymbolOccurrence.dbTable()
            .filter(SymbolOccurrence.symbolInformationIDColumn() == symbolID)
        if documentPrefix != nil {
            query = query.filter(SymbolOccurrence.documentColumn().like("\(documentPrefix!)%"))
        }
        let occurrences = try! self.connection.prepareRowIterator(query).map { SymbolOccurrence(fromDBRow: $0) }
        if excludeRole == nil { return occurrences }
        else { return occurrences.filter { !$0.roles.contains(excludeRole!) } }
    }

    /// Find symbol occurrences matching a pattern using SQLite LIKE
    public func occurrences(
        forSymbolIDPattern pattern: String,
        filterDocumentPrefix documentPrefix: String? = nil,
        filterExcludeRole excludeRole: SymbolOccurrence.Role? = nil
    ) -> [SymbolOccurrence] {
        var query = SymbolOccurrence.dbTable()
            .filter(SymbolOccurrence.symbolInformationIDColumn().like(pattern))
        
        if let documentPrefix = documentPrefix {
            query = query.filter(SymbolOccurrence.documentColumn().like("\(documentPrefix)%"))
        }
        
        let occurrences = try! self.connection.prepareRowIterator(query).map { SymbolOccurrence(fromDBRow: $0) }
        
        if let excludeRole = excludeRole {
            return occurrences.filter { !$0.roles.contains(excludeRole) }
        } else {
            return occurrences
        }
    }

    public func occurrenceAt(absoluteFileURL fileURL: URL, position: PointUTF16Z) -> SymbolOccurrence? {
        guard let compileUnit = self.compileUnit(fromAbsoluteFileURL: fileURL) else { return nil }
        let documentPath = compileUnit.documentPath(fromAbsoluteFileURL: fileURL)

        let query = SymbolOccurrence.dbTable()
            .filter(SymbolOccurrence.compileUnitRootDirColumn() == compileUnit.id.absoluteString)
            .filter(SymbolOccurrence.documentColumn() == documentPath)

        return try! self.connection
            .prepareRowIterator(query)
            .map { SymbolOccurrence(fromDBRow: $0) }
            .first { occurrence in
                // `contains` assumes the range is half-open, so `contains` will return false
                // if `position == range.upperBound`. However, when we have a tree-sitter node
                // and want to find a corresponding Occurrence, the tree-sitter node may span
                // more code than a single Occurrence (e.g. in case of chained method calls).
                // In that case, we may use the `upperBound` of that tree-sitter node
                // (instead of trying to do "one less than upperBound", which can get tricky
                // around line boundaries), and we want to find a matching Occurrence.
                occurrence.range.contains(position) || occurrence.range.upperBound == position
            }
    }


}



// MARK: - Persistable models

extension CompileUnit {
    static func dbTable() -> Table {
        Table("compileUnit")
    }

    static func rootDirColumn() -> SQLite.Expression<String> {
        SQLite.Expression<String>("rootDir")
    }

    func setters() -> [SQLite.Setter] {
        [
            Self.rootDirColumn() <- self.rootDir.absoluteString,
        ]
    }

    internal init(fromDBRow row: Row) {
        self.rootDir = URL(string: row[Self.rootDirColumn()])!
    }

    static func find(_ connection: Connection, rootDir: URL) throws -> Self? {
        let query = Self.dbTable()
            .filter(Self.rootDirColumn() == rootDir.absoluteString)
            .limit(1)
        let rowIterator = try connection.prepareRowIterator(query)
        if let row = try rowIterator.failableNext() {
            return Self(fromDBRow: row)
        } else {
            return nil
        }
    }
}

extension SymbolInformation {
    static func dbTable() -> Table {
        Table("symbolInformation")
    }

    static func idColumn() -> SQLite.Expression<String> {
        SQLite.Expression<String>("id")
    }

    static func kindColumn() -> SQLite.Expression<Int> {
        SQLite.Expression<Int>("kind")
    }

    static func displayNameColumn() -> SQLite.Expression<String> {
        SQLite.Expression<String>("displayName")
    }

    static func typeStringColumn() -> SQLite.Expression<String?> {
        SQLite.Expression<String?>("typeString")
    }

    func setters() -> [SQLite.Setter] {
        [
            Self.idColumn() <- self.id,
            Self.kindColumn() <- self.kind.rawValue,
            Self.displayNameColumn() <- self.displayName,
            Self.typeStringColumn() <- self.typeString
        ]
    }

    internal init(fromDBRow row: Row) {
        self.id = row[Self.idColumn()]
        self.kind = SCIPSymbolInformationKind(rawValue: row[Self.kindColumn()])!
        self.displayName = row[Self.displayNameColumn()]
        self.typeString = row[Self.typeStringColumn()]
    }

    static func find(_ connection: Connection, id: String) throws -> Self? {
        let query = Self.dbTable()
            .filter(Self.idColumn() == id)
            .limit(1)
        let rowIterator = try connection.prepareRowIterator(query)
        if let row = try rowIterator.failableNext() {
            return Self(fromDBRow: row)
        } else {
            return nil
        }
    }

    static func findAll(_ connection: Connection, ids: [String]) throws -> [Self] {
        let query = Self.dbTable()
            .filter(ids.contains(Self.idColumn()))
        let rowIterator = try connection.prepareRowIterator(query)
        return try rowIterator.map { Self(fromDBRow: $0) }
    }
}

extension SymbolOccurrence {
    static func dbTable() -> Table {
        Table("symbolOccurrence")
    }

    static func symbolInformationIDColumn() -> SQLite.Expression<String> {
        SQLite.Expression<String>("symbolInformationId")
    }

    static func compileUnitRootDirColumn() -> SQLite.Expression<String> {
        SQLite.Expression<String>("compileUnitRootDir")
    }

    static func documentColumn() -> SQLite.Expression<String> {
        SQLite.Expression<String>("document")
    }

    static func rangeArrayColumn() -> SQLite.Expression<String> {
        SQLite.Expression<String>("rangeArray")
    }

    static func enclosingRangeArrayColumn() -> SQLite.Expression<String?> {
        SQLite.Expression<String?>("enclosingRangeArray")
    }

    static func rolesColumn() -> SQLite.Expression<Int> {
        SQLite.Expression<Int>("roles")
    }

    func setters() -> [SQLite.Setter] {
        return [
            Self.symbolInformationIDColumn() <- self.symbolInformationId,
            Self.compileUnitRootDirColumn() <- self.compileUnitRootDir.absoluteString,
            Self.documentColumn() <- self.document,
            Self.rangeArrayColumn() <- Self.encodeArrayJSON(self.rangeArray)!,
            Self.enclosingRangeArrayColumn() <- Self.encodeArrayJSON(self.enclosingRangeArray),
            Self.rolesColumn() <- self.roles.rawValue
        ]
    }

    static internal func encodeArrayJSON(_ array: [Int]) -> String {
        let encoder = JSONEncoder()
        let data = try! encoder.encode(array)
        return String(data: data, encoding: .utf8)!
    }

    static internal func encodeArrayJSON(_ array: [Int]?) -> String? {
        if let array = array {
            return encodeArrayJSON(array)
        } else {
            return nil
        }
    }

    static private func decodeArrayJSON(_ jsonString: String?) -> [Int]? {
        if let jsonString = jsonString {
            let decoder = JSONDecoder()
            return try! decoder.decode([Int].self, from: jsonString.data(using: .utf8)!)
        } else {
            return nil
        }
    }

    internal init(fromDBRow row: Row) {
        self.symbolInformationId = row[Self.symbolInformationIDColumn()]
        self.compileUnitRootDir = URL(string: row[Self.compileUnitRootDirColumn()])!
        self.document = row[Self.documentColumn()]
        self.rangeArray = Self.decodeArrayJSON(row[Self.rangeArrayColumn()])!
        self.enclosingRangeArray = Self.decodeArrayJSON(row[Self.enclosingRangeArrayColumn()])
        self.roles = SymbolOccurrence.Role(rawValue: row[Self.rolesColumn()])
    }
}

extension SymbolRelationship {
    static func dbTable() -> Table {
        Table("symbolRelationship")
    }

    static func fromIDColumn() -> SQLite.Expression<String> {
        SQLite.Expression<String>("fromId")
    }

    static func toIDColumn() -> SQLite.Expression<String> {
        SQLite.Expression<String>("toId")
    }

    static func rolesColumn() -> SQLite.Expression<Int> {
        SQLite.Expression<Int>("roles")
    }

    static func referenceKindColumn() -> SQLite.Expression<String?> {
        SQLite.Expression<String?>("referenceKind")
    }

    func setters() -> [SQLite.Setter] {
        [
            Self.fromIDColumn() <- self.from,
            Self.toIDColumn() <- self.to,
            Self.rolesColumn() <- self.roles.rawValue,
            Self.referenceKindColumn() <- self.referenceKind?.rawValue
        ]
    }

    internal init(fromDBRow row: Row) {
        self.fromId = row[Self.fromIDColumn()]
        self.toId = row[Self.toIDColumn()]
        self.roles = SymbolRelationship.Role(rawValue: row[Self.rolesColumn()])
        if let referenceKindRawValue = row[Self.referenceKindColumn()] {
            self.referenceKind = SymbolRelationship.ReferenceKind(rawValue: referenceKindRawValue)
        } else {
            self.referenceKind = nil
        }
    }
}
