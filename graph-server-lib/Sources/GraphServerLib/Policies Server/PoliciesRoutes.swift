//
//  PoliciesRoutes.swift
//  graph-server-lib
//
//  Created by <PERSON><PERSON><PERSON> on 3/3/25.
//

import Foundation
import HTTPTypes
import Hummingbird

public protocol JSONWrapper: Codable {
    static func fromJSONData(_ data: Data) throws -> Self
    func toJSONData() throws -> Data
}

extension JSONWrapper {
    static func fromJSONData(_ data: Data) throws -> Self {
        let decoder = JSONDecoder()
        return try decoder.decode(Self.self, from: data)
    }
    
    func toJSONData() throws -> Data {
        return try JSONEncoder().encode(self)
    }
}

struct PoliciesRoutes {
    func addRoutes(to group: RouterGroup<some RequestContext>) {
        group.get("/policies/ping", use: self.ping)
        group.post("/policies/openCompileUnit", use: self.openCompileUnit)
        group.post("/policies/compileUnitAnalyzeDiff", use: self.compileUnitAnalyzeDiff)
        group.post("/policies/rules/runRule", use: self.runRule)
        group.get("/policies/utils/nearbyComments", use: self.nearbyComments)
    }

    @Sendable private func ping(_ request: Request, context: some RequestContext) async throws -> String {
        return "Policies Pong"
    }
    
    struct OpenCompileUnitRequest: JSONWrapper {
        let absolutePath: String
        let importSymbols: Bool
    }
     
    struct OpenCompileUnitResponse: JSONWrapper {
        let success: Bool
        let error: String?
    }
    
    @Sendable private func openCompileUnit(_ request: Request, context: some RequestContext) async throws -> Response {
        let payload = try await request.parseJSON(to: OpenCompileUnitRequest.self)
        context.logger.info("Attempting to open compile unit, path: \(payload.absolutePath)")
        let url = URL(fileURLWithPath: payload.absolutePath, isDirectory: true)
        let result = await OpenCompileUnits().run(compileUnitURLs: [url], openSCIPIndex: payload.importSymbols, ignoringLocals: false)
        if let error = result.errors?[url] {
            let errorAsString = String(describing: error)
            let response = OpenCompileUnitResponse(success: false, error: errorAsString)
            var status: HTTPResponse.Status = .ok
            switch error {
            case OpenCompileUnits.OpenCompileUnitError.compileUnitAlreadyOpen:
                // This isn't actually an error from the perspective of this HTTP endpoint's caller
                status = .ok
                context.logger.info("compileUnitAlreadyOpen (which is fine; this is idempotent)")
            default:
                status = .badRequest
                context.logger.info("Error attempting to open compile unit: \(errorAsString)")
            }
            return try Response.withJSON(response, status: status)
        } else {
            context.logger.info("Successfully opened compile unit, path: \(payload.absolutePath)")
            let response = OpenCompileUnitResponse(success: true, error: nil)
            return try Response.withJSON(response)
        }
    }
    
    struct CompileUnitAnalyzeDiffRequest: JSONWrapper {
        let compileUnitRootDirPath: String
        let diffFilePath: String
    }
    
    struct CompileUnitAnalyzeDiffResponse: JSONWrapper {
        let error: String?
        let affectedAPIEndpoints: [SymbolInformation]
    }
    
    @Sendable private func compileUnitAnalyzeDiff(_ request: Request, context: some RequestContext) async throws -> Response {
        let payload = try await request.parseJSON(to: CompileUnitAnalyzeDiffRequest.self)
        let url = URL(fileURLWithPath: payload.compileUnitRootDirPath, isDirectory: true)
        context.logger.info("Attempting to find existing compile unit, path: \(payload.compileUnitRootDirPath)")
        guard let compileUnit = await Database.shared().compileUnit(withRootDir: url) else {
            context.logger.info("No already-open compile unit, path: \(payload.compileUnitRootDirPath)")
            let response = CompileUnitAnalyzeDiffResponse(
                error: "no open compile unit matching root dir",
                affectedAPIEndpoints: []
            )
            return try Response.withJSON(response, status: .badRequest)
        }
        guard FileManager.default.fileExists(atPath: payload.diffFilePath, isDirectory: nil) else {
            context.logger.info("File doesn't exist at provided diff path: \(payload.diffFilePath)")
            let response = CompileUnitAnalyzeDiffResponse(
                error: "no such file at provided `diffFilePath`",
                affectedAPIEndpoints: []
            )
            return try Response.withJSON(response, status: .badRequest)
        }
        
        let diffURL = URL(fileURLWithPath: payload.diffFilePath)
        context.logger.info("Analyzing diff, path: \(payload.diffFilePath)")
        let affectedSymbols = try await CompileUnitDiffAnalyzer(compileUnit).analyzeDiff(diffURL)
        context.logger.info("Affected symbols: \(affectedSymbols.map { $0.id }.joined(separator: "\n"))")
        let response = CompileUnitAnalyzeDiffResponse(error: nil, affectedAPIEndpoints: affectedSymbols)
        return try Response.withJSON(response)
    }
    
    struct RunRuleRequest: JSONWrapper {
        let ruleName: String
        let compileUnitRootDirPath: String
        let diffFilePath: String
        // Default to `false` for efficiency
        // (no need to send all thet payload across the network if it's not needed)
        var returnLineContent: Bool = false
    }
    
    struct RuleMatchLocation: JSONWrapper {
        let side: ParseGitDiff.DiffSide
        let filePath: String
        let zeroBasedLine: Int
        let lineContent: String?
        
        static func fromDiffLine(_ diffLine: ParseGitDiff.DiffSideLine, includeContent: Bool) -> Self {
            let lineNumber = switch diffLine.location.locationInFile {
            case .point(let point):
                Int(point.row)
            case .range(let range):
                Int(range.lowerBound.row)
            }
            return Self(
                side: diffLine.side,
                filePath: diffLine.location.documentPath,
                zeroBasedLine: lineNumber,
                lineContent: includeContent ? String(diffLine.content) : nil
            )
        }
    }
    
    struct RunRuleResponse: JSONWrapper {
        let error: String?
        let matchLocations: [RuleMatchLocation]
    }
    
    @Sendable private func runRule(_ request: Request, context: some RequestContext) async throws -> Response {
        do {
            let payload = try await request.parseJSON(to: RunRuleRequest.self)
            context.logger.info("Running rule with name: \(payload.ruleName)")
            let url = URL(fileURLWithPath: payload.compileUnitRootDirPath, isDirectory: true)
            context.logger.info("Attempting to find existing compile unit, path: \(payload.compileUnitRootDirPath)")
            guard let compileUnit = await Database.shared().compileUnit(withRootDir: url) else {
                context.logger.info("No already-open compile unit, path: \(payload.compileUnitRootDirPath)")
                let response = CompileUnitAnalyzeDiffResponse(
                    error: "no open compile unit matching root dir",
                    affectedAPIEndpoints: []
                )
                return try Response.withJSON(response, status: .badRequest)
            }
            
            guard FileManager.default.fileExists(atPath: payload.diffFilePath, isDirectory: nil) else {
                context.logger.info("File doesn't exist at provided diff path: \(payload.diffFilePath)")
                let response = CompileUnitAnalyzeDiffResponse(
                    error: "no such file at provided `diffFilePath`",
                    affectedAPIEndpoints: []
                )
                return try Response.withJSON(response, status: .badRequest)
            }
            
            let diffURL = URL(fileURLWithPath: payload.diffFilePath)
            let ruleContext = try RuleContext(compileUnit: compileUnit, minimalDiffFileURL: diffURL)
            let maybeRuleInstance: PolicyRule? = switch payload.ruleName {
            case "HardcodedColors":
                HardcodedColors(context: ruleContext)
            case "AddedTodo":
                AddedTodo(context: ruleContext)
            case "AxiosConfigRule":
                AxiosConfigRule(context: ruleContext)
            case "LogUserListRule":
                LogUserListRule(context: ruleContext)
            case "DemoTransactionTypeEnumRule":
                DemoTransactionTypeEnumRule(context: ruleContext)
            case "dbMigration-CreateIndexRule":
                DBMigrationCreateIndexRule(context: ruleContext)
            case "dbMigration-SafeTableOperationsRule":
                DBMigrationSafeTableOperationsRule(context: ruleContext)
            case "dbMigration-UpdateTable":
                DBMigrationUpdateTable(context: ruleContext)
            case "TRPCDestructuringRule":
                TRPCDestructuringRule(context: ruleContext)
            case "SoftDeletePropertyRule":
                SoftDeleteQueryRule(context: ruleContext)
            case "RunwayAsTeamIdRule":
                // TODO: Make this less hacky
                if compileUnit.rootDir.absoluteString.contains("tanagram_pytest") {
                    RunwayAsTeamIdRule(context: ruleContext, config: .testDefault())
                } else {
                    RunwayAsTeamIdRule(context: ruleContext, config: .runwayDefault())
                }
            case "RunwayApiInterfaceRule":
                // TODO: Make this less hacky
                if compileUnit.rootDir.absoluteString.contains("tanagram_pytest") {
                    RunwayApiInterfaceRule(context: ruleContext, config: .testDefault())
                } else {
                    RunwayApiInterfaceRule(context: ruleContext, config: .runwayDefault())
                }
            case "RunwayPrivateInTeamRule":
                // TODO: Make this less hacky
                if compileUnit.rootDir.absoluteString.contains("tanagram_pytest") {
                    RunwayPrivateInTeamRule(context: ruleContext, config: .testDefault())
                } else {
                    RunwayPrivateInTeamRule(context: ruleContext, config: .runwayDefault())
                }
            default:
                nil
            }
            guard let ruleInstance = maybeRuleInstance else {
                context.logger.info("No rule matching provided rule name: \(payload.ruleName)")
                let response = RunRuleResponse(
                    error: "no rule matching '\(payload.ruleName)'",
                    matchLocations: []
                )
                return try Response.withJSON(response, status: .badRequest)
            }
            
            let matchingLines = try await ruleInstance.findMatchingLines()
            let matchLocations = matchingLines.map { RuleMatchLocation.fromDiffLine($0, includeContent: payload.returnLineContent) }
            if matchLocations.count > 0 {
                context.logger.info("Found matching locations: \(matchLocations.map { "\($0.filePath):\($0.zeroBasedLine + 1)" }.joined(separator: "\n"))")
            } else {
                context.logger.info("No matching locations found")
            }
            let response = RunRuleResponse(
                error: nil,
                matchLocations: matchLocations
            )
            return try Response.withJSON(response)
        } catch {
            context.logger.error("Error running rule: \(error)")
            let response = RunRuleResponse(
                error: "Internal server error: \(error)",
                matchLocations: []
            )
            return try Response.withJSON(response, status: .internalServerError)
        }
    }
    
    struct NearbyComment: JSONWrapper {
        let text: String
        let location: String
        
        static func fromResult(_ result: FindNearbyComments.Result) -> Self {
            var location = ""
            switch result.commentLocation {
            case .beforeLine: location = "beforeLine"
            case .endOfLine: location = "endOfLine"
            }
            
            return Self(text: result.commentText, location: location)
        }
    }
    
    struct NearbyCommentsRequest: JSONWrapper {
        let compileUnitRootDirPath: String
        let filePath: String
        let zeroBasedLine: Int
    }
    
    struct NearbyCommentsResponse: JSONWrapper {
        let error: String?
        let nearbyComments: [NearbyComment]
    }
    
    // Returns comments before and at the end of the `zeroBasedLine` specified in the request
    @Sendable private func nearbyComments(_ request: Request, context: some RequestContext) async throws -> Response {
        do {
            let payload = try await request.parseJSON(to: NearbyCommentsRequest.self)
            let url = URL(fileURLWithPath: payload.compileUnitRootDirPath, isDirectory: true)
            context.logger.info("Attempting to find existing compile unit, path: \(payload.compileUnitRootDirPath)")
            guard let compileUnit = await Database.shared().compileUnit(withRootDir: url) else {
                context.logger.info("No already-open compile unit, path: \(payload.compileUnitRootDirPath)")
                let response = NearbyCommentsResponse(
                    error: "no open compile unit matching root dir",
                    nearbyComments: []
                )
                return try Response.withJSON(response, status: .badRequest)
            }
            
            // Column doesn't matter, only row
            let codeLocation = CodeLocation(compileUnit: compileUnit, documentPath: payload.filePath, locationInFile: .point(try! PointUTF16Z(row: payload.zeroBasedLine, column: 0)))
            let results = try FindNearbyComments(codeLocation: codeLocation).run()
            let response = NearbyCommentsResponse(
                error: nil,
                nearbyComments: results.map { NearbyComment.fromResult($0) }
            )
            return try Response.withJSON(response)
            // TODO: Catch known errors from FindNearbyComments
        } catch let hummingbirdError as HTTPError {
            let response = NearbyCommentsResponse(
                error: hummingbirdError.body as? String ?? "Unknown error",
                nearbyComments: []
            )
            return try Response.withJSON(
                response,
                status: hummingbirdError.status
            )
        } catch FindNearbyComments.Error.unsupportedFileExtension(let fileExtension) {
            let response = NearbyCommentsResponse(
                error: "Unsupported file extension: \(fileExtension)",
                nearbyComments: []
            )
            return try Response.withJSON(
                response,
                status: .badRequest
            )
        } catch {
            context.logger.error("Error running nearbyComments: \(error)")
            let response = NearbyCommentsResponse(
                error: "Internal server error: \(error)",
                nearbyComments: []
            )
            return try Response.withJSON(
                response,
                status: .internalServerError
            )
        }
    }
}

extension Request {
    func parseJSON<T>(to: T.Type) async throws -> T where T: JSONWrapper {
        // TODO: Safer access to headers and contentLength specifically
        guard let contentLengthValue = self.headers[.contentLength] else {
            throw HTTPError(.badRequest, message: "Missing Content-Length header")
        }
        let contentLength = Int(contentLengthValue)!
        let bodyData = try await self.body.collect(upTo: contentLength).getData(at: 0, length: contentLength)
        return try T.fromJSONData(bodyData!)
    }
}

extension Response {
    static func withJSON(_ jsonWrapper: JSONWrapper, status: HTTPResponse.Status = .ok) throws -> Self {
        let data = try jsonWrapper.toJSONData()
        return Response(
            status: status,
            headers: [.contentType: "application/json"],
            // TODO: Look into using `.serializedBytes`
            // (would need ByteBuffer to conform to `SwiftProtobufContiguousBytes`
            body: .init(byteBuffer: ByteBuffer(data: data))
        )
    }
}
