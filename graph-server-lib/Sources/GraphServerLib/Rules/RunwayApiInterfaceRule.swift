//
//  RunwayApiInterfaceRule.swift
//  graph-server-lib
//
//  Created by <PERSON><PERSON><PERSON> on 6/12/25.
//

import Foundation
import Logging

class RunwayApiInterfaceRule: PolicyRule {
    private let logger: Logger
    let database: Database
    let context: RuleContext
    let config: Config
    
    // Allow some flexibility for tests
    struct Config {
        let expressRouterMethodIDs: [ScipID]
        
        static func runwayDefault() -> Config {
            return Config(
                expressRouterMethodIDs: [
                    "scip-typescript npm @types/express-serve-static-core % `index.d.ts`/IRouter#get.",
                    "scip-typescript npm @types/express-serve-static-core % `index.d.ts`/IRouter#post.",
                    "scip-typescript npm @types/express-serve-static-core % `index.d.ts`/IRouter#put.",
                    "scip-typescript npm @types/express-serve-static-core % `index.d.ts`/IRouter#delete."
                ]
            )
        }
        
        static func testDefault() -> Config {
            return Config(
                expressRouterMethodIDs: [
                    "scip-typescript npm @types/express-serve-static-core 4.17.33 `index.d.ts`/IRouter#get.",
                    "scip-typescript npm @types/express-serve-static-core 4.17.33 `index.d.ts`/IRouter#post.",
                    "scip-typescript npm @types/express-serve-static-core 4.17.33 `index.d.ts`/IRouter#put.",
                    "scip-typescript npm @types/express-serve-static-core 4.17.33 `index.d.ts`/IRouter#delete."
                ]
            )
        }
    }
    
    public init(database: Database = Database.shared(), context: RuleContext, config: Config = .runwayDefault()) {
        self.logger = TLog.default(labeled: "RunwayApiInterfaceRule")
        self.database = database
        self.context = context
        self.config = config
    }
    
    func findMatchingLines() async throws -> [ParseGitDiff.DiffSideLine] {
        let newLocations = self.context.diffLocations.filter { $0.side == .new }
        let locations = try await self.run(inLocations: newLocations)
        return locations.map { ParseGitDiff.DiffSideLine(side: .new, location: $0, content: "") }
    }
    
    public func run(inLocations locatables: [any Locatable]) async throws -> [CodeLocation] {
        logger.info("Starting RunwayApiInterfaceRule analysis for \(locatables.count) locations")
        
        let getCallsToMethods = GetCallsToMethods(database: database)
        
        let codeLocations = locatables.compactMap { $0.location }
        let expressRouterCalls = try await getCallsToMethods.run(inLocations: codeLocations, methodIDs: self.config.expressRouterMethodIDs)
        
        let locations = expressRouterCalls.map { $0.location }
        
        logger.info("RunwayApiInterfaceRule analysis complete: found \(locations.count) Express router method calls")
        
        return locations
    }
}
