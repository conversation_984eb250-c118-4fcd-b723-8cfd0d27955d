//
//  AxiosConfigRule.swift
//  graph-server-lib
//
//  Created by Augment Agent on 1/27/25.
//

import Foundation
import Logging

class AxiosConfigRule: PolicyRule {
    private let logger: Logger
    let database: Database
    let context: RuleContext
    let config: Config
    
    // Allow some flexibility for tests
    struct Config {
        let axiosCreateMethodIDs: [ScipID]
        let axiosGetMethodIDs: [ScipID]
        let axiosPostMethodIDs: [ScipID]
        let requiredConfigKeys: [String]
        let requiredHeaders: [String]


        static func defaultConfig() -> Config {
            return Config(
                axiosCreateMethodIDs: [
                    "scip-typescript npm axios % `index.d.ts`/AxiosStatic#create().",
                    "scip-typescript npm axios % `index.d.ts`/AxiosInstance#create()."
                ],
                axiosGetMethodIDs: [
                    "scip-typescript npm axios % `index.d.ts`/Axios#get()."
                ],
                axiosPostMethodIDs: [
                    "scip-typescript npm axios % `index.d.ts`/Axios#post()."
                ],
                requiredConfigKeys: ["timeout"],
                requiredHeaders: ["User-Agent"]
            )
        }

    }
    
    public init(database: Database = Database.shared(), context: RuleContext, config: Config = .defaultConfig()) {
        self.logger = TLog.default(labeled: "AxiosConfigRule")
        self.database = database
        self.context = context
        self.config = config
    }
    
    func findMatchingLines() async throws -> [ParseGitDiff.DiffSideLine] {
        let newLocations = self.context.diffLocations.filter { $0.side == .new }
        let locations = try await self.run(inLocations: newLocations)
        return locations.map { ParseGitDiff.DiffSideLine(side: .new, location: $0, content: "") }
    }
    
    public func run(inLocations locatables: [any Locatable]) async throws -> [CodeLocation] {
        logger.info("Starting AxiosConfigRule analysis for \(locatables.count) locations")

        let codeLocations = locatables.compactMap { $0.location }
        var allViolations: [MethodCall] = []

        // Check axios.create calls (parameter 0)
        let axiosCreateViolations = try await checkAxiosMethodCalls(
            methodIDs: self.config.axiosCreateMethodIDs,
            parameterIndex: 0,
            methodName: "axios.create",
            codeLocations: codeLocations
        )
        allViolations.append(contentsOf: axiosCreateViolations)

        // Check axios.get calls (parameter 1)
        let axiosGetViolations = try await checkAxiosMethodCalls(
            methodIDs: self.config.axiosGetMethodIDs,
            parameterIndex: 1,
            methodName: "axios.get",
            codeLocations: codeLocations
        )
        allViolations.append(contentsOf: axiosGetViolations)

        // Check axios.post calls (parameter 2)
        let axiosPostViolations = try await checkAxiosMethodCalls(
            methodIDs: self.config.axiosPostMethodIDs,
            parameterIndex: 2,
            methodName: "axios.post",
            codeLocations: codeLocations
        )
        allViolations.append(contentsOf: axiosPostViolations)

        let locations = allViolations.map { $0.location }

        logger.info("AxiosConfigRule analysis complete: found \(locations.count) violations")

        return locations
    }

    private func checkAxiosMethodCalls(
        methodIDs: [ScipID],
        parameterIndex: Int,
        methodName: String,
        codeLocations: [CodeLocation]
    ) async throws -> [MethodCall] {
        let getCallsToMethods = GetCallsToMethods(database: database)

        // Find all calls to the specified axios methods
        let methodCalls = try await getCallsToMethods.run(inLocations: codeLocations, methodIDs: methodIDs)

        let filteredResults = try await methodCalls.asyncMap { methodCall -> MethodCall? in
            // Get the source text for the method call span
            let spanText = try methodCall.span.sourceText()
            
            // Skip if this isn't an axios call
            guard spanText.contains("axios") else {
                logger.info("Skipping non-axios call: \(spanText)")
                return nil
            }
            
            guard let configParam = methodCall.parameter(at: parameterIndex) else {
                logger.info("\(methodName) call without configuration parameter at \(methodCall.location.description)")
                return methodCall
            }

            let configValue = configParam.value
            let configNode = configValue.node
            let parser = configValue.parser

            // Create a SymbolResolver with this parser
            let symbolResolver = SymbolResolver(
                database: self.context.database,
                compileUnit: self.context.compileUnit,
                parser: parser
            )

            // Resolve the object expression
            guard let resolvedObject = await symbolResolver.resolveObjectExpression(configNode) else {
                logger.info("Failed to resolve config object for \(methodName) call at \(methodCall.location.description)")
                return nil
            }
            
            self.logger.debug("""
                Resolved axios configuration:
                \(formatResolvedValue(ResolvedValue.object(resolvedObject)))
                """,
                metadata: [
                    "file": .string(parser.file.path),
                    "spanText": "\(spanText)"
                ]
            )

            // Now you can work with the resolved object
            var missingConfigs: [String] = []

            // Check required config keys
            for requiredKey in self.config.requiredConfigKeys {
                if resolvedObject[requiredKey] == nil {
                    missingConfigs.append("Missing '\(requiredKey)' configuration")
                }
            }

            // Check required headers
            if let headersValue = resolvedObject["headers"] {
                // If headers is an object, check for specific headers
                if case .object(let headersObject) = headersValue {
                    for requiredHeader in self.config.requiredHeaders {
                        if headersObject[requiredHeader] == nil {
                            missingConfigs.append("Missing '\(requiredHeader)' header")
                        }
                    }
                }
            } else {
                // No headers property at all
                for requiredHeader in self.config.requiredHeaders {
                    missingConfigs.append("Missing '\(requiredHeader)' header")
                }
            }

            logger.info("Analyzing \(methodName) call; location=\(methodCall.location.description), missingConfigs=\(missingConfigs)")

            return missingConfigs.isEmpty ? nil : methodCall
        }

        return filteredResults.compactMap { $0 }
    }
    
    private func formatResolvedValue(_ value: ResolvedValue, indent: String = "") -> String {
        switch value {
        case .leaf(_, let sourceText):
            return "\(indent)\(sourceText)"
        case .object(let dict):
            return dict.map { key, value in
                "\(indent)\(key): \(formatResolvedValue(value, indent: indent + "  "))"
            }.joined(separator: "\n")
        }
    }
}
