//
//  RunwayPrivateInTeamRule.swift
//  graph-server-lib
//
//  Created by AI Assistant on 6/30/25.
//

import Foundation
import Logging

class RunwayPrivateInTeamRule: PolicyRule {
    private let logger: Logger
    let database: Database
    let context: RuleContext
    let config: Config
    
    struct Config {
        let findByMethodIDs: [ScipID]
        let findMethodIDs: [ScipID]
        let queryBuilderMethodIDs: [ScipID]
        let receiverIDs: [ScipID]
        let whereMethodIDs: [ScipID]
        
        static func runwayDefault() -> Config {
            return Config(
                findByMethodIDs: [
                    "scip-typescript npm runway-api 1.0.0 lib/`RunwayBaseEntity.ts`/RunwayBaseEntity#getOneBy().",
                    "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#findBy().",
                    "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#findOneBy().",
                    "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#countBy().",
                    "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#existsBy()."
                ],
                findMethodIDs: [
                    "scip-typescript npm runway-api 1.0.0 lib/`RunwayBaseEntity.ts`/RunwayBaseEntity#getOne().",
                    "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#find().",
                    "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#findOne()."
                ],
                queryBuilderMethodIDs: [
                    "scip-typescript npm typeorm % repository/`BaseEntity.d.ts`/BaseEntity#createQueryBuilder()."
                ],
                receiverIDs: [
                    "scip-typescript npm runway-api 1.0.0 entity/`Asset.ts`/Asset#",
                    "scip-typescript npm runway-api 1.0.0 entity/`AssetGroup.ts`/AssetGroup#",
                    "scip-typescript npm runway-api 1.0.0 entity/`CustomPreset.ts`/CustomPreset#",
                    "scip-typescript npm runway-api 1.0.0 entity/`GeneratedVoice.ts`/GeneratedVoice#",
                    "scip-typescript npm runway-api 1.0.0 entity/datasets/`Dataset.ts`/Dataset#",
                    "scip-typescript npm runway-api 1.0.0 entity/tasks/`TaskArtifact.ts`/TaskArtifact#",
                    "scip-typescript npm runway-api 1.0.0 entity/video/`VideoComposition.ts`/VideoComposition#"
                ],
                whereMethodIDs: [
                    "scip-typescript npm typeorm % query-builder/`SelectQueryBuilder.d.ts`/SelectQueryBuilder#where().",
                    "scip-typescript npm typeorm % query-builder/`SelectQueryBuilder.d.ts`/SelectQueryBuilder#andWhere()."
                ]
            )
        }
        
        static func testDefault() -> Config {
            return Config(
                findByMethodIDs: [
                    "scip-typescript npm typeorm 0.3.20 repository/`BaseEntity.d.ts`/BaseEntity#findBy().",
                    "scip-typescript npm typeorm 0.3.20 repository/`BaseEntity.d.ts`/BaseEntity#findOneBy().",
                    "scip-typescript npm typeorm 0.3.20 repository/`BaseEntity.d.ts`/BaseEntity#countBy().",
                    "scip-typescript npm typeorm 0.3.20 repository/`BaseEntity.d.ts`/BaseEntity#existsBy()."
                ],
                findMethodIDs: [
                    "scip-typescript npm typeorm 0.3.20 repository/`BaseEntity.d.ts`/BaseEntity#find().",
                    "scip-typescript npm typeorm 0.3.20 repository/`BaseEntity.d.ts`/BaseEntity#findOne()."
                ],
                queryBuilderMethodIDs: [
                    "scip-typescript npm typeorm 0.3.20 repository/`BaseEntity.d.ts`/BaseEntity#createQueryBuilder()."
                ],
                receiverIDs: [
                    "scip-typescript npm example-api 1.0.0 `TaskArtifact.ts`/TaskArtifact#"
                ],
                whereMethodIDs: [
                    "scip-typescript npm typeorm 0.3.20 query-builder/`SelectQueryBuilder.d.ts`/SelectQueryBuilder#where().",
                    "scip-typescript npm typeorm 0.3.20 query-builder/`SelectQueryBuilder.d.ts`/SelectQueryBuilder#andWhere()."
                ]
            )
        }
    }
    
    public init(database: Database = Database.shared(), context: RuleContext, config: Config = .runwayDefault()) {
        self.logger = TLog.default(labeled: "RunwayPrivateInTeamRule")
        self.database = database
        self.context = context
        self.config = config
    }
    
    func findMatchingLines() async throws -> [ParseGitDiff.DiffSideLine] {
        let newLocations = self.context.diffLocations.filter { $0.side == .new }
        let locations = try await self.run(inLocations: newLocations)
        return locations.map { ParseGitDiff.DiffSideLine(side: .new, location: $0, content: "") }
    }
    
    public func run(inLocations locatables: [any Locatable]) async throws -> [CodeLocation] {
        logger.info("Starting RunwayPrivateInTeamRule analysis for \(locatables.count) locations")
        
        let codeLocations = locatables.compactMap { $0.location }
        
        // Create thunks for each pattern
        let thunks: [([CodeLocation]) async throws -> [MethodCall]] = [
            // Pattern 1: findBy/findOneBy/countBy pattern
            { locations in
                let getCallsToMethods = GetCallsToMethods(database: self.database)
                return try await self.findByPattern(getCallsToMethods: getCallsToMethods, locations: locations)
            },
            // Pattern 2: find/findOne pattern  
            { locations in
                let getCallsToMethods = GetCallsToMethods(database: self.database)
                return try await self.findPattern(getCallsToMethods: getCallsToMethods, locations: locations)
            },
            // Pattern 3: queryBuilder pattern
            { locations in
                let getCallsToMethods = GetCallsToMethods(database: self.database)
                let getChainedCallsToMethods = GetChainedCallsToMethods(database: self.database)
                return try await self.queryBuilderPattern(
                    getCallsToMethods: getCallsToMethods,
                    getChainedCallsToMethods: getChainedCallsToMethods,
                    locations: locations
                )
            }
        ]
        
        // Use XargsUnion to combine all pattern results
        let xargsUnion = XargsUnion<CodeLocation, MethodCall>(database: database)
        let allResults = try await xargsUnion.run(collection: codeLocations, thunks: thunks, dedupStrategy: .full)
        let locations = allResults.map { $0.location }
        
        logger.info("RunwayPrivateInTeamRule analysis complete: found \(locations.count) violations")
        
        return locations
    }
    
    private func findByPattern(getCallsToMethods: GetCallsToMethods, locations: [CodeLocation]) async throws -> [MethodCall] {
        let findByCalls = try await getCallsToMethods.run(inLocations: locations, methodIDs: config.findByMethodIDs)
        let receiverChecker = MethodCallReceiverIsSymbol(database: database)
        
        let filteredCalls = try await findByCalls.asyncCompactMap { methodCall -> MethodCall? in
            // Check if receiver matches any of the configured receiver IDs
            let isValidReceiver = try await receiverChecker.run(methodCall: methodCall, receiverSymbols: config.receiverIDs)
            guard isValidReceiver else {
                return nil
            }
            
            // Check first parameter for userId and privateInTeam
            guard let firstParam = methodCall.parameter(at: 0) else {
                return nil
            }
            
            let hasUserId = firstParam.value.isObject(withKeyPath: "userId")
            let hasPrivateInTeam = firstParam.value.isObject(withKeyPath: "privateInTeam")
            
            // Only flag if userId is present but privateInTeam is missing
            return (hasUserId && !hasPrivateInTeam) ? methodCall : nil
        }
        
        return filteredCalls
    }
    
    private func findPattern(getCallsToMethods: GetCallsToMethods, locations: [CodeLocation]) async throws -> [MethodCall] {
        let findCalls = try await getCallsToMethods.run(inLocations: locations, methodIDs: config.findMethodIDs)
        let receiverChecker = MethodCallReceiverIsSymbol(database: database)

        let filteredCalls = try await findCalls.asyncCompactMap { methodCall -> MethodCall? in
            // Check if receiver matches any of the configured receiver IDs
            let isValidReceiver = try await receiverChecker.run(methodCall: methodCall, receiverSymbols: config.receiverIDs)
            guard isValidReceiver else {
                return nil
            }
            
            // Check first parameter for where.userId and where.privateInTeam
            guard let firstParam = methodCall.parameter(at: 0) else {
                return nil
            }
            
            // Check for simple where.userId and where.privateInTeam
            let hasWhereUserId = firstParam.value.isObject(withKeyPath: "where.userId")
            let hasWherePrivateInTeam = firstParam.value.isObject(withKeyPath: "where.privateInTeam")
            
            // If simple where case has violation, return it
            if hasWhereUserId && !hasWherePrivateInTeam {
                return methodCall
            }
            
            // Check for where array case
            if let whereValue = firstParam.value.get(keyPath: "where") {
                // Check if where is an array by examining the node type
                if whereValue.node.nodeType == "array" {
                    // Iterate through array elements
                    var foundViolation = false
                    whereValue.node.enumerateChildren { arrayElementNode in
                        guard arrayElementNode.nodeType == "object" else { return }
                        
                        // Create a SourceValue for each array element
                        let elementValue = SourceValue(kind: .objectLiteral, node: arrayElementNode, parser: whereValue.parser)
                        
                        let hasUserIdInElement = elementValue.isObject(withKeyPath: "userId")
                        let hasPrivateInTeamInElement = elementValue.isObject(withKeyPath: "privateInTeam")
                        
                        // If any array element has userId but missing privateInTeam, flag it
                        if hasUserIdInElement && !hasPrivateInTeamInElement {
                            foundViolation = true
                        }
                    }
                    
                    if foundViolation {
                        return methodCall
                    }
                }
            }
            
            return nil
        }
        
        return filteredCalls
    }
    
    private func queryBuilderPattern(
        getCallsToMethods: GetCallsToMethods,
        getChainedCallsToMethods: GetChainedCallsToMethods,
        locations: [CodeLocation]
    ) async throws -> [MethodCall] {
        let queryBuilderCalls = try await getCallsToMethods.run(inLocations: locations, methodIDs: config.queryBuilderMethodIDs)
        let receiverChecker = MethodCallReceiverIsSymbol(database: database)
        
        let filteredCalls = try await queryBuilderCalls.asyncCompactMap { methodCall -> MethodCall? in
            // Check if receiver matches any of the configured receiver IDs
            let isValidReceiver = try await receiverChecker.run(methodCall: methodCall, receiverSymbols: config.receiverIDs)
            guard isValidReceiver else {
                return nil
            }
            
            // Find chained where/andWhere calls
            let chainedWhereCalls = try await getChainedCallsToMethods.run(
                initialCall: methodCall,
                chainedMethodIDs: config.whereMethodIDs
            )
            
            let whereCallsWithUserId = chainedWhereCalls.filter { whereCall in
                guard let firstParam = whereCall.parameter(at: 0) else { 
                    return false 
                }
                let paramString = firstParam.value.sourceStringValue()
                return paramString.contains("userId")
            }
            
            let whereCallsWithPrivateInTeam = chainedWhereCalls.filter { whereCall in
                guard let firstParam = whereCall.parameter(at: 0) else { return false }
                let paramString = firstParam.value.sourceStringValue()
                return paramString.contains("privateInTeam")
            }
            
            let hasUserIdWhere = !whereCallsWithUserId.isEmpty
            let hasPrivateInTeamWhere = !whereCallsWithPrivateInTeam.isEmpty
            
            // Only flag if userId where clause is present but privateInTeam where clause is missing
            return (hasUserIdWhere && !hasPrivateInTeamWhere) ? methodCall : nil
        }
        
        return filteredCalls
    }
}
