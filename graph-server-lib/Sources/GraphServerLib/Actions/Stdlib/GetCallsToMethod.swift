//
//  GetCallsToMethod.swift
//  graph-server-lib
//
//  Created by <PERSON><PERSON><PERSON> on 5/28/25.
//

import SwiftTreeSitter

class GetCallsToMethods {
    let database: Database
    private var cachedASTs: [String: SwiftTreeSitter.Node] = [:]
    // TODO: Make this a generic type instead of TypescriptParser
    private var cachedParsers: [String: TypescriptParser] = [:]
    
    public init(database: Database = Database.shared()) {
        self.database = database
    }
    
    // If `locatables` is empty, this method will return all locations where any of the `methodIDs` are called.
    public func run(inLocations locatables: [any Locatable], methodIDs: [ScipID]) async throws -> [MethodCall] {
        var allOccurrences: [SymbolOccurrence] = []
        for methodID in methodIDs {
            let occurrences = methodID.hasWildcards 
                ? await self.database.occurrences(forSymbolIDPattern: methodID.rawValue, filterExcludeRole: .definition)
                : await self.database.occurrences(forSymbolID: methodID.rawValue, filterExcludeRole: .definition)
            allOccurrences.append(contentsOf: occurrences)
        }
        let filteredOccurrences = locatables.isEmpty ? allOccurrences : self.filter(occurrences: allOccurrences, byLocations: locatables)
        return try filteredOccurrences.map { occurrence in
            let fullSpan = try fullSpan(forOccurrence: occurrence)
            let outputRange = fullSpan ?? occurrence.enclosingRange ?? occurrence.range
            let span = CodeLocation(
                compileUnit: occurrence.compileUnit,
                documentPath: occurrence.document,
                locationInFile: .range(outputRange)
            )
            let parameters = (try? self.parameters(forOccurrence: occurrence)) ?? []
            let matchingMethodID = ScipID(occurrence.symbolInformationId)!
            return MethodCall(
                method: matchingMethodID,
                span: span,
                parameters: parameters
            )
        }
    }
    
    private func filter(occurrences: [SymbolOccurrence], byLocations locations: [any Locatable]) -> [SymbolOccurrence] {
        // TODO: Runtime here is O(MxN); this could probably be made more efficient
        return occurrences.filter { occurrence in
            locations.contains(where: { $0.location.contains(occurrence.location) })
        }
    }
    
    private func callExpression(forOccurrence occurrence: SymbolOccurrence) throws -> SwiftTreeSitter.Node? {
        let parser = try self.parser(forDocumentOfOccurrence: occurrence)
        let root = try self.rootNode(forDocumentOfOccurrence: occurrence)
        return parser.closestCallExpression(fromRoot: root, enclosingRange: occurrence.range)
    }
    
    private func fullSpan(forOccurrence occurrence: SymbolOccurrence) throws -> Range<PointUTF16Z>? {
        let maybeCallExpression = try self.callExpression(forOccurrence: occurrence)
        if let callExpression = maybeCallExpression {
            return callExpression.pointRange.asUTF16()
        } else {
            return nil
        }
    }
    
    private func parameters(forOccurrence occurrence: SymbolOccurrence) throws -> [MethodParameter] {
        let parser = try self.parser(forDocumentOfOccurrence: occurrence)
        guard let callExpression = try self.callExpression(forOccurrence: occurrence) else { return [] }
        return parser.parameters(forCallExpression: callExpression)
    }
    
    // TODO: Handle other file types
    private func parser(forDocumentOfOccurrence occurrence: SymbolOccurrence) throws -> TypescriptParser {
        if let cachedParser = self.cachedParsers[occurrence.document] {
            return cachedParser
        }
        
        let parser = try TypescriptParser(location: occurrence.location)
        self.cachedParsers[occurrence.document] = parser
        return parser
    }

    private func rootNode(forDocumentOfOccurrence occurrence: SymbolOccurrence) throws -> SwiftTreeSitter.Node {
        if let cachedAST = self.cachedASTs[occurrence.document] {
            return cachedAST
        }
        
        let parser = try self.parser(forDocumentOfOccurrence: occurrence)
        guard let rootNode = try parser.parse() else {
            throw ParserError.missingRootNode
        }
        self.cachedASTs[occurrence.document] = rootNode
        return rootNode
    }
}
