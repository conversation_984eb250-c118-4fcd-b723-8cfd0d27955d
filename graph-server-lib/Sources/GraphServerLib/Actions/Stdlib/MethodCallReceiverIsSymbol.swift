//
//  MethodCallReceiverIsSymbol.swift
//  graph-server-lib
//
//  Created by AI Agent on 6/29/25.
//

import SwiftTreeSitter

class MethodCallReceiverIsSymbol {
    let database: Database
    private var cachedASTs: [String: SwiftTreeSitter.Node] = [:]
    // TODO: Make this a generic type instead of TypescriptParser
    private var cachedParsers: [String: TypescriptParser] = [:]
    
    public init(database: Database = Database.shared()) {
        self.database = database
    }
    
    /// Returns true if the receiver of the given method call is one of the listed symbols
    /// and the method call is a static method call (not an instance method call).
    public func run(methodCall: MethodCall, receiverSymbols: [ScipID]) async throws -> Bool {
        guard !receiverSymbols.isEmpty else { return false }
        
        let parser = try self.parser(forDocumentAtLocation: methodCall.location)
        let root = try self.rootNode(forDocumentAtLocation: methodCall.location)
        
        // Find the AST node for the method call
        guard let callExpression = parser.closestCallExpression(fromRoot: root, enclosingRange: methodCall.span.locationInFile.getRange()) else {
            return false
        }
        
        // Get the function part of the call expression (e.g., "TaskArtifact.createQueryBuilder" in "TaskArtifact.createQueryBuilder(...)")
        guard let functionNode = callExpression.child(byFieldName: "function") else {
            return false
        }
        
        // Check if this is a member expression (e.g., "object.method" or "Class.staticMethod")
        guard functionNode.nodeType == "member_expression" else {
            return false
        }
        
        // Get the object part (e.g., "TaskArtifact" in "TaskArtifact.createQueryBuilder")
        guard let objectNode = functionNode.child(byFieldName: "object") else {
            return false
        }
        
        // For static method calls, the object should be an identifier (class name)
        // For instance method calls, the object would be a variable or another expression
        guard objectNode.nodeType == "identifier" else {
            return false
        }
        
        // Get the range of the receiver (object) to look up its symbol
        let objectRange = Range.fromTreeSitterPointRange(objectNode.pointRange)
        let absoluteFileURL = methodCall.location.absoluteFileURL()
        
        // Look up the symbol occurrence at the receiver position
        guard let receiverOccurrence = await self.database.occurrenceAt(
            absoluteFileURL: absoluteFileURL, 
            position: objectRange.lowerBound
        ) else {
            return false
        }
        
        // Check if the receiver symbol matches any of the provided symbols
        let receiverSymbolID = ScipID(receiverOccurrence.symbolInformationId)!
        return receiverSymbols.contains(receiverSymbolID)
    }
    
    // TODO: Handle other file types
    private func parser(forDocumentAtLocation location: CodeLocation) throws -> TypescriptParser {
        let documentPath = location.documentPath
        if let cachedParser = self.cachedParsers[documentPath] {
            return cachedParser
        }
        
        let parser = try TypescriptParser(location: location)
        self.cachedParsers[documentPath] = parser
        return parser
    }

    private func rootNode(forDocumentAtLocation location: CodeLocation) throws -> SwiftTreeSitter.Node {
        let documentPath = location.documentPath
        if let cachedAST = self.cachedASTs[documentPath] {
            return cachedAST
        }
        
        let parser = try self.parser(forDocumentAtLocation: location)
        guard let rootNode = try parser.parse() else {
            throw ParserError.missingRootNode
        }
        self.cachedASTs[documentPath] = rootNode
        return rootNode
    }
}
