//
//  GetChainedCallToMethods.swift
//  graph-server-lib
//
//  Created by <PERSON><PERSON><PERSON> on 5/28/25.
//

import Foundation
import SwiftTreeSitter

class GetChainedCallsToMethods {
    let database: Database
    private var cachedASTs: [String: SwiftTreeSitter.Node] = [:]
    // TODO: Make this a generic type instead of TypescriptParser
    private var cachedParsers: [String: TypescriptParser] = [:]
    
    public init(database: Database = Database.shared()) {
        self.database = database
    }
    
    // Find all chained method calls following the given initial method call.
    // If `chainedMethodIDs` is empty, return all chained method calls.
    public func run(initialCall: MethodCall, chainedMethodIDs: [ScipID]) async throws -> [MethodCall] {
        let parser = try self.parser(forDocumentAtLocation: initialCall.location)
        let root = try self.rootNode(forDocumentAtLocation: initialCall.location)
        
        // Find the AST node for the initial call
        guard let initialCallNode = parser.closestCallExpression(fromRoot: root, enclosingRange: initialCall.span.locationInFile.getRange()) else {
            return []
        }
        
        // Find all chained method calls following this initial call
        var chainedCalls: [MethodCall] = []
        
        /*
         * The AST for a method call chain is an inversion of that chain. E.g. for `a().b().c()`, the AST is:
         * ```
         * program [0, 0] - [1, 0]
         *   expression_statement [0, 0] - [0, 11]
         *     call_expression [0, 0] - [0, 11]
         *       function: member_expression [0, 0] - [0, 9]
         *         object: call_expression [0, 0] - [0, 7]
         *           function: member_expression [0, 0] - [0, 5]
         *             object: call_expression [0, 0] - [0, 3]
         *               function: identifier [0, 0] - [0, 1]
         *               arguments: arguments [0, 1] - [0, 3]
         *             property: property_identifier [0, 4] - [0, 5]
         *           arguments: arguments [0, 5] - [0, 7]
         *         property: property_identifier [0, 8] - [0, 9]
         *       arguments: arguments [0, 9] - [0, 11]
         * ```
         * where the innermost `call_expression` corresponds to `a()`,
         * its parent `call_expression` corresponds to `a().b()`,
         * and the top-level `call_expression` corresponds to `a().b().c()`.
         */
        // Walk up the AST to find the top-level chained expression
        var currentNode: SwiftTreeSitter.Node? = initialCallNode
        while let node = currentNode {
            if let parentNode = node.parent,
               parentNode.nodeType == "member_expression",
               let grandParentNode = parentNode.parent,
               grandParentNode.nodeType == "call_expression" {
                // This is a chained call - check if it matches our target method IDs
                if let methodCall = try? await self.methodCall(forCallExpression: grandParentNode, parser: parser, atLocation: initialCall.location) {
                    if chainedMethodIDs.isEmpty || self.methodMatches(methodCall.method, against: chainedMethodIDs) {
                        chainedCalls.append(methodCall)
                    }
                }
                currentNode = grandParentNode
            } else {
                break
            }
        }
        
        return chainedCalls
    }
    
    /// Check if a method ID matches against a list of target IDs, supporting wildcard patterns
    private func methodMatches(_ methodID: ScipID, against targetIDs: [ScipID]) -> Bool {
        for targetID in targetIDs {
            if targetID.hasWildcards {
                // Use SQLite LIKE pattern matching for wildcard patterns
                if self.isLikeMatch(methodID.rawValue, pattern: targetID.rawValue) {
                    return true
                }
            } else {
                // Exact match for non-wildcard patterns
                if methodID == targetID {
                    return true
                }
            }
        }
        return false
    }
    
    /// Simple SQLite LIKE pattern matching implementation
    /// Supports % (any sequence of characters) and _ (single character)
    private func isLikeMatch(_ string: String, pattern: String) -> Bool {
        // Convert SQLite LIKE pattern to regular expression
        let escapedPattern = pattern
            .replacingOccurrences(of: "\\", with: "\\\\")  // Escape backslashes
            .replacingOccurrences(of: ".", with: "\\.")    // Escape dots
            .replacingOccurrences(of: "+", with: "\\+")    // Escape plus
            .replacingOccurrences(of: "*", with: "\\*")    // Escape asterisks
            .replacingOccurrences(of: "?", with: "\\?")    // Escape question marks
            .replacingOccurrences(of: "[", with: "\\[")    // Escape brackets
            .replacingOccurrences(of: "]", with: "\\]")    // Escape brackets
            .replacingOccurrences(of: "(", with: "\\(")    // Escape parentheses
            .replacingOccurrences(of: ")", with: "\\)")    // Escape parentheses
            .replacingOccurrences(of: "{", with: "\\{")    // Escape braces
            .replacingOccurrences(of: "}", with: "\\}")    // Escape braces
            .replacingOccurrences(of: "^", with: "\\^")    // Escape caret
            .replacingOccurrences(of: "$", with: "\\$")    // Escape dollar sign
            .replacingOccurrences(of: "|", with: "\\|")    // Escape pipe
            .replacingOccurrences(of: "%", with: ".*")     // Convert % to regex .*
            .replacingOccurrences(of: "_", with: ".")      // Convert _ to regex .
        
        let regexPattern = "^" + escapedPattern + "$"
        
        do {
            let regex = try NSRegularExpression(pattern: regexPattern, options: [])
            let range = NSRange(location: 0, length: string.utf16.count)
            return regex.firstMatch(in: string, options: [], range: range) != nil
        } catch {
            // Fallback to exact match if regex fails
            return string == pattern
        }
    }
    
    private func methodCall(forCallExpression callExpression: SwiftTreeSitter.Node, parser: TypescriptParser, atLocation location: CodeLocation) async throws -> MethodCall? {
        /*
         * Given this example code:
         * ```
         * client.get("/api/data")
         *   .then(response => response.data)
         * ```
         * The `call_expression` node represents the whole thing.
         *
         * Its child `member_expression` node represents:
         * ```
         * client.get("/api/data")
         *   .then
         * ```
         *
         * We need to get the end of that `member_expression` because it'll match the end of the SCIP occurrence range.
         */
        // Get the range of the call expression
        guard let memberExpression = callExpression.child(byFieldName: "function") else { return nil }
        let memberRange = Range.fromTreeSitterPointRange(memberExpression.pointRange)
        
        let callRange = Range.fromTreeSitterPointRange(callExpression.pointRange)
        let callLocation = CodeLocation(
            compileUnit: location.compileUnit,
            documentPath: location.documentPath,
            locationInFile: .range(callRange)
        )

        let absoluteFileURL = location.absoluteFileURL()
        
        guard let occurrence = await self.database.occurrenceAt(absoluteFileURL: absoluteFileURL, position: memberRange.upperBound) else {
            return nil
        }
        
        let scipID = ScipID(occurrence.symbolInformationId)!
        let parameters = parser.parameters(forCallExpression: callExpression)
        return MethodCall(
            method: scipID,
            span: callLocation,
            parameters: parameters
        )
    }
    
    // TODO: Handle other file types
    private func parser(forDocumentAtLocation location: CodeLocation) throws -> TypescriptParser {
        let documentPath = location.documentPath
        if let cachedParser = self.cachedParsers[documentPath] {
            return cachedParser
        }
        
        let parser = try TypescriptParser(location: location)
        self.cachedParsers[documentPath] = parser
        return parser
    }

    private func rootNode(forDocumentAtLocation location: CodeLocation) throws -> SwiftTreeSitter.Node {
        let documentPath = location.documentPath
        if let cachedAST = self.cachedASTs[documentPath] {
            return cachedAST
        }
        
        let parser = try self.parser(forDocumentAtLocation: location)
        guard let rootNode = try parser.parse() else {
            throw ParserError.missingRootNode
        }
        self.cachedASTs[documentPath] = rootNode
        return rootNode
    }
}
