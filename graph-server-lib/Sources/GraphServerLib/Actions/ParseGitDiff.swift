//
//  ParseGitDiff.swift
//  graph-server-lib
//
//  Created by <PERSON><PERSON><PERSON> on 2/26/25.
//

import Foundation
import Logging

/*
 A git diff file is a concatenation of file-level diffs. Each file-level diff looks like:
    1. A line like `diff --git a/<original_filepath> b/<new_filepath>`
    2. One or two lines with SHAs and file modes/permissions
    3. A line like `--- a/<original_filepath>`
    4. A line like `+++ b/<new_filepath>`
    5. Hunks
 
 Each hunk looks like:
    6. A line like `@@ <hunk_header> @@` <optional_preview>
    7. +/- line-by-line diffs
 
 A hunk header looks like `@@ -1,7 +1,9 @@`.
 * The numbers after the first - show the starting line number and line count in the original file: -1,7 means "starting at line 1, 7 lines are shown from the original file"
 * The numbers after the + show the starting line number and line count in the new file: +1,9 means "starting at line 1, 9 lines are shown in the modified file"
 
 Git diff line numbers are 1-indexed. PointUTF16Z is meant to be 0-indexed,
 so we'll use -1 in a few places in this implementation.
 */
public struct ParseGitDiff {
    private let logger: Logger
    private let compileUnit: CompileUnit
    private let rawDiffText: String
    
    public enum DiffSide: Codable, Equatable, Hashable {
        case old
        case new
    }
    
    public struct DiffSideLine: Equatable, Hashable {
        let side: DiffSide
        let location: CodeLocation
        let content: String.SubSequence
    }
    
    public struct DiffLine: Equatable, Hashable {
        let oldContent: DiffSideLine?
        let newContent: DiffSideLine?
    }
    
    public struct HunkLocation: Equatable, Hashable, Locatable {
        public let side: DiffSide
        public let location: CodeLocation
    }
    
    public struct ParseResult: Equatable, Hashable {
        public let diffLines: [DiffLine]
        public let hunkLocations: [HunkLocation]
    }
    
    public init(compileUnit: CompileUnit, diffText: String) {
        self.logger = TLog.default(labeled: "ParseGitDiff")
        self.compileUnit = compileUnit
        self.rawDiffText = diffText
    }
    
    public init(compileUnit: CompileUnit, diffFileURL: URL) throws {
        self.logger = TLog.default(labeled: "ParseGitDiff")
        self.compileUnit = compileUnit
        self.rawDiffText = try String(contentsOf: diffFileURL, encoding: .utf8)
    }
    
    public func run() -> ParseResult {
        let fileChunks = self.rawDiffText.split(separator: "diff --git")
        let results = fileChunks.map(self.parse(fileDiff:))
        let allDiffLines = results.flatMap { $0.diffLines }
        let allCodeLocations = results.flatMap { $0.hunkLocations }
        return ParseResult(diffLines: allDiffLines, hunkLocations: allCodeLocations)
    }
    
    private func parse(fileDiff: String.SubSequence) -> ParseResult {
        self.logger.info("Parse fileDiff: \(fileDiff)")
        let lines = fileDiff.split(separator: "\n")
        var maybeOldDocumentPath: String.SubSequence? = nil
        var maybeNewDocumentPath: String.SubSequence? = nil
        for line in lines {
            if line.hasPrefix("---") {
                maybeOldDocumentPath = parse(filePathFromDiffHeaderLine: line)
                continue
            }
            if line.hasPrefix("+++") {
                maybeNewDocumentPath = parse(filePathFromDiffHeaderLine: line)
                continue
            }
        }
        guard let oldDocumentPath = maybeOldDocumentPath, let newDocumentPath = maybeNewDocumentPath else {
            self.logger.info("No document paths found in file diff. maybeOldDocumentPath: \(maybeOldDocumentPath ?? "nil"), maybeNewDocumentPath: \(maybeNewDocumentPath ?? "nil")")
            return ParseResult(diffLines: [], hunkLocations: [])
        }
        self.logger.info("Parsing file diff, oldDocumentPath: \(oldDocumentPath), newDocumentPath: \(newDocumentPath)")
        
        var output: [DiffLine] = []
        var hunkLocations: [HunkLocation] = []
        // Assumptions:
        // * We get a minimal diff (-U0),
        // * every line is part of the diff,
        // * addition lines (`+`) come after deletion lines (`-`),
        // * hunks are aligned at the first line (i.e. the first line of either side are supposed to be the "same" line)
        var hunkRanges: HunkRanges? = nil
        var longestSideLength: Int = 0
        var oldHunkOffset = 0
        var newHunkOffset = 0
        var onSide: DiffSide = .old
        var oldSideLines: [Int: DiffSideLine] = [:]
        var newSideLines: [Int: DiffSideLine] = [:]
        for line in lines.dropFirst(4) {
            // Skip lines that start with "\" (like "\ No newline at end of file")
            if line.hasPrefix("\\") {
                continue
            }
            if line.hasPrefix("@@") {
                // At the start of a new hunk; save results from previous hunk first if there are any
                for i in 0 ..< longestSideLength {
                    let oldSide = oldSideLines[i]
                    let newSide = newSideLines[i]
                    output.append(DiffLine(oldContent: oldSide, newContent: newSide))
                }
                
                // Reset for new hunk
                oldHunkOffset = -1
                newHunkOffset = -1
                hunkRanges = rangeParse(hunkHeaderLine: line)
                longestSideLength = [hunkRanges!.old?.count ?? 0, hunkRanges!.new?.count ?? 0].max()!
                
                // Create CodeLocation objects for this hunk range
                if let oldRange = hunkRanges!.old {
                    let startPoint = try! PointUTF16Z(row: oldRange.startIndex - 1, column: 0)
                    let endPoint = try! PointUTF16Z(row: oldRange.endIndex - 1, column: 0)
                    let oldLocation = CodeLocation(
                        compileUnit: self.compileUnit,
                        documentPath: String(oldDocumentPath),
                        locationInFile: .range(startPoint ..< endPoint)
                    )
                    hunkLocations.append(HunkLocation(side: .old, location: oldLocation))
                }
                if let newRange = hunkRanges!.new {
                    let startPoint = try! PointUTF16Z(row: newRange.startIndex - 1, column: 0)
                    let endPoint = try! PointUTF16Z(row: newRange.endIndex - 1, column: 0)
                    let newLocation = CodeLocation(
                        compileUnit: self.compileUnit,
                        documentPath: String(newDocumentPath),
                        locationInFile: .range(startPoint ..< endPoint)
                    )
                    hunkLocations.append(HunkLocation(side: .new, location: newLocation))
                }
                
                oldSideLines.removeAll()
                newSideLines.removeAll()
                continue
//                self.logger.info("Starting hunk, line: \(line), side: \(String(describing: diffSide)), range: \(hunkRange)")
            }
            guard let nonNilHunkRanges = hunkRanges else { continue }
            if line.hasPrefix("-") {
                onSide = .old
                oldHunkOffset += 1
            } else if line.hasPrefix("+") {
                onSide = .new
                newHunkOffset += 1
            }
            guard let nonNilSideRange = nonNilHunkRanges.range(forSide: onSide) else { continue }
            
            let currentOffset = (onSide == .old) ? oldHunkOffset : newHunkOffset
            let oneBasedLineNum = nonNilSideRange.startIndex + currentOffset
            guard nonNilSideRange.contains(oneBasedLineNum) else { continue }
            let codeLocation = CodeLocation(
                compileUnit: self.compileUnit,
                documentPath: String(onSide == .old ? oldDocumentPath : newDocumentPath),
                locationInFile: .point(try! PointUTF16Z(row: oneBasedLineNum - 1, column: 0))
            )
            let diffSideLine = DiffSideLine(
                side: onSide,
                location: codeLocation,
                // dropFirst to remove the leading +/-
                content: line.dropFirst()
            )
            switch onSide {
            case .old:
                oldSideLines[oldHunkOffset] = diffSideLine
            case .new:
                newSideLines[newHunkOffset] = diffSideLine
            }
            // Update longestSideLength
            longestSideLength = max(longestSideLength, max(oldHunkOffset, newHunkOffset))
        }
        
        for i in 0 ..< longestSideLength {
            let oldSide = oldSideLines[i]
            let newSide = newSideLines[i]
            output.append(DiffLine(oldContent: oldSide, newContent: newSide))
        }
        return ParseResult(diffLines: output, hunkLocations: hunkLocations)
    }
    
    // Given a line like `--- a/<path>` or `+++ b/<path>`, return `<path>`.
    private func parse(filePathFromDiffHeaderLine line: String.SubSequence) -> String.SubSequence {
        return line.dropFirst(6)
    }
    
    private struct HunkRanges {
        let old: Range<Int>?
        let new: Range<Int>?
        
        func range(forSide side: DiffSide) -> Range<Int>? {
            switch side {
            case .old:
                self.old
            case .new:
                self.new
            }
        }
    }
    
    /*
     Given a line like:
        * `@@ -107,0 +108,6 @@ struct TanagramApp: App {`
        * `@@ -70,0 +71 @@ def create_app(config=None, testing=False):`
     Return the range of changed lines, and which side the change is on.
     E.g. for the first line, it would return a range of (108 ..< 114).
     Side `.new` is preferred if there are any indicated-new lines in the hunk header;
     otherwise you'll get side `.old`.
     Returned ranges are one-based lines (coming directly from git diff output)
     */
    private func rangeParse(hunkHeaderLine line: String.SubSequence) -> HunkRanges {
        var oldRange: Range<Int>? = nil
        var newRange: Range<Int>? = nil
        let regex = /@@ -(?<oldStart>\d+)(,(?<oldLength>\d+))? \+(?<newStart>\d+)(,(?<newLength>\d+))? @@.*/
        if let result = try? regex.wholeMatch(in: line) {
            /*
             `result.newLength` would be nil in a hunk header like
             `@@ -70,0 +71 @@`
             in which case we assume the new side is 1 line long
             */
            let newLength = result.newLength == nil ? 1 : (Int(result.newLength!) ?? 1)
            let oldLength = result.oldLength == nil ? 1 : (Int(result.oldLength!) ?? 1)
            
            if newLength > 0 {
                // This would be a line change or addition
                let newStart = Int(result.newStart)!
                newRange = newStart ..< (newStart + newLength)
            }
            if oldLength > 0 {
                // This would be a line deletion
                let oldStart = Int(result.oldStart)!
                oldRange = oldStart ..< (oldStart + oldLength)
            }
            return HunkRanges(old: oldRange, new: newRange)
        } else {
            return HunkRanges(old: nil, new: nil)
        }
    }
}
