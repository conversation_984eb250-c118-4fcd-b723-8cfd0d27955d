//
//  TypescriptParser.swift
//  graph-server-lib
//
//  Created by <PERSON><PERSON><PERSON> on 1/24/25.
//

import Foundation
import Logging
import SwiftTreeSitter
import TreeSitterTSX

class TypescriptParser: AbstractApiParser {
    
    private func createParser() throws -> Parser {
        let language = Language(tree_sitter_tsx())
        let parser = Parser()
        try parser.setLanguage(language)
        return parser
    }
    
    override public func parse() throws -> Node? {
        let parser = try createParser()
        guard let tree = parser.parse(self.sourceString) else {
            throw ParserError.parsingFailed
        }
        return tree.rootNode
    }
    
    
    func parseFetchURLPieces(underCursorPosition cursorPosition: PointUTF16Z) throws -> [String]? {
        // TODO: choose `tree_sitter_tsx()` or `tree_sitter_typescript()` accordingly
        let language = Language(tree_sitter_tsx())
        let parser = Parser()
        try parser.setLanguage(language)
        let tree = parser.parse(self.sourceString)!
        
//        func recurse(fromNode node: Node) {
//            print("Node \(node.nodeType), pointRange: \(node.pointRange)")
//            node.enumerateChildren { recurse(fromNode: $0) }
//        }
//        recurse(fromNode: tree.rootNode!)
        return parsedFetchURL(root: tree.rootNode!, cursorPosition: cursorPosition)
    }
    
    private func sourceText(atLine zeroBasedLine: UInt32) -> String? {
        let lines = self.sourceString.split(separator: "\n")
        if let nonNilSubstring = lines[maybe: Int(zeroBasedLine)] {
            return String(nonNilSubstring)
        } else {
            return nil
        }
    }
    
    private func strippingCommentDelimiters(_ input: String) -> String {
        if input.hasPrefix("//") {
            // Trim leading double-slash plus any spaces immediately after it
            return String(input.trimmingPrefix(/\/\/\s*/))
        } else if input.hasPrefix("/*") {
            // dropLast(2) to remove closing `*/`
            return String(input.trimmingPrefix(/\/\*\w*/).dropLast(2))
            /*
             * In a block comment, the author might choose to add a line of asterisks
             * in the left margin (like here). We want to remove these in our output.
             */
                .replacing(/[ \t]*\*[ \t]*/, with: "")
                .trimmingCharacters(in: .whitespacesAndNewlines)
        } else {
            return input
        }
    }
    
    func trailingCommentText(onLine zeroBasedLine: UInt32) -> String? {
        let logger = TLog.default(labeled: "TypescriptParser#trailingCommentText")
        // Check for comments at the end of the identified line. Do this by only pulling out the identified line,
        // and running tree-sitter on it. We'll likely get a parse error, but tree-sitter should be robust enough
        // to still pull out any trailing comments as a distinct node.
        guard let sourceLine = self.sourceText(atLine: zeroBasedLine) else {
            logger.info("Could not get source text at line \(zeroBasedLine)")
            return nil
        }
        guard let parser = try? createParser() else {
            logger.info("Could not create parser")
            return nil
        }
        guard let tree = parser.parse(sourceLine) else {
            logger.info("Could not parse sourceLine: \(sourceLine)")
            return nil
        }
        guard let lastChild = tree.rootNode?.lastChild else {
            logger.info("Tree's rootNode or lastChild is nil")
            return nil
        }
        if lastChild.nodeType == "comment" {
            let sourceTextAsNSString = (sourceLine as NSString)
            return strippingCommentDelimiters(lastChild.sourceText(from: sourceTextAsNSString))
        } else {
            logger.info("Last child is not a comment; nodeType=\(String(describing: lastChild.nodeType))")
            return nil
        }
    }
    
    func leadingCommentText(beforeLine zeroBasedLine: UInt32) -> String? {
        let logger = TLog.default(labeled: "TypescriptParser#leadingCommentText")
        // Check for comments before the identified line.
        // Look for the outermost non-comment AST node that *starts* on the identified line
        // (outermost, because *that* AST node may have children that also start on the same line).
        // Look for comments that are immediately-preceding siblings of that outermost node.
        guard let parser = try? createParser() else {
            logger.info("Could not create parser")
            return nil
        }
        guard let tree = parser.parse(self.sourceString) else {
            logger.info("Could not parse sourceString for file: \(self.file.absoluteString)")
            return nil
        }
        var commentNodes: [Node] = []
        var outermostTargetNodeFound: Bool = false
        func recurse(_ node: Node) {
            if outermostTargetNodeFound { return }
            if node.nodeType == "comment" {
                commentNodes.append(node)
            } else {
                let childLine = node.pointRange.lowerBound.row
                if childLine == zeroBasedLine {
                    outermostTargetNodeFound = true
                    return
                } else {
                    // Found a non-comment node that is not our target,
                    // so any previously-found comments are not considered relevant
                    commentNodes = []
                    if childLine < zeroBasedLine {
                        node.enumerateChildren(block: recurse)
                    }
                }
            }
        }
        if let root = tree.rootNode { recurse(root) }
        
        if !commentNodes.isEmpty {
            let commentString = commentNodes
                .map { strippingCommentDelimiters($0.sourceText(from: self.sourceNSString)) }
                .joined(separator: "\n")
            return commentString
        } else {
            return nil
        }
    }
    
    private func parsedFetchURL(root: Node, cursorPosition: PointUTF16Z) -> [String]? {
        var tightestCallExpression: Node? = nil
        var tightestCallExpressionRange: Range<PointUTF16Z>? = nil
        func recurse(fromNode node: Node) {
            switch node.nodeType {
            case "call_expression":
                let nodeRange = Range.fromTreeSitterPointRange(node.pointRange)
                if tightestCallExpressionRange == nil {
                    tightestCallExpression = node
                    tightestCallExpressionRange = nodeRange
                } else if nodeRange.tighter(than: tightestCallExpressionRange!, around: cursorPosition) {
                    tightestCallExpression = node
                    tightestCallExpressionRange = nodeRange
                }
            default:
                break
            }
            node.enumerateChildren { recurse(fromNode: $0) }
        }
        recurse(fromNode: root)
        
        if let callExpression = tightestCallExpression {
            let calledName = callExpression.namedChild(at: 0)?.sourceText(from: self.sourceNSString)
            let fetchURL = callExpression.namedChild(at: 1)?.namedChild(at: 0)?.sourceText(from: self.sourceNSString)
            let parsedFetchURLParts = parseFetchURLParam(fetchURL!)
            return parsedFetchURLParts
        } else {
            return nil
        }
    }
    
    private func parseFetchURLParam(_ param: String) -> [String] {
        // Turn a string like "`http://localhost:4567/hello/${data.name}`"
        // into an array of parts: ["hello", ":"]
        // where ":" denotes some param field
        var isTemplateLiteral = param.hasPrefix("`") && param.hasSuffix("`")
        var param = param
        if (param.hasPrefix("`") && param.hasSuffix("`")) ||
            (param.hasPrefix("'") && param.hasSuffix("'")) ||
            (param.hasPrefix("\"") && param.hasSuffix("\"")) {
            param = String(param.dropFirst().dropLast())
        }
        let urlPath = URL(string: param)!.path
        let parts = urlPath.split(separator: "/")
        if isTemplateLiteral {
            return parts.map {
                String($0.starts(with: "${") ? ":" : $0)
            }
        } else {
            return parts.map { String($0) }
        }
    }
    
    func closestCallExpression(fromRoot root: SwiftTreeSitter.Node, enclosingRange range: Range<PointUTF16Z>) -> Node? {
        let treeSitterRange = range.toTreeSitterPointRange()
        var workingNode = root.descendant(in: treeSitterRange)
        while workingNode != nil {
            if workingNode!.nodeType == "call_expression" {
                return workingNode
            }
            workingNode = workingNode!.parent
        }
        return nil
    }
    
    private func sourceValue(forNode node: SwiftTreeSitter.Node) -> SourceValue {
        var sourceValueKind: SourceValue.Kind
        switch node.nodeType {
        case "arrow_function", "function_expression":
            sourceValueKind = .lambda
        case "identifier", "shorthand_property_identifier":
            sourceValueKind = .identifier
        case "object":
            sourceValueKind = .objectLiteral
        case "string":
            sourceValueKind = .stringLiteral
        default:
            sourceValueKind = .unimplemented
        }
        return SourceValue(kind: sourceValueKind, node: node, parser: self)
    }
    
    func parameters(forCallExpression callExpression: SwiftTreeSitter.Node) -> [MethodParameter] {
        guard let argumentsNode = callExpression.child(byFieldName: "arguments") else { return [] }
        var parameters: [MethodParameter] = []
        var position: Int = 0
        argumentsNode.enumerateChildren { argumentNode in
            guard argumentNode.isNamed else { return }
            let nodeRange = Range.fromTreeSitterPointRange(argumentNode.pointRange)
            let location = CodeLocation(
                compileUnit: self.compileUnit,
                documentPath: self.compileUnit.documentPath(fromAbsoluteFileURL: self.file),
                locationInFile: .range(nodeRange)
            )
            let parameter = MethodParameter(
                identifiedBy: .position(position),
                value: self.sourceValue(forNode: argumentNode),
                location: location
            )
            parameters.append(parameter)
            position += 1
        }
        return parameters
    }
    
    internal func sourceValue(fromObjectNode node: SwiftTreeSitter.Node, atKey key: Substring) -> SourceValue? {
        guard node.nodeType == "object" else { return nil }
        var sourceValue: SourceValue? = nil
        node.enumerateChildren { childNode in
            // Check that sourceValue hasn't been found already
            guard sourceValue == nil else { return }
            
            if childNode.nodeType == "pair" {
                guard (childNode.child(byFieldName: "key")?.sourceText(from: self.sourceNSString) ?? "") == key else { return }
                guard let value = childNode.child(byFieldName: "value") else { return }
                sourceValue = self.sourceValue(forNode: value)
            } else if childNode.nodeType == "shorthand_property_identifier" {
                guard childNode.sourceText(from: self.sourceNSString) == key else { return }
                sourceValue = self.sourceValue(forNode: childNode)
            }
        }
        return sourceValue
    }
}
