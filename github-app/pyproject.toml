[tool.poetry]
name = "github-app"
version = "0.1.0"
description = "Webhook Handler for Github installations"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [
    { include = "src" },
    # This is an important line
    { include = "protos_generated", from = "../all-protos/" }
]


[tool.poetry.dependencies]
python = "^3.10"
botocore = "^1.37.13"
grpcio = "^1.71.0"
grpcio-tools = "^1.71.0"
python-dotenv = "^1.0.1"
fastapi = "^0.115.11"
pygithub = "^2.6.1"
uvicorn = "^0.34.0"
gitpython = "^3.1.44"
infisicalsdk = "^1.0.6"
sentry-sdk = {extras = ["fastapi"], version = "^2.24.0"}
streamlit = "^1.44.1"
fpdf = "^1.7.2"
aiohttp = "^3.11.18"
posthog = "4.0.1"
httpx = "0.28.1"
filelock = "^3.18.0"
aiofiles = "^24.1.0"
aioshutil = "^1.5"
psutil = "^7.0.0"


[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
pytest-asyncio = "^0.21.1"
pre-commit = "^4.2.0"
ipykernel = "^6.29.5"
locust = "^2.37.4"
ruff = "^0.5.4"


[tool.poetry.scripts]
server = "src.main:main_function"
backtest = "src.api.streamlit_app.stream_app:main"

[tool.pytest.ini_options]
testpaths = ["tests"]  # Only look in the tests directory, ignore everything else

[tool.ruff]
indent-width = 2