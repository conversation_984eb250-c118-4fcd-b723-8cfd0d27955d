#!/bin/bash
set -e

echo "Starting GitHub App services..."

# Start the FastAPI server
echo "Starting FastAPI server on port 9000..."
poetry run server &
SERVER_PID=$!

# Start the Streamlit app
echo "Starting Streamlit app on port 8501..."
poetry run backtest &
STREAMLIT_PID=$!

# Handle termination signals
trap "kill $SERVER_PID $STREAMLIT_PID; exit" SIGINT SIGTERM

# Keep the container running
echo "All services started. Container is now running."
wait
