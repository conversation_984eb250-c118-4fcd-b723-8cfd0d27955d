from contextlib import contextmanager
from datetime import datetime
import os
import asyncio
import shutil
import time
import logging

from src.config.AppConfig import CHECKOUTS_PATH

logger = logging.getLogger(__name__)


def timeit(func):
  async def process(func, *args, **params):
    if asyncio.iscoroutinefunction(func):
      print("this function is a coroutine: {}".format(func.__name__))
      return await func(*args, **params)
    else:
      print("this is not a coroutine")
      return func(*args, **params)

  async def helper(*args, **params):
    print("{}.time".format(func.__name__))
    start = time.time()
    result = await process(func, *args, **params)
    print(">>> {:.3f} seconds".format(time.time() - start))
    return result

  return helper


def log_diskspace():
  try:
    total, _used, free = shutil.disk_usage(CHECKOUTS_PATH)
    total_gb = total / (1024**3)
    free_gb = free / (1024**3)
    logger.info(f"Disk space - Total: {total_gb:.2f} GB, Free: {free_gb:.2f} GB")
    return total_gb, free_gb
  except Exception as e:
    logger.error(f"Error checking disk space: {e}")


def format_datetime_to_string(date_input):
  """
  Convert datetime object or ISO format string to "Month Day, Year" format
  Args:
      date_input: datetime object or ISO format string
  Returns:
      str: Formatted date string like "January 1, 2024"
  """
  if isinstance(date_input, str):
    try:
      dt = datetime.fromisoformat(date_input)
    except ValueError as e:
      raise ValueError(f"Invalid date string format: {e}")
  elif isinstance(date_input, datetime):
    dt = date_input
  else:
    raise ValueError("Input must be a datetime object or ISO format string")

  return dt.strftime("%B %d, %Y")


def is_running_in_pytest():
  # https://docs.pytest.org/en/latest/example/simple.html#pytest-current-test-env
  return "PYTEST_CURRENT_TEST" in os.environ


def magic_github_user_for_pytest():
  return "allowed-user"


def magic_github_app_id_for_pytest():
  return "TEST_GITHUB_APP_ID"


def magic_installation_id_for_pytest():
  return -1


def magic_installation_token_for_pytest():
  return "TEST_INSTALLATION_TOKEN"


@contextmanager
def set_context(context_var, value):
  token = context_var.set(value)
  try:
    yield
  finally:
    context_var.reset(token)
