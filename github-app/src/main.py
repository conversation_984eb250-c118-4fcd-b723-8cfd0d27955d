import logging
from fastapi import FastAP<PERSON>
import uvicorn

from src.config.AppConfig import ENVIRONMENT
from src.config.Middlewares import RequestIdMiddleware
from src.api.Routes import Router
from src.api.DemoRoutes import DemoRouter
from src.api.AuthUtils import setup_auth_routes

from src.config.SentryConfig import configure_monitoring
from src.services.Metrics.MetricsService import MetricsService


configure_monitoring()
logger = logging.getLogger(__name__)


# Initialize FastAPI app
app = FastAPI(
  title="PR Analyzer",
  docs_url=None,  # Disable default /docs endpoint
  redoc_url=None,  # Disable default /redoc endpoint
)
app.include_router(Router)
app.include_router(DemoRouter)
app.add_middleware(RequestIdMiddleware)
setup_auth_routes(app)


def main_function():
  MetricsService().put_metric_single_count("github_app.main_function", {})
  logger.info('Debugger on for the python app, "github-app"')
  if ENVIRONMENT == "dev":
    uvicorn.run("src.main:app", host="0.0.0.0", port=9000, reload=True)
  else:
    uvicorn.run("src.main:app", host="0.0.0.0", port=9000, workers=4)


if __name__ == "__main__":
  main_function()
