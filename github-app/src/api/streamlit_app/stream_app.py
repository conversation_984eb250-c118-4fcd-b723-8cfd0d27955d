import sys
from collections import defaultdict
import asyncio
from typing import List, Dict, Any
import streamlit as st
import streamlit.web.cli as stcli
from github.Repository import Repository as GithubRepository
import logging

from src.core.commands.policies.SoftDeletePropertyRule import SoftDeletePropertyRule
from src.api.streamlit_app.helpers.diff_render import render_diff_html
from src.api.streamlit_app.helpers.auth import check_password
from src.api.streamlit_app.helpers.constants import (
  PAGE_DIFF_ANALYZE,
  PAGE_SEARCH,
  PAGE_REPO_EXPLORER,
)
from src.api.streamlit_app.helpers.pdf_generator import add_analysis_download_button
from src.api.streamlit_app.search_pr import search_view
from src.api.streamlit_app.repo_explorer import repo_explorer_view
from src.core.commands.policies.LLMBasedPolicy import LLMBasedPolicy
from src.core.commands.policies.TRPCDestructuringRule import TRPCDestructuringRule
from src.services.GithubService import GithubService
from src.api.streamlit_app.analysis_executor import AnalysisExecutor
from src.api.streamlit_app.models.file_selection import FileMetaData, SelectedFiles
from src.core.commands.policies import (
  AxiosConfigRule,
  ApiCallHierarchy,
  TodoCreateTicket,
  CreateIndexRule,
  SafeTableOperationsRule,
  UpdateTable,
  RunwayAsTeamIdRule,
  RunwayApiInterfaceRule,
  RunwayPrivateInTeamRule,
)
from src.core.commands.policies.NoHardCodedColors import NoHardcodedColors


logger = logging.getLogger(__name__)


async def run_analysis(diff_payload: Dict[str, Any], policies: List) -> Dict[str, Any]:
  """Async function to run the analysis"""
  if len(policies) == 0:
    st.warning("No policies found / selected")
    return {"error": "No policies found / selected"}
  logger.info(f"Starting analysis for PR #{diff_payload['pr_number']}")
  executor = AnalysisExecutor()
  result = await executor.process_pull_request(diff_payload, policies)
  logger.info(f"Completed analysis for PR #{diff_payload['pr_number']}")
  return result


async def run_all_analyses(all_diffs, policies):
  logger.info("Creating analysis tasks")
  # Create a thread pool for CPU-bound tasks
  loop = asyncio.get_event_loop()
  tasks = [
    loop.run_in_executor(
      None, lambda d=diff_payload: asyncio.run(run_analysis(d, policies))
    )
    for diff_payload in all_diffs
  ]
  return await asyncio.gather(*tasks)


# ----
# -- Main APP
# ----


def app():
  if not check_password():
    st.stop()  # Do not continue if check_password is not True

  st.set_page_config(layout="wide")

  # Initialize view state
  if "current_view" not in st.session_state:
    st.session_state.current_view = PAGE_DIFF_ANALYZE

  # Sidebar navigation
  with st.sidebar:
    if st.button("Diff-Analysis", use_container_width=True):
      st.session_state.current_view = PAGE_DIFF_ANALYZE
      st.rerun()
    if st.button("Search PRs", use_container_width=True):
      st.session_state.current_view = PAGE_SEARCH
      st.rerun()
    if st.button("Repo Explorer", use_container_width=True):
      st.session_state.current_view = PAGE_REPO_EXPLORER
      st.rerun()

  # Main content
  if st.session_state.current_view == PAGE_SEARCH:
    search_view()
  elif st.session_state.current_view == PAGE_REPO_EXPLORER:
    repo_explorer_view()
  else:
    diff_analysis_view()


def diff_analysis_view():
  st.title("GitHub PR Diff Analysis")
  st.write("", "")

  # Initialize session state for PR inputs and current page
  if "pr_inputs" not in st.session_state:
    st.session_state.pr_inputs = [{"pr_number": "1", "commit_hash": ""}]
  if "current_diff_page" not in st.session_state:
    st.session_state.current_diff_page = 0
  if "diff_loaded" not in st.session_state:
    st.session_state.diff_loaded = False
  if "llm_policy_list" not in st.session_state:
    st.session_state.llm_policy_list = [""]
  if "policy_classes" not in st.session_state:
    st.session_state.policy_classes = []
  if "all_diffs" not in st.session_state:
    st.session_state.all_diffs = []
  if "analysis_results" not in st.session_state:
    st.session_state.analysis_results = []

  with st.container():
    col1, col2 = st.columns(2)
    with col1:
      installation_id = st.text_input(
        "Installation ID",
        value="64320692",
        placeholder="Enter installation ID",
      )
      st.session_state.last_installation_id = installation_id
    with col2:
      repo_name = st.text_input(
        "Repository",
        value="runwayml/api",
        placeholder="owner/repository",
      )
      st.session_state.last_repo_name = repo_name
    # PR inputs container
    st.subheader("Pull Requests")
    for i, pr_input in enumerate(st.session_state.pr_inputs):
      col1, col2, col3 = st.columns([1, 4, 3])
      with col1:
        pr_number = st.text_input(
          "PR Number",
          value=pr_input["pr_number"],
          key=f"pr_{i}",
          placeholder="#PR number",
        )
        st.session_state.pr_inputs[i]["pr_number"] = pr_number
      with col2:
        commit_hash = st.text_input(
          " ",
          value=pr_input["commit_hash"],
          key=f"commit_{i}",
          placeholder="Enter specific commit hash",
        )
        st.session_state.pr_inputs[i]["commit_hash"] = commit_hash
        st.caption("↑ Commit Hash (optional)")
      with col3:
        pass

    len_pr_inputs = len(st.session_state.pr_inputs)
    button_col1, button_col2, _ = st.columns([2, 1, 9])
    with button_col1:
      if st.button("+ Add PR", key="add_pr", help="Add PR input"):
        st.session_state.pr_inputs.append({"pr_number": "", "commit_hash": ""})
        st.rerun()
    with button_col2:
      if len_pr_inputs > 1:
        if st.button(
          "−", key=f"remove_pr_{len_pr_inputs - 1}", help="Remove this PR input"
        ):
          st.session_state.pr_inputs.pop(len_pr_inputs - 1)
          st.rerun()

  # --------- Fetch and display diffs
  if st.button("View Diffs", use_container_width=True):
    if (
      installation_id
      and repo_name
      and any(pr["pr_number"] for pr in st.session_state.pr_inputs)
    ):
      try:
        github_service = GithubService(int(installation_id))
        _git = github_service._github
        repo = _git.get_repo(repo_name)

        # Clear previous diffs and analysis results
        st.session_state.all_diffs = _fetch_all_diffs_and_prepare_for_analysis(
          repo, installation_id, st.session_state.pr_inputs
        )

        st.session_state.diff_loaded = True
        st.session_state.current_diff_page = 0

      except Exception as e:
        print(f"Error: {str(e)}")
        st.error(f"Error: {str(e)}")
    else:
      st.warning(
        "Please fill in Installation ID, Repository name, and at least one PR number"
      )

  # --------- Display diffs with pagination
  if st.session_state.diff_loaded and st.session_state.all_diffs:
    st.write("", "")  # Add two empty lines for spacing
    st.write("", "")  # Add two empty lines for spacing
    current_diff = render_PR_diff_header_with_pagination(
      st.session_state.all_diffs, key="diff"
    )

    # Add View Raw Diff button (centered)
    col1, col2, col3 = st.columns([2, 1, 2])
    with col2:
      if st.button(
        "View Raw Diff", help="View raw diff content", use_container_width=True
      ):
        raw_diff_content = extract_raw_diff_content(current_diff)
        show_raw_diff_dialog(raw_diff_content)

    # Initialize file selection state if not exists
    if "selected_files" not in st.session_state:
      st.session_state.selected_files = defaultdict(SelectedFiles)

    # Display current diff
    for file in current_diff["files"]:
      col1, col2 = st.columns([0.05, 0.95])
      # Checkbox column
      with col1:
        # Handle file selection with a separate function for better readability
        file_name = file["filename"]
        current_pr, current_commit_hash = (
          current_diff["pr_number"],
          current_diff["pull_request"].get("head", {}).get("sha", ""),
        )
        handle_file_selection(current_pr, current_commit_hash, file_name)

      # File column
      with col2:
        diff_html = render_diff_html(file["filename"], file["patch"])
        st.markdown(diff_html, unsafe_allow_html=True)

  # --------- Show policy configuration after successful diff load
  if st.session_state.diff_loaded:
    llm_policy_list, policy_classes = render_LLM_policy_config()

    # Run Analysis button
    if st.button("Run Analysis", type="primary", use_container_width=True):
      if llm_policy_list or policy_classes:
        try:
          with st.spinner("Running analysis..."):
            # Create policy instances
            policies = gather_policies(llm_policy_list, policy_classes)
            # Run analysis for PRs
            st.session_state.analysis_results = []
            formatted_results_list = asyncio.run(
              run_all_analyses(st.session_state.all_diffs, policies)
            )
            st.session_state.analysis_results = [
              {
                "pr_number": diff["pr_number"],
                "pull_request": diff["pull_request"],
                "results": results,
              }
              for diff, results in zip(
                st.session_state.all_diffs, formatted_results_list
              )
              if "error" not in results
            ]
        except Exception as e:
          st.error(f"Error running analysis: {str(e)}")
      else:
        st.warning("Please add at least one policy")

  # --------- Display analysis results with pagination
  if (
    hasattr(st.session_state, "analysis_results") and st.session_state.analysis_results
  ):
    st.write("", "")  # Add spacing
    st.markdown(
      """
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 10px; border: 1px solid #dee2e6;">
            <h2 style="color: #2c3e50; font-size: 24px; margin-bottom: 20px; text-align: center;">
                Analysis Results
                <span style="display: block; font-size: 16px; color: #6c757d; margin-top: 5px;">
                    Review findings and suggestions
                </span>
            </h2>
        </div>
        """,
      unsafe_allow_html=True,
    )

    st.write("", "")  # Add spacing

    # Get current analysis results with pagination
    current_analysis = render_PR_diff_header_with_pagination(
      st.session_state.analysis_results, key="analysis"
    )

    # Create a container for the analysis content
    with st.container():
      st.markdown(
        """
            <div style="background-color: white; padding: 20px; border-radius: 8px; border: 1px solid #eaecef;">
            """,
        unsafe_allow_html=True,
      )

      formatted_result = current_analysis["results"]
      render_analysis_summary(formatted_result)

    st.write("", "")  # Add spacing at the bottom


# ---------
# --------- Helper functions
# ---------


def _fetch_all_diffs_and_prepare_for_analysis(
  repo: GithubRepository, installation_id, sessions_state_pr_inputs
):
  result_all_diffs = []
  repo_name = repo.full_name
  repo_data = {
    "full_name": repo_name,
    "name": repo_name.split("/")[1],
    "owner": {"login": repo_name.split("/")[0]},
    "clone_url": repo.clone_url,
    "html_url": repo.html_url,
  }
  for pr_input in sessions_state_pr_inputs:
    if pr_input["pr_number"]:
      pr = repo.get_pull(int(pr_input["pr_number"]))
      commit_hash = pr_input["commit_hash"]
      if commit_hash:
        files = repo.get_commit(commit_hash).files
      else:
        files = pr.get_files()

      # Keys match the original payload from Github for "pull_request"
      pr_data = {
        "number": pr.number,
        "base": {"ref": pr.base.ref, "sha": pr.base.sha},
        "head": {
          "ref": pr.head.ref,
          "sha": commit_hash if commit_hash else pr.head.sha,
        },
        "title": pr.title,
        "created_at": pr.created_at,
        "html_url": pr.html_url,
        "user": {"login": pr.user.login},
      }
      # This is the data we are building for ourselves
      diff_data = {
        "installation": {
          "id": int(installation_id)
        },  # Key Matches the original payload from Github
        "pull_request": pr_data,  # Key Matches the original payload from Github
        "pr_number": pr_input["pr_number"],
        "pr_author": pr.user.login,
        "files": [{"filename": f.filename, "patch": f.patch} for f in files],
        "is_final_pr_commit": False if commit_hash else True,
        "repository": repo_data,  # Key Matches the original payload from Github
      }
      result_all_diffs.append(diff_data)
  return result_all_diffs


def render_PR_diff_header_with_pagination(all_diff_pages, key):
  # all_diff_pages must have a key 'pr_number'

  current_page = all_diff_pages[st.session_state.current_diff_page]

  # Pagination controls
  col1, col2, col3 = st.columns([1, 9, 1])
  with col1:
    if st.button(
      "←", disabled=st.session_state.current_diff_page == 0, key=f"prev_page_{key}"
    ):
      st.session_state.current_diff_page -= 1
      st.rerun()
  with col2:
    st.markdown(
      f"""
            <div style="text-align: center; font-size: 16px; color: #333; background-color: #f0f0f0; padding: 10px; border-radius: 5px;">
                <b>
                    <a href="{current_page.get("pull_request", {}).get("html_url", "#")}/files" target="_blank" style="font-size: 20px;">
                    PR #{current_page["pr_number"]}
                    </a>
                </b>
                <br>
                <span style="font-size: 14px; color: #666;">
                    ({st.session_state.current_diff_page + 1} of {len(all_diff_pages)} diffs)
                </span>
            </div>
            """,
      unsafe_allow_html=True,
    )
  with col3:
    if st.button(
      "→",
      disabled=st.session_state.current_diff_page == len(all_diff_pages) - 1,
      key=f"next_page_{key}",
    ):
      st.session_state.current_diff_page += 1
      st.rerun()
  return current_page


def render_analysis_summary(formatted_results):
  # Display summary
  st.markdown(formatted_results["summary"])

  # Display each file's diff with its inline comments
  current_diff = st.session_state.all_diffs[st.session_state.current_diff_page]
  for file in current_diff["files"]:
    file_comments = []
    # Filter comments for this specific file
    for comment in formatted_results.get("inline_comments", []):
      if comment["file"] == file["filename"]:
        file_comments.append({"line": comment["line"], "text": comment["comment"]})
    if file_comments:  # Only show files that have comments
      diff_html = render_diff_html(
        file["filename"], file["patch"], inline_comments=file_comments
      )
      st.markdown(diff_html, unsafe_allow_html=True)
  st.markdown("</div>", unsafe_allow_html=True)

  st.write("", "")  # Add spacing
  # Add download button after analysis summary
  if hasattr(st.session_state, "analysis_results"):
    add_analysis_download_button(
      st.session_state.analysis_results, st.session_state.all_diffs
    )

  # --- Display inline comments
  if formatted_results["inline_comments"]:
    st.markdown("### Inline Comments")
    for comment in formatted_results["inline_comments"]:
      st.markdown(f"""
            **File:** `{comment["file"]}` (Line {comment["line"]})
            ```
            {comment["comment"]}
            ```
            ---
            """)
  else:
    pass


def render_LLM_policy_config():
  st.write("", "", "")  # Add two empty lines for spacing
  st.markdown("---")
  st.write("", "")  # Add two empty lines for spacing
  st.subheader("Configure Policies")
  # LLM-based policies input
  st.markdown("### LLM-based Policies")

  # Add new LLM policy button
  if st.button("+ Add LLM Policy", key="+ Add LLM Policy"):
    st.session_state.llm_policy_list.append("")

  # Display existing LLM policy inputs with remove buttons
  for i in range(len(st.session_state.llm_policy_list)):
    col1, col2 = st.columns([5, 1])
    with col1:
      st.session_state.llm_policy_list[i] = st.text_area(
        f"LLM Policy {i + 1}",
        value=st.session_state.llm_policy_list[i],
        placeholder="e.g., Check for hardcoded secrets",
        height=160,
      )
    with col2:
      if len(st.session_state.llm_policy_list) > 1 and st.button(
        "−", key=f"remove_llm_{i}"
      ):
        st.session_state.llm_policy_list.pop(i)
        st.rerun()

  # Additional policy classes
  st.markdown("### Class Policies")

  # Add new policy class button
  if st.button("+ Add Policy Class", key="+ Add Policy Class"):
    st.session_state.policy_classes.append("")

  # Display existing policy class inputs with remove buttons
  for i in range(len(st.session_state.policy_classes)):
    col1, col2 = st.columns([5, 1])
    with col1:
      st.session_state.policy_classes[i] = st.text_input(
        f"Policy Class {i + 1}",
        value=st.session_state.policy_classes[i],
        placeholder="e.g., ApiCallHierarchy",
      )
    with col2:
      if st.button("−", key=f"remove_{i}"):
        st.session_state.policy_classes.pop(i)
        st.rerun()

  return st.session_state.llm_policy_list, st.session_state.policy_classes


def gather_policies(llm_policies_list, policy_classes):
  policies = []
  # Add LLM policies
  llm_policies = [policy for policy in llm_policies_list if policy.strip()]
  if llm_policies:
    policies.append(LLMBasedPolicy(llm_policies))
  # Add other policy instances
  other_policy_instances = create_policy_instances(policy_classes)
  policies.extend(other_policy_instances)
  return policies


def create_policy_instances(policy_class_names: list[str]) -> list:
  """
  Creates policy instances from a list of policy class names.
  Handles case-insensitive matching of policy names.
  Args:
      policy_class_names: List of policy class names as strings
  Returns:
      List of instantiated policy objects
  """
  policy_mapping = {
    "nohardcodedcolors": NoHardcodedColors,
    "axiosconfigrule": AxiosConfigRule,
    "createindexrule": CreateIndexRule,
    "safetableoperationsrule": SafeTableOperationsRule,
    "trpc": TRPCDestructuringRule,
    "soft": SoftDeletePropertyRule,
    "updatetable": UpdateTable,
    "apicallhierarchy": ApiCallHierarchy,
    "todocreateticket": TodoCreateTicket,
    "llmbasedpolicy": LLMBasedPolicy,
    "runwayasteamid": RunwayAsTeamIdRule,
    "runwayapiinterface": RunwayApiInterfaceRule,
    "runwayprivateinteam": RunwayPrivateInTeamRule,
  }
  # Create a lowercase to Actual Name
  display_names = {
    "nohardcodedcolors": "NoHardcodedColors",
    "axiosconfigrule": "AxiosConfigRule",
    "createindexrule": "CreateIndexRule",
    "safetableoperationsrule": "SafeTableOperationsRule",
    "trpc": "TRPC",
    "soft": "SOFT",
    "updatetable": "UpdateTable",
    "apicallhierarchy": "ApiCallHierarchy",
    "todocreateticket": "TodoCreateTicket",
    "llmbasedpolicy": "LLMBasedPolicy",
    "runwayasteamid": "RunwayAsTeamId",
    "runwayapiinterface": "RunwayAPIInterface",
    "runwayprivateinteam": "RunwayPrivateInTeam",
  }
  policies = []
  for policy_class_name in policy_class_names:
    if not policy_class_name.strip():
      continue
    normalized_name = policy_class_name.lower().strip()
    try:
      if normalized_name in policy_mapping:
        policy_module = policy_mapping[normalized_name]
        # policy_class = getattr(policy_module, display_names[normalized_name])  #If policy_module is the file & not class, then this is used
        policies.append(policy_module())
      else:
        st.warning(
          f"Policy class '{policy_class_name}' not found. Available policies: "
          f"{', '.join(display_names.values())}"
        )
    except Exception as e:
      st.error(f"Error initializing policy '{policy_class_name}': {str(e)}")
  return policies


def handle_file_selection(current_pr: str, current_commit_hash: str, file_name: str):
  """
  Handle the file selection checkbox and state management

  Args:
      current_pr: The current PR number
      file_name: The file name being processed
  """
  checkbox_key = f"file_select_{current_pr}_{current_commit_hash[:7]}_{file_name}"
  if current_commit_hash:
    checkbox_key += f"_{current_commit_hash}"

  # Initialize the array for this PR if it doesn't exist
  if current_pr not in st.session_state.selected_files:
    st.session_state.selected_files[current_pr] = SelectedFiles()

  selectedFiles_obj = st.session_state.selected_files[current_pr]

  # Check if this file is already selected
  initial_value = selectedFiles_obj.get(file_name) is not None

  # Set initial checkbox value
  is_selected = st.checkbox("", key=checkbox_key, value=initial_value)
  file_metadata_obj = selectedFiles_obj.get(file_name) or FileMetaData(file_name)

  # Update selection state
  if is_selected:
    render_file_selection_options(current_pr, file_metadata_obj)
    if selectedFiles_obj.get(file_name) is None:
      selectedFiles_obj.add(file_metadata_obj)

  else:
    # Remove file from selected_files if unchecked
    if selectedFiles_obj.get(file_name) is not None:
      selectedFiles_obj.discard(file_metadata_obj)

  st.session_state.selected_files[current_pr] = selectedFiles_obj


def render_file_selection_options(current_pr: str, file_metadata: FileMetaData):
  """
  Render UI elements for file selection options (line range and explanation)

  Args:
      current_pr: The current PR number
      file_metadata: The FileSelection object to update
  """
  with st.container():
    file_name = file_metadata.file_name

    # From line input
    from_line = st.text_input(
      "From line",
      value=str(file_metadata.line_range[0]) if file_metadata.line_range else "",
      key=f"from_line_{current_pr}_{file_name}",
      placeholder="Start line",
    )
    # To line input
    to_line = st.text_input(
      "To line",
      value=str(file_metadata.line_range[1]) if file_metadata.line_range else "",
      key=f"to_line_{current_pr}_{file_name}",
      placeholder="End line",
    )

    # Update line range in the FileSelection object
    if from_line and to_line:
      try:
        file_metadata.line_range = (int(from_line), int(to_line))
      except ValueError:
        st.warning("Line numbers must be integers")

    # Add explanation button that toggles the explanation popup
    button_label = "\+" if file_metadata.explanation is None else "−"
    if st.button(button_label, key=f"add_explanation_{current_pr}_{file_name}"):
      # Toggle the explanation popup state
      open_explanation_dialog(current_pr, file_metadata)


@st.dialog("Add Explanation")
def open_explanation_dialog(current_pr: str, file_metadata: FileMetaData):
  dialog_key = f"explanation_dialog_{current_pr}_{file_metadata.file_name}"

  explanation_input = st.text_area(
    "Explanation", value=file_metadata.explanation, key=f"textarea_{dialog_key}"
  )

  if st.button("Save"):
    file_metadata.explanation = explanation_input
    st.success("Explanation saved.")
    st.rerun()


def extract_raw_diff_content(diff_data: Dict[str, Any]) -> str:
  """
  Extract raw diff content from the current PR's diff data.

  Args:
      diff_data: Dictionary containing PR diff information with 'files' key

  Returns:
      String containing all raw diff content for the PR
  """
  raw_diff_lines = []

  # Add PR header information
  pr_number = diff_data.get("pr_number", "Unknown")
  pr_title = diff_data.get("pull_request", {}).get("title", "")
  raw_diff_lines.append(f"PR #{pr_number}: {pr_title}")
  raw_diff_lines.append("=" * 80)
  raw_diff_lines.append("")

  # Process each file in the diff
  for file_data in diff_data.get("files", []):
    filename = file_data.get("filename", "unknown_file")
    patch = file_data.get("patch", "")

    # Add file header
    raw_diff_lines.append(f"diff --git a/{filename} b/{filename}")
    raw_diff_lines.append(f"--- a/{filename}")
    raw_diff_lines.append(f"+++ b/{filename}")

    # Add the actual patch content
    if patch:
      raw_diff_lines.append(patch)
    else:
      raw_diff_lines.append("(No changes in this file)")

    raw_diff_lines.append("")  # Add spacing between files

  return "\n".join(raw_diff_lines)


@st.dialog("Raw Diff Content")
def show_raw_diff_dialog(raw_diff_content: str):
  """
  Show the raw diff content in a dialog for easy copying.

  Args:
      raw_diff_content: The raw diff content to display
  """
  st.markdown(
    "Click the copy button in the top-right corner of the code block to copy:"
  )
  st.code(raw_diff_content, language="diff")

  if st.button("Close"):
    st.rerun()


# ---- Function to run from poetry script
def main():
  sys.argv = [
    "streamlit",
    "run",
    "src/api/streamlit_app/stream_app.py",
    "--server.headless=true",
  ]
  sys.exit(stcli.main())


if __name__ == "__main__":
  app()
