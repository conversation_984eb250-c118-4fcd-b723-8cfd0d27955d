import streamlit as st
from src.config.AppConfig import get_secret


def check_password():
  """Returns `True` if the user had the correct password."""

  def password_entered():
    """Checks whether a password entered by the user is correct."""
    original_pass = get_secret("STREAMLIT_PASSWORD", "tan-tan")
    if st.session_state["password"] == original_pass:
      st.session_state["password_correct"] = True
      del st.session_state["password"]  # Clear password
    else:
      st.session_state["password_correct"] = False

  if "password_correct" not in st.session_state:
    # First run, show input for password
    st.text_input(
      "Password", type="password", on_change=password_entered, key="password"
    )
    return False
  elif not st.session_state["password_correct"]:
    # Password incorrect, show input + error
    st.text_input(
      "Password", type="password", on_change=password_entered, key="password"
    )
    st.error("😕 Password incorrect")
    return False
  else:
    # Password correct
    return True
