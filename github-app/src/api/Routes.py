import logging
import traceback
from fastapi import <PERSON><PERSON>out<PERSON>, BackgroundTasks, Request, Depends
from fastapi.responses import JSONResponse, RedirectResponse
from datetime import datetime

from src.services.GithubService import GithubService
from src.services.Metrics.MetricsService import MetricsService
from src.api.WebhookHandler import WebhookHandler
from src.util.utils import log_diskspace
from src.api.AuthUtils import verify_password

logger = logging.getLogger(__name__)

# Create router
Router = APIRouter()


@Router.get("/")
async def root():
  """Root endpoint."""
  return {"status": "app found", "timestamp": datetime.now().isoformat()}


@Router.get("/health")
async def health(request: Request, background_tasks: BackgroundTasks):
  """Health check endpoint."""
  logger.info("Health checkpoint!")

  # Check graph-server connection
  graph_server_status = "unavailable"
  try:
    from src.services.GraphLibService import GraphLibService
    import requests

    graph_service = GraphLibService()
    response = requests.get(
      f"{graph_service.base_url}/policies/ping",
      headers=graph_service.headers,
      timeout=2,
    )
    if response.status_code == 200:
      graph_server_status = "available"
  except Exception as e:
    logger.warning(f"Error connecting to graph-server: {e}")

  return {
    "status": "healthy",
    "timestamp": datetime.now().isoformat(),
    "dependencies": {"graph-server": graph_server_status},
  }


@Router.get("/sentry-debug")
async def trigger_error():
  try:
    test()
  except Exception as e:
    logger.error(f"Caught an Error: {e}")
    return JSONResponse(status_code=404, content={"message": "Internal server error"})


def test():
  logger.info("About to raise an exception")
  raise Exception("hi this is an error")


@Router.post("/webhook/github")
async def github_webhook(request: Request, background_tasks: BackgroundTasks):
  """GitHub webhook endpoint."""
  try:
    total_gb, free_gb = log_diskspace()
    m = MetricsService()
    m.put_metric_single_count(
      "github_app.disk_space",
      {"total_gb": total_gb, "free_gb": free_gb},
    )
    return await WebhookHandler.handle_github_webhook(request, background_tasks)
  except Exception as e:
    tb = traceback.format_exc()
    logger.error(f"Error handling GitHub webhook: {e}\n{tb}")
    status_code = e.status_code if hasattr(e, "status_code") else 500
    return JSONResponse(
      status_code=status_code, content={"message": "Internal server error"}
    )


@Router.get("/user-installations")
async def list_installations(username: str = Depends(verify_password)):
  installations = await GithubService().get_installations()
  return {"installations": installations}


@Router.get("/dev/backtest")
async def diff_viewer(request: Request):
  from urllib.parse import urlparse

  def redirect_to_streamlit(request: Request, streamlit_port: int):
    url_parts = urlparse(str(request.url))
    new_netloc = f"http://{url_parts.hostname}:{streamlit_port}"
    return RedirectResponse(url=new_netloc)

  STREAMLIT_PORT = 8501
  return redirect_to_streamlit(request, STREAMLIT_PORT)
