import logging
from typing import Any, Dict

from src.core.PolicyConfig import PolicyConfig, PolicyResponse
from src.core.commands.CommandInterface import CommandInterface
from src.core.commands.CommandExecuteSubject import CommandExecuteSubject
from src.core.commands.InlineDiffComment import InlineDiffComment
from src.core.commands.RunRule import RunRule
from src.services.GithubService import GithubService

logger = logging.getLogger(__name__)


class RunwayPrivateInTeamRule(CommandInterface):
  @staticmethod
  def rule_as_string() -> str:
    return "`find` operations on TaskArtifact and Dataset that includes `userId` should also include `privateInTeam`"

  def __init__(self, custom_inline_message=None, notification_manager=None):
    super().__init__(notification_manager=notification_manager)
    self._github_service = None
    self._custom_inline_message = custom_inline_message

  @property
  def policy_config(self) -> PolicyConfig:
    return PolicyConfig(rule_needs_scip_index=True)

  async def execute(self, subject: CommandExecuteSubject) -> PolicyResponse:
    self._github_service = GithubService(subject.installation_id)
    result = await RunRule(
      "RunwayPrivateInTeamRule", subject, self.policy_config
    ).process()
    return await self._process_result(result)

  async def _process_result(self, result: Dict[str, Any]) -> PolicyResponse:
    is_violation, error, summary = False, False, ""
    inline_diff_comments = []
    if result.get("status", None) == "success":
      match_locations = result.get("rule_results", {}).get("matchLocations", [])
      if len(match_locations) == 0:
        summary = "All `find` operations on `TaskArtifact` and `Dataset` that include `userId` also specify `privateInTeam`."
      else:
        is_violation, summary = (
          True,
          "Some `find` operations that include `userId` are missing `privateInTeam`; see inline comments on diff lines.",
        )
        default_message = (
          "Some `find` operations that include `userId` are missing `privateInTeam`."
        )

        for match in match_locations:
          file_path = match.get("filePath", "")
          one_based_line = match.get("zeroBasedLine", -1) + 1
          if len(file_path) > 0 and one_based_line > 0:
            inline_diff_comments.append(
              InlineDiffComment(
                file_path,
                one_based_line,
                self._custom_inline_message
                if self._custom_inline_message is not None
                else default_message,
              )
            )
          else:
            logger.info(
              f"Match didn't have a non-zero file_path or one_based_line: {match}"
            )
    else:
      error = True
      summary = "Something went wrong processing this PR — please let us know!"

    return PolicyResponse(
      is_violation=is_violation,
      is_error=error,
      summary_comment=summary,
      inline_diff_comments=inline_diff_comments,
    )
