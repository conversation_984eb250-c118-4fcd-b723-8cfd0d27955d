from textwrap import dedent


def get_policy_prompts():
  policies = []

  # policies.append(get_trpc_prompt())
  policies.append(get_non_nullable_columns_prompt())
  policies.append(get_foreign_key_prompt())
  policies.append(concurrencyLock_prompt())
  return policies


def get_trpc_prompt():
  return dedent("""
        tRPC Hook Usage for useQuery or useMutation API methods

        VIOLATION PATTERN:
        - Code where tRPC hooks are destructured directly in declaration using { } syntax
        - ONLY flag: const/let { anything } = trpc.*.use(Query|Mutation)();

        NON-VIOLATION PATTERN:
        - Code where full tRPC hook results are assigned to variables
        - DO NOT flag: const/let variableName = trpc.*.use(Query|Mutation)();

        REASON: Destructuring in declarations prevents access to important metadata like loading states and errors.

        IMPORTANT: Only report lines with destructuring brackets { } in the declaration. Only report for the methods using `useQuery()` or `useMutation()` methods.
        """)


def get_foreign_key_prompt():
  return dedent("""
        POLICY: Database Migration - Foreign Keys

        VIOLATION PATTERN:
        - Operations that ALTER a table TO ADD a foreign key constraint
        - Operations that CREATE a new table and add foreign key/s constraint
        - Parameter consisting of "foreignKeys"
        
        Examples of violations:
        - ALTER TABLE "existing_table" ADD CONSTRAINT "fk_name" FOREIGN KEY ("column_name") REFERENCES "target_table" ("target_column");
        - queryRunner.createForeignKey("existing_table", new TableForeignKey({...}));
        - await queryRunner.query(`ALTER TABLE "existing_table" ADD FOREIGN KEY ("column_name") REFERENCES "target_table" ("target_column")`);

        NON-VIOLATION PATTERN:
        - Dropping foreign keys
        """)


def get_non_nullable_columns_prompt():
  return dedent("""
        POLICY: Database Migration - Non-nullable Columns on existing tables

        VIOLATION PATTERN:
        - Operations that add non-nullable columns without defaults to PREVIOUSLY EXISTING tables
        - Must meet ALL these conditions:
        1. Modifies a table that existed BEFORE the current migration
        2. Adds one or more new columns
        3. Makes those columns non-nullable (NOT NULL)
        4. Does not provide default values

        NON-VIOLATION PATTERN:
        - Adding non-nullable columns to tables created IN THE SAME MIGRATION
        - CREATE TABLE statements
        - DROP COLUMN statements
        - ADD COLUMN with nullable columns
        - ADD COLUMN with NOT NULL AND DEFAULT

        REASON: This policy prevents migration failures that occur when adding non-nullable columns without defaults to tables that contain existing data. Tables created in the same migration file are guaranteed to be empty when columns are added to them, so they're exempt from this policy.

        DETECTION LOGIC:
        1. If a CREATE TABLE and ALTER TABLE for the same table appear in the same migration file, operations on that table are exempt
        2. If only ALTER TABLE appears (indicating the table existed before), then check for violations
        3. Look specifically for the sequence of: ALTER TABLE → existing_table → ADD COLUMN → column_name → NOT NULL without DEFAULT
        4. Look for the sequence: queryRunner.addColumn → isNullable → false
            """)


def privateInTeam_prompt():
  return dedent("""
        POLICY: TaskArtifact and Dataset Query Requirements

        A code snippet is a violation if and ONLY if ALL THREE conditions are met:
        1. The exact string "TaskArtifact" OR the exact string "Dataset" appears as the model name
        2. The query includes "userId:" as a parameter 
        3. The query does NOT include "privateInTeam:" anywhere

        DO NOT report violations based on partial matches or pattern recognition.

        FALSE POSITIVE EXAMPLES (NOT VIOLATIONS):
        - CustomPreset.existsBy({ userId: team.id }) - NOT a violation because model is not TaskArtifact or Dataset
        - TaskArtifact.find({ name: "test" }) - NOT a violation because userId is not in the query
        - Dataset.find({ userId: id, privateInTeam: false }) - NOT a violation because privateInTeam IS included
        - TaskArtifact.find({ where: { userId: USER_ID, privateInTeam: true } }) - NOT a violation
        - TaskArtifact.find({ where: { userId: USER_ID, deleted: true } }) - THIS IS A VIOLATION (meets all conditions)

        IMPORTANT: For each potential violation, explicitly check all three conditions before reporting.
    """)


def concurrencyLock_prompt():
  return dedent("""
        POLICY: ConcurrencyLock for Database Operations

        A code snippet is a violation if and ONLY if ALL THREE conditions are met:
        1. The function contains BOTH a query operation (findBy, getBy, find, findOne, getOne, getOneBy, findOneBy, etc.) AND a write operation (create, save, update, delete, etc.)
        2. These operations appear to be operating on the same entity
        3. No concurrency control mechanism is present (no locks, transactions, or atomic operations)

        VIOLATION EXAMPLES:
        - Function with: SomeModel.findBy({id: userId}) followed by someEntity.save()
        - Function with: await getById(id) and later await entity.update({...})
        - Function with: User.findOne({id}) and User.update({id}, {...})

        NON-VIOLATION EXAMPLES:
        - Only query operations with no writes
        - Only write operations with no reads
        - Operations wrapped in database transactions
        - Operations using database-level atomic updates
        - Operations with explicit locking mechanisms
        - Read and write operations on completely unrelated entities
        - If these operations are in a .test file (ignore test files)

        REASON: When a function reads data and then writes based on that read, race conditions can occur if another process modifies the data between the read and write operations. Concurrency control prevents data corruption and ensures consistency.

        IMPORTANT: Look for the combination of read-then-write patterns within the same function scope. Operations must appear to be related or operating on the same data to constitute a violation.

        REPORTING FORMAT: When reporting violations, include:
        - Entity name causing the violation
        - Read operation line range (from line X to line Y)
        - Write operation line range (from line X to line Y)
        - Explanation of why these operations are related

        CRITICAL: Carefully examine code blocks and braces to determine if operations are already protected by ConcurrencyLock or similar mechanisms before reporting violations.
        """)
