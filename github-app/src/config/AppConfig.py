from enum import Enum
import os
from dotenv import load_dotenv
from pathlib import Path
from infisical_sdk import InfisicalSDKClient
import logging

from src.config.LoggingConfig import configure_logging

# override=True for not fetching from cache
load_dotenv(override=True)
configure_logging()
logger = logging.getLogger(__name__)


ENVIRONMENT = os.getenv("ENVIRONMENT")


# Initialize the client
client = InfisicalSDKClient(host="https://app.infisical.com")
client.auth.universal_auth.login(
  client_id=os.getenv("INFISICAL_CLIENT_ID"),
  client_secret=os.getenv("INFISICAL_CLIENT_SECRET"),
)
INFISICAL_PROJECT_ID = os.getenv("INFISICAL_PROJECT_ID")


def get_secret(secret_name: str, default_value: str = "") -> str:
  try:
    if client:
      return client.secrets.get_secret_by_name(
        secret_name=secret_name,
        project_id=INFISICAL_PROJECT_ID,
        environment_slug=ENVIRONMENT,
        secret_path="/",
      ).secretValue
  except Exception as e:
    logger.warning(f"Failed to fetch {secret_name} from secrets: {e}")
    return os.getenv(secret_name, default_value)


DEPLOYMENT_ENVIRONMENT = get_secret("DEPLOYMENT_ENVIRONMENT", "unknown")
POSTHOG_WRITE_KEY = get_secret("POSTHOG_WRITE_KEY")


# Base directory
BASE_DIR = Path(__file__).resolve().parent.parent.parent


# GitHub App configuration
GITHUB_APP_ID = get_secret("GITHUB_APP_ID")
GITHUB_APP_PRIVATE_KEY_PATH = get_secret("GITHUB_APP_PRIVATE_KEY_PATH")
GITHUB_APP_PRIVATE_KEY_PEM = get_secret("GITHUB_APP_PRIVATE_KEY_PEM")
GITHUB_APP_WEBHOOK_SECRET = get_secret("GITHUB_APP_WEBHOOK_SECRET")


# Dependencies Configuration
SCIP_PYTHON_ROOT = os.getenv("SCIP_PYTHON_ROOT") or get_secret("SCIP_PYTHON_ROOT")
SCIP_TYPESCRIPT_ROOT = os.getenv("SCIP_TYPESCRIPT_ROOT") or get_secret(
  "SCIP_TYPESCRIPT_ROOT"
)


# GitHub API
GITHUB_BASE_URL = "https://github.com"
GITHUB_API_BASE_URL = "https://api.github.com"


# Repository storage
CHECKOUTS_PATH = os.getenv("CHECKOUTS_PATH") or "./repos"
REPOS_DIR = Path(CHECKOUTS_PATH).absolute()
REPOS_DIR.mkdir(exist_ok=True)


# Pull Request processing
class PRActions(Enum):
  PROCESS_PR = ["opened"]
  UPDATE_DESC = ["reopened", "synchronize"]
  CLEANUP_PR = ["closed"]


# __init__.py files get loaded before our `src/main.py`, which calls `load_dotenv()`.
# For modules imported directly or indirectly in __init.py__ files,
# if they depend on values defined in this module,
# those values will all be `None` because the dotenv hasn't been loaded yet.
# This happens e.g. with `NotionService`.
def get_our_own_notion_secret_key() -> str:
  return os.getenv("NOTION_SECRET_KEY_OURSELVES")


def get_ecotrove_notion_secret_key() -> str:
  return os.getenv("NOTION_SECRET_KEY_ECOFLOW")
