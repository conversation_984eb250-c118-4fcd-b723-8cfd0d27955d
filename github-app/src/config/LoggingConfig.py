import json
import logging
import os
import sys
from pathlib import Path
from src.config.Middlewares import REQUEST_ID_CTX


class JSONFormatter(logging.Formatter):
  """Structured JSON formatter for AWS CloudWatch"""

  def format(self, record):
    log_entry = {
      "timestamp": self.formatTime(record),
      "level": record.levelname,
      "request_id": getattr(record, "request_id", "no_id"),
      "service": record.name,
      "function": record.funcName,
      "message": record.getMessage(),
      "filename": record.filename,
      "line_number": record.lineno,
    }

    # Add exception info if present
    if record.exc_info:
      log_entry["exception"] = self.formatException(record.exc_info)

    # Add extra fields if present
    if hasattr(record, "extra_fields"):
      log_entry.update(record.extra_fields)

    return json.dumps(log_entry, ensure_ascii=False)


class ColorFormatter(logging.Formatter):
  COLORS = {
    "DEBUG": "\033[94m",  # Blue
    "INFO": "\033[92m",  # Green
    "WARNING": "\033[93m",  # Yellow
    "ERROR": "\033[91m",  # Red
    "CRITICAL": "\033[1;91m",  # Bright Red
  }

  RESET = "\033[0m"  # Reset to default color

  def format(self, record):
    # Get the log message formatted using the base formatter
    log_message = super().format(record)
    # Convert multi-line messages to single line for AWS log searching
    log_message = log_message.replace("\n", " | ").replace("\r", "")

    # Get the color for the log level (default to no color if not found)
    color = self.COLORS.get(record.levelname, self.RESET)

    # Apply the color to the log message
    return f"{color}{log_message}{self.RESET}"


def configure_logging(
  log_level: str = "INFO", log_file: str = None, use_json: bool = None
) -> None:
  """Configure application logging.

  Args:
      log_level: The logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
      log_file: Optional path to log file
      use_json: Use JSON formatting (None=auto-detect from env, True=JSON, False=colored)
  """
  # Set up log level
  numeric_level = getattr(logging, log_level.upper(), None)
  if not isinstance(numeric_level, int):
    raise ValueError(f"Invalid log level: {log_level}")

  # Auto-detect JSON usage if not specified
  if use_json is None:
    use_json = os.getenv("ENVIRONMENT", "development") == "prod"

  # Choose formatter based on environment
  if use_json:
    formatter = JSONFormatter()
  else:
    formatter = ColorFormatter(
      "[gitapp - %(request_id)s] %(levelname)s : %(name)s %(funcName)s() -  %(message)s",
      datefmt="%m/%d %H:%M:%S",
    )

  # Configure handlers
  stream_handler = logging.StreamHandler(sys.stdout)
  stream_handler.addFilter(RequestIdFilter())
  stream_handler.setFormatter(formatter)
  handlers = [stream_handler]

  if log_file:
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    file_handler = logging.FileHandler(log_file)
    file_handler.addFilter(RequestIdFilter())
    file_handler.setFormatter(formatter)
    handlers.append(file_handler)

  # Configure logging
  logging.basicConfig(
    level=numeric_level,
    handlers=handlers,
    force=True,  # Override any existing configuration
  )

  # Set third-party loggers to a higher level to reduce noise
  for logger_name in ["urllib3", "git", "httpx", "httpcore"]:
    logging.getLogger(logger_name).setLevel(logging.WARNING)


# --- Logging a request ID ---
class RequestIdFilter(logging.Filter):
  def filter(self, record):
    record.request_id = REQUEST_ID_CTX.get()
    return True


def log_with_context(
  logger: logging.Logger, level: str, message: str, **context
) -> None:
  """Log with additional structured context for JSON formatting.

  Args:
      logger: The logger instance to use
      level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
      message: The main log message
      **context: Additional context fields for structured logging

  Example:
      log_with_context(
          logger, "ERROR", "RPC failed",
          error_type="AioRpcError",
          status="StatusCode.INTERNAL",
          pr_number="1234",
          repository="runwayml/api"
      )
  """
  # Filter out None values
  extra_fields = {k: v for k, v in context.items() if v is not None}

  # Get the numeric level
  numeric_level = getattr(logging, level.upper())

  # Create a log record with extra fields
  record = logger.makeRecord(logger.name, numeric_level, __file__, 0, message, (), None)
  record.extra_fields = extra_fields
  logger.handle(record)
