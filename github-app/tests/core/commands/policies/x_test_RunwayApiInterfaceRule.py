from src.core.commands.policies.RunwayApiInterfaceRule import RunwayApiInterfaceRule
from tests.GitSet import GitSet
from src.util.utils import set_context
from src.config.TestConfig import test_commands_var
from tests.api.GithubWebhookTest import GithubWebhookTest

apiInterface_ts_content = """
// Custom API interface methods that should NOT be detected by RunwayApiInterfaceRule
// since they don't use Express router

export const get = (path: string, handler: any) => {
  console.log(`GET ${path}`);
  return handler();
};

export const post = (path: string, data: any, handler: any) => {
  console.log(`POST ${path}`, data);
  return handler();
};

export const patch = (path: string, data: any, handler: any) => {
  console.log(`PATCH ${path}`, data);
  return handler();
};

export const delete_ = (path: string, handler: any) => {
  console.log(`DELETE ${path}`);
  return handler();
};

// Example usage - these should NOT be found by the rule
export const setupCustomRoutes = () => {
  get('/custom/users', () => ({ users: [] }));
  
  post('/custom/users', { name: 'John' }, () => ({ created: true }));
  
  patch('/custom/users/1', { name: 'Jane' }, () => ({ updated: true }));
  
  delete_('/custom/users/1', () => ({ deleted: true }));
};
"""

index_ts_content = """
import { Router } from 'express';
import { get, post, patch, delete_ } from './apiInterface';

const router = Router();

router.get('/api/users', (req, res) => {
  res.json({ users: [] });
});

router.post('/api/users', (req, res) => {
  res.json({ created: true });
});

router.put('/api/users/:id', (req, res) => {
  res.json({ updated: true });
});

router.delete('/api/users/:id', (req, res) => {
  res.json({ deleted: true });
});

get('/custom/endpoint', () => {
  return { message: 'Custom GET' };
});

post('/custom/create', { data: 'test' }, () => {
  return { message: 'Custom POST' };
});

patch('/custom/update', { id: 1 }, () => {
  return { message: 'Custom PATCH' };
});

delete_('/custom/remove', () => {
  return { message: 'Custom DELETE' };
});

export default router;
""".strip()

package_json_content = """
{
  "name": "express-example",
  "version": "1.0.0",
  "dependencies": {
    "@types/express": "^4.17.17",
    "@types/express-serve-static-core": "4.17.33",
    "express": "^4.18.2"
  },
  "devDependencies": {
    "typescript": "^5.0.0"
  }
}

""".strip()

tsconfig_content = """
{
  "compilerOptions": {
    "target": "ES5",
    "module": "CommonJS",
    "lib": [ "es2015" ],
    "allowImportingTsExtensions": true,
    "noEmit": true
  } 
}
""".strip()


class TestRunwayApiInterfaceRule(GithubWebhookTest):
  def test_e2e(self):
    with (
      GitSet() as repo,
      set_context(test_commands_var, [RunwayApiInterfaceRule()]),
      repo.on_branch("test"),
    ):
      repo.add_file_with_contents("apiInterface.ts", apiInterface_ts_content)
      repo.add_file_with_contents("index.ts", index_ts_content)
      repo.add_file_with_contents("package.json", package_json_content)
      repo.add_file_with_contents(
        "tsconfig.json", tsconfig_content, commit_message="Setup test"
      )

      payload = repo.github_pull_request_payload()
      response = self.post(github_event="pull_request", payload=payload)
      assert response.status_code == 200
      json = response.json()
      assert json["success"]
      assert json["error"] is None
      results = json["results"]
      assert len(results) == 1
      assert results[0]["command_name"] == "RunwayApiInterfaceRule"
      assert results[0]["response"]["is_violation"]

      comments = results[0]["response"]["inline_diff_comments"]
      assert len(comments) == 4
      assert comments[0]["file_path"] == "index.ts"
      assert comments[0]["one_based_line"] == 6
      assert comments[1]["file_path"] == "index.ts"
      assert comments[1]["one_based_line"] == 10
      assert comments[2]["file_path"] == "index.ts"
      assert comments[2]["one_based_line"] == 14
      assert comments[3]["file_path"] == "index.ts"
      assert comments[3]["one_based_line"] == 18
