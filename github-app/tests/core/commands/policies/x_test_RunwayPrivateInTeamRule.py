import pytest

from src.core.commands.policies.RunwayPrivateInTeamRule import RunwayPrivateInTeamRule
from tests.GitSet import GitSet
from src.util.utils import set_context
from src.config.TestConfig import test_commands_var
from tests.api.GithubWebhookTest import GithubWebhookTest

index_ts_content = """
import { BaseEntity, Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity()
export class TaskArtifact extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: string;

  @Column()
  userId: string;

  @Column()
  privateInTeam: boolean;

  @Column()
  content: string;
}

@Entity()
export class Task extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: string;

  @Column()
  userId: string;

  @Column()
  deleted: boolean;

  @Column()
  status: string;

  @Column()
  internal: boolean;
}

// All four examples here should be flagged
export function testCases() {
  TaskArtifact.findBy({ userId: "user123" });

  TaskArtifact.findOne({ where: { userId: "user123" } });

  TaskArtifact.createQueryBuilder()
    .where("userId = :userId", { userId: "user123" })
    .getOne();

  // Pattern 3: queryBuilder pattern - VIOLATION (complex join with userId but missing privateInTeam)
  const qb2 = TaskArtifact.createQueryBuilder('ta')
    .innerJoin(Task, 't', 't.id = ta.taskId')
    .where('t.userId = :userId', { userId: "user456" })
    .andWhere('t.deleted = :deleted', { deleted: false })
    .andWhere('t.status = :status', { status: 'SUCCEEDED' })
    .andWhere('t.internal = :internal', { internal: false })
    .andWhere('ta.deleted = :deleted', { deleted: false })
    .andWhere('ta.reported = :reported', { reported: false })
    .andWhere('ta.userId = :userId', { userId: "user456" });
}

// Non-TaskArtifact calls should be ignored
class OtherEntity {
  static findBy(criteria: any): any[] { return []; }
  static find(options: any): any[] { return []; }
  static createQueryBuilder(): any { return {}; }
}

export function nonTaskArtifactCalls() {
  // These should all be ignored since they're not TaskArtifact calls
  OtherEntity.findBy({ content: "test" });
  OtherEntity.find({ where: { content: "test" } });
  OtherEntity.createQueryBuilder().where("content = :content").getMany();
}
""".strip()

package_json_content = """
{
  "name": "example-api",
  "version": "1.0.0",
  "dependencies": {
    "typeorm": "0.3.20"
  }
}
""".strip()

tsconfig_content = """
{
  "include": ["*.ts"],
  "compilerOptions": {
    "typeRoots": ["node_modules/@types", "node_modules/typeorm"]
  }
}
""".strip()


class TestRunwayPrivateInTeamRule(GithubWebhookTest):
  @pytest.mark.asyncio
  async def test_e2e(self):
    with (
      GitSet() as repo,
      set_context(test_commands_var, [RunwayPrivateInTeamRule()]),
      repo.on_branch("test"),
    ):
      repo.add_file_with_contents("index.ts", index_ts_content)
      repo.add_file_with_contents("package.json", package_json_content)
      repo.add_file_with_contents(
        "tsconfig.json", tsconfig_content, commit_message="Setup test"
      )

      payload = repo.github_pull_request_payload()
      response = self.post(github_event="pull_request", payload=payload)
      assert response.status_code == 200
      json = response.json()
      assert json["success"]
      assert json["error"] is None
      results = json["results"]
      assert len(results) == 1
      assert results[0]["command_name"] == "RunwayPrivateInTeamRule"
      assert results[0]["response"]["is_violation"]

      comments = results[0]["response"]["inline_diff_comments"]
      assert len(comments) == 4
      lines = [c["one_based_line"] for c in comments]
      lines.sort()
      assert lines == [38, 40, 42, 47]
