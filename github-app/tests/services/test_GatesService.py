from unittest.mock import patch

from src.services.GatesService import _get_gate_value
from src.util.utils import magic_github_app_id_for_pytest


@patch(
  "src.services.GatesService._GATES_BY_INSTALLATION_ID",
  {
    "test_gate": True,
    "test_gate_list": [12345, 67890],
    "test_gate_dict": {
      "1167764": [12345, 67890],  # Prod
      magic_github_app_id_for_pytest(): True,
    },
    "test_gate_dict_list": {
      "1167764": [12345, 67890],  # Prod
      magic_github_app_id_for_pytest(): [12345, 54321],
    },
  },
)
def test_get_gate_value_not_configured():
  # Test gate that doesn't exist
  assert _get_gate_value("non_existent_gate", 12345) is False


@patch(
  "src.services.GatesService._GATES_BY_INSTALLATION_ID",
  {
    "test_gate": True,
    "test_gate_list": [12345, 67890],
    "test_gate_dict": {
      "1167764": [12345, 67890],  # Prod
      magic_github_app_id_for_pytest(): True,
    },
    "test_gate_dict_list": {
      "1167764": [12345, 67890],  # Prod
      magic_github_app_id_for_pytest(): [12345, 54321],
    },
  },
)
def test_get_gate_value_true_for_all():
  # Test gate that's enabled for everyone
  assert _get_gate_value("test_gate", 12345) is True
  assert _get_gate_value("test_gate", 99999) is True


@patch(
  "src.services.GatesService._GATES_BY_INSTALLATION_ID",
  {
    "test_gate": True,
    "test_gate_list": [12345, 67890],
    "test_gate_dict": {
      "1167764": [12345, 67890],  # Prod
      magic_github_app_id_for_pytest(): True,
    },
    "test_gate_dict_list": {
      "1167764": [12345, 67890],  # Prod
      magic_github_app_id_for_pytest(): [12345, 54321],
    },
  },
)
@patch("src.services.GatesService.GITHUB_APP_ID", "1167764")  # Simulate prod
def test_get_gate_value_list_in_prod():
  # Test list gate in prod environment
  assert _get_gate_value("test_gate_list", 12345) is True  # In list
  assert _get_gate_value("test_gate_list", 99999) is False  # Not in list


@patch(
  "src.services.GatesService._GATES_BY_INSTALLATION_ID",
  {
    "test_gate": True,
    "test_gate_list": [12345, 67890],
    "test_gate_dict": {
      "1167764": [12345, 67890],  # Prod
      magic_github_app_id_for_pytest(): True,
    },
    "test_gate_dict_list": {
      "1167764": [12345, 67890],  # Prod
      magic_github_app_id_for_pytest(): [12345, 54321],
    },
  },
)
@patch("src.services.GatesService.GITHUB_APP_ID", "dev_app_id")  # Simulate non-prod
def test_get_gate_value_list_in_non_prod():
  # Test list gate in non-prod environment (should always be False)
  assert _get_gate_value("test_gate_list", 12345) is False
  assert _get_gate_value("test_gate_list", 99999) is False


@patch(
  "src.services.GatesService._GATES_BY_INSTALLATION_ID",
  {
    "test_gate": True,
    "test_gate_list": [12345, 67890],
    "test_gate_dict": {
      "1167764": [12345, 67890],  # Prod
      magic_github_app_id_for_pytest(): True,
    },
    "test_gate_dict_list": {
      "1167764": [12345, 67890],  # Prod
      magic_github_app_id_for_pytest(): [12345, 54321],
    },
  },
)
@patch(
  "src.services.GatesService.GITHUB_APP_ID", magic_github_app_id_for_pytest()
)  # Simulate specific env
def test_get_gate_value_dict_true_for_specific_env():
  # Test dict gate with True for specific environment
  assert _get_gate_value("test_gate_dict", 12345) is True
  assert _get_gate_value("test_gate_dict", 99999) is True


@patch(
  "src.services.GatesService._GATES_BY_INSTALLATION_ID",
  {
    "test_gate": True,
    "test_gate_list": [12345, 67890],
    "test_gate_dict": {
      "1167764": [12345, 67890],  # Prod
      magic_github_app_id_for_pytest(): True,
    },
    "test_gate_dict_list": {
      "1167764": [12345, 67890],  # Prod
      magic_github_app_id_for_pytest(): [12345, 54321],
    },
  },
)
@patch("src.services.GatesService.GITHUB_APP_ID", "1167764")  # Simulate prod
def test_get_gate_value_dict_list_in_prod():
  # Test dict with list for prod environment
  assert _get_gate_value("test_gate_dict", 12345) is True  # In list
  assert _get_gate_value("test_gate_dict", 99999) is False  # Not in list


@patch(
  "src.services.GatesService._GATES_BY_INSTALLATION_ID",
  {
    "test_gate": True,
    "test_gate_list": [12345, 67890],
    "test_gate_dict": {
      "1167764": [12345, 67890],  # Prod
      magic_github_app_id_for_pytest(): True,
    },
    "test_gate_dict_list": {
      "1167764": [12345, 67890],  # Prod
      magic_github_app_id_for_pytest(): [12345, 54321],
    },
  },
)
@patch(
  "src.services.GatesService.GITHUB_APP_ID", magic_github_app_id_for_pytest()
)  # Simulate specific env
def test_get_gate_value_dict_list_in_specific_env():
  # Test dict with list for specific environment
  assert _get_gate_value("test_gate_dict_list", 12345) is True  # In list
  assert _get_gate_value("test_gate_dict_list", 54321) is True  # In list
  assert _get_gate_value("test_gate_dict_list", 99999) is False  # Not in list


@patch(
  "src.services.GatesService._GATES_BY_INSTALLATION_ID",
  {
    "test_gate": True,
    "test_gate_list": [12345, 67890],
    "test_gate_dict": {
      "1167764": [12345, 67890],  # Prod
      magic_github_app_id_for_pytest(): True,
    },
    "test_gate_dict_list": {
      "1167764": [12345, 67890],  # Prod
      magic_github_app_id_for_pytest(): [12345, 54321],
    },
  },
)
@patch(
  "src.services.GatesService.magic_github_app_id_for_pytest", lambda: "unknown_app_id"
)
@patch(
  "src.services.GatesService.GITHUB_APP_ID", "random_non_existent_app_id"
)  # Simulate specific env
def test_get_gate_value_dict_missing_env():
  # Test dict gate with missing environment config
  assert _get_gate_value("test_gate_dict", 12345) is False
  assert _get_gate_value("test_gate_dict", 99999) is False


@patch(
  "src.services.GatesService._GATES_BY_INSTALLATION_ID",
  {"test_gate_invalid": "invalid_value"},
)
def test_get_gate_value_invalid_type():
  # Test invalid gate config type
  assert _get_gate_value("test_gate_invalid", 12345) is False


@patch(
  "src.services.GatesService._GATES_BY_INSTALLATION_ID",
  {"test_gate_dict_invalid": {magic_github_app_id_for_pytest(): "invalid_value"}},
)
@patch(
  "src.services.GatesService.GITHUB_APP_ID", magic_github_app_id_for_pytest()
)  # App ID with invalid value
def test_get_gate_value_dict_invalid_type():
  # Test dict gate with invalid value type
  assert _get_gate_value("test_gate_dict_invalid", 12345) is False
