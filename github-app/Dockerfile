FROM python:3.13.2-slim-bookworm

RUN apt-get update && apt-get install -y \
    build-essential \
    libudev-dev \
    curl \
    tree \
    jq \
    npm \
    && npm install n -g && n 23.6.1 \
    && corepack enable && corepack prepare pnpm@8.15.5 --activate \
    && apt-get clean

RUN pnpm --version
RUN mkdir /src

# Install Python tools (cached)
RUN pip install --upgrade pip setuptools && \
    pip install poetry==1.6.1

# Build scip-python (dependency)
COPY ../scip-python /src/scip-python
WORKDIR /src/scip-python/packages/pyright-scip
RUN npx scip-python --version

# Build scip-typescript (dependency)
COPY ../scip-typescript /src/scip-typescript
WORKDIR /src/scip-typescript
RUN npm install
RUN npm run build
RUN npx scip-typescript --version

# WORKDIR /src/scip-python
# RUN npm install && lerna exec --no-bail npm install --omit=dev
# RUN npm run bootstrap
# RUN cd /src/scip-python/packages/pyright-scip && npm run build


# BUILD github-app
WORKDIR /src/github-app
COPY ../github-app /src/github-app
COPY ../all-protos /src/all-protos

# Configure Poetry to not use a virtual environment
RUN poetry config virtualenvs.create false
RUN poetry install --no-dev

# Expose ports: 9000 for server, 8501 for streamlit
EXPOSE 9000 
EXPOSE 8501

# Copy and set up the startup script
COPY github-app/start-services.sh /src/github-app/
RUN chmod +x /src/github-app/start-services.sh
CMD ["/src/github-app/start-services.sh"]
